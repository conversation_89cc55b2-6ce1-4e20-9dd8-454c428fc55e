# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: 'fr'
    router.request_context.host: '%env(HOST)%'
    router.request_context.scheme: '%env(SCHEME)%'
    render_html: true

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $noReplyAddress: '%env(NO_REPLY_ADDRESS)%'
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    App\Client\MantisApiClient:
        arguments:
            $apiUrl: '%env(MANTIS_URL)%'

    App\Service\ReportingService:
        arguments:
            - '@monolog.http_client'

    App\Authenticator\ApiTokenAuthenticator:
        arguments:
            - '%env(API_TOKEN)%'

    App\Service\WeeklyReportService:
        arguments:
            $defaultRecipientAddress: '%env(WEEKLY_REPORT_DEFAULT_RECIPIENT_ADDRESS)%'
