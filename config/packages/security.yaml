security:
    role_hierarchy:
        ROLE_PROJECT_MANAGER:
        ROLE_ADMIN: [ROLE_GESTION_CONGES, ROLE_PROJECT_MANAGER]
        ROLE_SUPER_ADMIN: ROLE_ADMIN
    encoders:
        App\Entity\User:
            algorithm: bcrypt
    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        in_memory: { memory: ~ }
        in_database:
            entity:
                class: App\Entity\User
                property: username
        oauth:
            id: knpu.oauth2.user_provider
    firewalls:
        login:
            pattern: ^/api/login
            stateless: true
            anonymous: true
            json_login:
                check_path: /api/login_check
                provider: in_database
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
        api_average_time:
            pattern: ^/api/average-time
            stateless: true
            guard:
                provider: in_database
                authenticator:
                    - lexik_jwt_authentication.jwt_token_authenticator
        api_token:
            pattern: ^/api/(users|clients)
            stateless: true
            guard:
                provider: in_database
                authenticators:
                    - App\Authenticator\ApiTokenAuthenticator
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            anonymous: true 
            
            provider: in_database
            
            form_login: 
                login_path: security_login
                check_path: security_login

            logout:
                path: security_logout
                target: security_login

            guard:
                authenticators:
                     - App\Authenticator\GoogleAuthenticator

            # activate different ways to authenticate

            # http_basic: true
            # https://symfony.com/doc/current/security.html#a-configuring-how-your-users-will-authenticate

            # form_login: true
            # https://symfony.com/doc/current/security/form_login_setup.html

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
     
    access_control:
        - { path: ^/api/login, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/api/clients, roles: ROLE_API }
        - { path: ^/api/users, roles: ROLE_API }
        - { path: ^/connexion, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/update_assignments, roles: ROLE_ADMIN }
        - { path: ^/reset-password, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/, roles: [ROLE_USER, ROLE_ADMIN,ROLE_GESTION_CONGES] }
