App\Entity\Task:
    itemOperations: {}
    collectionOperations:
        post_publication:
            method: POST
            path: /task-duration
            controller: App\Controller\ApiController::insert
            denormalization_context:
                groups: ['write']
            defaults:
                _api_receive: false
        average_time:
            method: POST
            path: '/average-time'
            controller: App\Controller\ApiController::average
            swagger_context:
                summary: Average time per ticket
                description: >
                    # Average Time

                    Retrive the average time per ticket sent in the request content.<br />
                    If start and end date is precised, it retrieve the average time per ticket between these two dates.<br />
                    If only start date is precised, it retrieve the average time per ticket between this date and now.<br />
                    Otherwise it raised an error.
                parameters:
                    -
                        in: body
                        name: tickets
                        description: The tickets for which we want to retrieve the average time
                        schema:
                            type: string
                            properties:
                                tickets: {type: string, description: tickets list separated with semicolon}
                                start_date: {type: string, description: The date from which we want to retrieve the tickets}
                                end_date: {type: string, description: The date from which we no longer want to retrieve the tickets}
                        example:
                            tickets: 001;1000;1050
                            start_date: 2019-09-02
                            end_date: 2019-10-02
                responses:
                    200:
                        description: Retrieve average time for the tickets in request body
                        schema:
                            type: application/json
                            properties:
                                total_time:
                                    type: number
                                    example: 523
                                average:
                                    type: number
                                    example: 261.5
                                times:
                                    type: object
                                    properties:
                                        1000:
                                            type: number
                                            example: 236
                                        1050:
                                            type: number
                                            example: 287
                                        001:
                                            type: number
                                            example: 0
                    400:
                        description: Not all required parameters are sent.
                    404:
                        description: No ticket found.
