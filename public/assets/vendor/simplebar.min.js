/**
 * SimpleBar.js - v3.1.1
 * Scrollbars, simpler.
 * https://grsmto.github.io/simplebar/
 * 
 * Made by <PERSON><PERSON> from a fork by <PERSON>
 * Under MIT License
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.SimpleBar=e()}(this,function(){"use strict";var t=function(t){return"object"==typeof t?null!==t:"function"==typeof t},e=function(e){if(!t(e))throw TypeError(e+" is not an object!");return e},i=function(t){try{return!!t()}catch(t){return!0}},r=!i(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function s(t,e){return t(e={exports:{}},e.exports),e.exports}var o=s(function(t){var e=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)}),a=o.document,l=t(a)&&t(a.createElement),c=function(t){return l?a.createElement(t):{}},u=!r&&!i(function(){return 7!=Object.defineProperty(c("div"),"a",{get:function(){return 7}}).a}),h=Object.defineProperty,f={f:r?Object.defineProperty:function(i,r,n){if(e(i),r=function(e,i){if(!t(e))return e;var r,n;if(i&&"function"==typeof(r=e.toString)&&!t(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!t(n=r.call(e)))return n;if(!i&&"function"==typeof(r=e.toString)&&!t(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}(r,!0),e(n),u)try{return h(i,r,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(i[r]=n.value),i}},d=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},p=r?function(t,e,i){return f.f(t,e,d(1,i))}:function(t,e,i){return t[e]=i,t},v={}.hasOwnProperty,y=function(t,e){return v.call(t,e)},b=0,m=Math.random(),g=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++b+m).toString(36))},x=s(function(t){var e=t.exports={version:"2.5.7"};"number"==typeof __e&&(__e=e)}),w=(x.version,s(function(t){var e=g("src"),i=Function.toString,r=(""+i).split("toString");x.inspectSource=function(t){return i.call(t)},(t.exports=function(t,i,n,s){var a="function"==typeof n;a&&(y(n,"name")||p(n,"name",i)),t[i]!==n&&(a&&(y(n,e)||p(n,e,t[i]?""+t[i]:r.join(String(i)))),t===o?t[i]=n:s?t[i]?t[i]=n:p(t,i,n):(delete t[i],p(t,i,n)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[e]||i.call(this)})})),E=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t},_=s(function(t){var e=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(t.exports=function(t,i){return e[t]||(e[t]=void 0!==i?i:{})})("versions",[]).push({version:x.version,mode:"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})}),O=s(function(t){var e=_("wks"),i=o.Symbol,r="function"==typeof i;(t.exports=function(t){return e[t]||(e[t]=r&&i[t]||(r?i:g)("Symbol."+t))}).store=e}),S=function(t,e,r){var n=O(t),s=r(E,n,""[t]),o=s[0],a=s[1];i(function(){var e={};return e[n]=function(){return 7},7!=""[t](e)})&&(w(String.prototype,t,o),p(RegExp.prototype,n,2==e?function(t,e){return a.call(t,this,e)}:function(t){return a.call(t,this)}))};S("replace",2,function(t,e,i){return[function(r,n){var s=t(this),o=void 0==r?void 0:r[e];return void 0!==o?o.call(r,s,n):i.call(String(s),r,n)},i]});var k=f.f,A=Function.prototype,L=/^\s*function ([^ (]*)/;"name"in A||r&&k(A,"name",{configurable:!0,get:function(){try{return(""+this).match(L)[1]}catch(t){return""}}}),S("match",1,function(t,e,i){return[function(i){var r=t(this),n=void 0==i?void 0:i[e];return void 0!==n?n.call(i,r):new RegExp(i)[e](String(r))},i]});var M=O("unscopables"),T=Array.prototype;void 0==T[M]&&p(T,M,{});var j=function(t){T[M][t]=!0},N=function(t,e){return{value:e,done:!!t}},R={},W={}.toString,C=function(t){return W.call(t).slice(8,-1)},z=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==C(t)?t.split(""):Object(t)},D=function(t){return z(E(t))},V=function(t,e,i){if(function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!")}(t),void 0===e)return t;switch(i){case 1:return function(i){return t.call(e,i)};case 2:return function(i,r){return t.call(e,i,r)};case 3:return function(i,r,n){return t.call(e,i,r,n)}}return function(){return t.apply(e,arguments)}},B=function(t,e,i){var r,n,s,a,l=t&B.F,c=t&B.G,u=t&B.S,h=t&B.P,f=t&B.B,d=c?o:u?o[e]||(o[e]={}):(o[e]||{}).prototype,v=c?x:x[e]||(x[e]={}),y=v.prototype||(v.prototype={});for(r in c&&(i=e),i)s=((n=!l&&d&&void 0!==d[r])?d:i)[r],a=f&&n?V(s,o):h&&"function"==typeof s?V(Function.call,s):s,d&&w(d,r,s,t&B.U),v[r]!=s&&p(v,r,a),h&&y[r]!=s&&(y[r]=s)};o.core=x,B.F=1,B.G=2,B.S=4,B.P=8,B.B=16,B.W=32,B.U=64,B.R=128;var P,F=B,H=Math.ceil,q=Math.floor,I=function(t){return isNaN(t=+t)?0:(t>0?q:H)(t)},X=Math.min,Y=function(t){return t>0?X(I(t),9007199254740991):0},G=Math.max,$=Math.min,U=_("keys"),J=function(t){return U[t]||(U[t]=g(t))},K=(P=!1,function(t,e,i){var r,n=D(t),s=Y(n.length),o=function(t,e){return(t=I(t))<0?G(t+e,0):$(t,e)}(i,s);if(P&&e!=e){for(;s>o;)if((r=n[o++])!=r)return!0}else for(;s>o;o++)if((P||o in n)&&n[o]===e)return P||o||0;return!P&&-1}),Q=J("IE_PROTO"),Z="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),tt=Object.keys||function(t){return function(t,e){var i,r=D(t),n=0,s=[];for(i in r)i!=Q&&y(r,i)&&s.push(i);for(;e.length>n;)y(r,i=e[n++])&&(~K(s,i)||s.push(i));return s}(t,Z)},et=r?Object.defineProperties:function(t,i){e(t);for(var r,n=tt(i),s=n.length,o=0;s>o;)f.f(t,r=n[o++],i[r]);return t},it=o.document,rt=it&&it.documentElement,nt=J("IE_PROTO"),st=function(){},ot=function(){var t,e=c("iframe"),i=Z.length;for(e.style.display="none",rt.appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),ot=t.F;i--;)delete ot.prototype[Z[i]];return ot()},at=Object.create||function(t,i){var r;return null!==t?(st.prototype=e(t),r=new st,st.prototype=null,r[nt]=t):r=ot(),void 0===i?r:et(r,i)},lt=f.f,ct=O("toStringTag"),ut=function(t,e,i){t&&!y(t=i?t:t.prototype,ct)&&lt(t,ct,{configurable:!0,value:e})},ht={};p(ht,O("iterator"),function(){return this});var ft=function(t,e,i){t.prototype=at(ht,{next:d(1,i)}),ut(t,e+" Iterator")},dt=function(t){return Object(E(t))},pt=J("IE_PROTO"),vt=Object.prototype,yt=Object.getPrototypeOf||function(t){return t=dt(t),y(t,pt)?t[pt]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?vt:null},bt=O("iterator"),mt=!([].keys&&"next"in[].keys()),gt=function(){return this},xt=function(t,e,i,r,n,s,o){ft(i,e,r);var a,l,c,u=function(t){if(!mt&&t in v)return v[t];switch(t){case"keys":case"values":return function(){return new i(this,t)}}return function(){return new i(this,t)}},h=e+" Iterator",f="values"==n,d=!1,v=t.prototype,y=v[bt]||v["@@iterator"]||n&&v[n],b=y||u(n),m=n?f?u("entries"):b:void 0,g="Array"==e&&v.entries||y;if(g&&(c=yt(g.call(new t)))!==Object.prototype&&c.next&&(ut(c,h,!0),"function"!=typeof c[bt]&&p(c,bt,gt)),f&&y&&"values"!==y.name&&(d=!0,b=function(){return y.call(this)}),(mt||d||!v[bt])&&p(v,bt,b),R[e]=b,R[h]=gt,n)if(a={values:f?b:u("values"),keys:s?b:u("keys"),entries:m},o)for(l in a)l in v||w(v,l,a[l]);else F(F.P+F.F*(mt||d),e,a);return a},wt=xt(Array,"Array",function(t,e){this._t=D(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,i=this._i++;return!t||i>=t.length?(this._t=void 0,N(1)):N(0,"keys"==e?i:"values"==e?t[i]:[i,t[i]])},"values");R.Arguments=R.Array,j("keys"),j("values"),j("entries");for(var Et=O("iterator"),_t=O("toStringTag"),Ot=R.Array,St={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},kt=tt(St),At=0;At<kt.length;At++){var Lt,Mt=kt[At],Tt=St[Mt],jt=o[Mt],Nt=jt&&jt.prototype;if(Nt&&(Nt[Et]||p(Nt,Et,Ot),Nt[_t]||p(Nt,_t,Mt),R[Mt]=Ot,Tt))for(Lt in wt)Nt[Lt]||w(Nt,Lt,wt[Lt],!0)}var Rt,Wt=(Rt=!0,function(t,e){var i,r,n=String(E(t)),s=I(e),o=n.length;return s<0||s>=o?Rt?"":void 0:(i=n.charCodeAt(s))<55296||i>56319||s+1===o||(r=n.charCodeAt(s+1))<56320||r>57343?Rt?n.charAt(s):i:Rt?n.slice(s,s+2):r-56320+(i-55296<<10)+65536});xt(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,i=this._i;return i>=e.length?{value:void 0,done:!0}:(t=Wt(e,i),this._i+=t.length,{value:t,done:!1})});var Ct=function(t,i,r,n){try{return n?i(e(r)[0],r[1]):i(r)}catch(i){var s=t.return;throw void 0!==s&&e(s.call(t)),i}},zt=O("iterator"),Dt=Array.prototype,Vt=function(t,e,i){e in t?f.f(t,e,d(0,i)):t[e]=i},Bt=O("toStringTag"),Pt="Arguments"==C(function(){return arguments}()),Ft=O("iterator"),Ht=x.getIteratorMethod=function(t){if(void 0!=t)return t[Ft]||t["@@iterator"]||R[function(t){var e,i,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),Bt))?i:Pt?C(e):"Object"==(r=C(e))&&"function"==typeof e.callee?"Arguments":r}(t)]},qt=O("iterator"),It=!1;try{[7][qt]().return=function(){It=!0}}catch(t){}function Xt(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Yt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Gt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},r=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),r.forEach(function(e){Yt(t,e,i[e])})}return t}F(F.S+F.F*!function(t,e){if(!e&&!It)return!1;var i=!1;try{var r=[7],n=r[qt]();n.next=function(){return{done:i=!0}},r[qt]=function(){return n},t(r)}catch(t){}return i}(function(t){}),"Array",{from:function(t){var e,i,r,n,s,o=dt(t),a="function"==typeof this?this:Array,l=arguments.length,c=l>1?arguments[1]:void 0,u=void 0!==c,h=0,f=Ht(o);if(u&&(c=V(c,l>2?arguments[2]:void 0,2)),void 0!=f&&(a!=Array||(void 0===(s=f)||R.Array!==s&&Dt[zt]!==s)))for(n=f.call(o),i=new a;!(r=n.next()).done;h++)Vt(i,h,u?Ct(n,c,[r.value,h],!0):r.value);else for(i=new a(e=Y(o.length));e>h;h++)Vt(i,h,u?c(o[h],h):o[h]);return i.length=h,i}});var $t=s(function(t,e){t.exports=function(){if("undefined"==typeof document)return 0;var t,e=document.body,i=document.createElement("div"),r=i.style;return r.position="absolute",r.top=r.left="-9999px",r.width=r.height="100px",r.overflow="scroll",e.appendChild(i),t=i.offsetWidth-i.clientWidth,e.removeChild(i),t}}),Ut="Expected a function",Jt=NaN,Kt="[object Symbol]",Qt=/^\s+|\s+$/g,Zt=/^[-+]0x[0-9a-f]+$/i,te=/^0b[01]+$/i,ee=/^0o[0-7]+$/i,ie=parseInt,re="object"==typeof n&&n&&n.Object===Object&&n,ne="object"==typeof self&&self&&self.Object===Object&&self,se=re||ne||Function("return this")(),oe=Object.prototype.toString,ae=Math.max,le=Math.min,ce=function(){return se.Date.now()};function ue(t,e,i){var r,n,s,o,a,l,c=0,u=!1,h=!1,f=!0;if("function"!=typeof t)throw new TypeError(Ut);function d(e){var i=r,s=n;return r=n=void 0,c=e,o=t.apply(s,i)}function p(t){var i=t-l;return void 0===l||i>=e||i<0||h&&t-c>=s}function v(){var t=ce();if(p(t))return y(t);a=setTimeout(v,function(t){var i=e-(t-l);return h?le(i,s-(t-c)):i}(t))}function y(t){return a=void 0,f&&r?d(t):(r=n=void 0,o)}function b(){var t=ce(),i=p(t);if(r=arguments,n=this,l=t,i){if(void 0===a)return function(t){return c=t,a=setTimeout(v,e),u?d(t):o}(l);if(h)return a=setTimeout(v,e),d(l)}return void 0===a&&(a=setTimeout(v,e)),o}return e=fe(e)||0,he(i)&&(u=!!i.leading,s=(h="maxWait"in i)?ae(fe(i.maxWait)||0,e):s,f="trailing"in i?!!i.trailing:f),b.cancel=function(){void 0!==a&&clearTimeout(a),c=0,r=l=n=a=void 0},b.flush=function(){return void 0===a?o:y(ce())},b}function he(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function fe(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&oe.call(t)==Kt}(t))return Jt;if(he(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=he(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(Qt,"");var i=te.test(t);return i||ee.test(t)?ie(t.slice(2),i?2:8):Zt.test(t)?Jt:+t}var de=function(t,e,i){var r=!0,n=!0;if("function"!=typeof t)throw new TypeError(Ut);return he(i)&&(r="leading"in i?!!i.leading:r,n="trailing"in i?!!i.trailing:n),ue(t,e,{leading:r,maxWait:e,trailing:n})},pe="Expected a function",ve=NaN,ye="[object Symbol]",be=/^\s+|\s+$/g,me=/^[-+]0x[0-9a-f]+$/i,ge=/^0b[01]+$/i,xe=/^0o[0-7]+$/i,we=parseInt,Ee="object"==typeof n&&n&&n.Object===Object&&n,_e="object"==typeof self&&self&&self.Object===Object&&self,Oe=Ee||_e||Function("return this")(),Se=Object.prototype.toString,ke=Math.max,Ae=Math.min,Le=function(){return Oe.Date.now()};function Me(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function Te(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&Se.call(t)==ye}(t))return ve;if(Me(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Me(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(be,"");var i=ge.test(t);return i||xe.test(t)?we(t.slice(2),i?2:8):me.test(t)?ve:+t}var je=function(t,e,i){var r,n,s,o,a,l,c=0,u=!1,h=!1,f=!0;if("function"!=typeof t)throw new TypeError(pe);function d(e){var i=r,s=n;return r=n=void 0,c=e,o=t.apply(s,i)}function p(t){var i=t-l;return void 0===l||i>=e||i<0||h&&t-c>=s}function v(){var t=Le();if(p(t))return y(t);a=setTimeout(v,function(t){var i=e-(t-l);return h?Ae(i,s-(t-c)):i}(t))}function y(t){return a=void 0,f&&r?d(t):(r=n=void 0,o)}function b(){var t=Le(),i=p(t);if(r=arguments,n=this,l=t,i){if(void 0===a)return function(t){return c=t,a=setTimeout(v,e),u?d(t):o}(l);if(h)return a=setTimeout(v,e),d(l)}return void 0===a&&(a=setTimeout(v,e)),o}return e=Te(e)||0,Me(i)&&(u=!!i.leading,s=(h="maxWait"in i)?ke(Te(i.maxWait)||0,e):s,f="trailing"in i?!!i.trailing:f),b.cancel=function(){void 0!==a&&clearTimeout(a),c=0,r=l=n=a=void 0},b.flush=function(){return void 0===a?o:y(Le())},b},Ne="Expected a function",Re="__lodash_hash_undefined__",We="[object Function]",Ce="[object GeneratorFunction]",ze=/^\[object .+?Constructor\]$/,De="object"==typeof n&&n&&n.Object===Object&&n,Ve="object"==typeof self&&self&&self.Object===Object&&self,Be=De||Ve||Function("return this")();var Pe,Fe=Array.prototype,He=Function.prototype,qe=Object.prototype,Ie=Be["__core-js_shared__"],Xe=(Pe=/[^.]+$/.exec(Ie&&Ie.keys&&Ie.keys.IE_PROTO||""))?"Symbol(src)_1."+Pe:"",Ye=He.toString,Ge=qe.hasOwnProperty,$e=qe.toString,Ue=RegExp("^"+Ye.call(Ge).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Je=Fe.splice,Ke=si(Be,"Map"),Qe=si(Object,"create");function Ze(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function ti(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function ei(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function ii(t,e){for(var i,r,n=t.length;n--;)if((i=t[n][0])===(r=e)||i!=i&&r!=r)return n;return-1}function ri(t){return!(!ai(t)||Xe&&Xe in t)&&(function(t){var e=ai(t)?$e.call(t):"";return e==We||e==Ce}(t)||function(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}(t)?Ue:ze).test(function(t){if(null!=t){try{return Ye.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(t))}function ni(t,e){var i,r,n=t.__data__;return("string"==(r=typeof(i=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==i:null===i)?n["string"==typeof e?"string":"hash"]:n.map}function si(t,e){var i=function(t,e){return null==t?void 0:t[e]}(t,e);return ri(i)?i:void 0}function oi(t,e){if("function"!=typeof t||e&&"function"!=typeof e)throw new TypeError(Ne);var i=function(){var r=arguments,n=e?e.apply(this,r):r[0],s=i.cache;if(s.has(n))return s.get(n);var o=t.apply(this,r);return i.cache=s.set(n,o),o};return i.cache=new(oi.Cache||ei),i}function ai(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}Ze.prototype.clear=function(){this.__data__=Qe?Qe(null):{}},Ze.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},Ze.prototype.get=function(t){var e=this.__data__;if(Qe){var i=e[t];return i===Re?void 0:i}return Ge.call(e,t)?e[t]:void 0},Ze.prototype.has=function(t){var e=this.__data__;return Qe?void 0!==e[t]:Ge.call(e,t)},Ze.prototype.set=function(t,e){return this.__data__[t]=Qe&&void 0===e?Re:e,this},ti.prototype.clear=function(){this.__data__=[]},ti.prototype.delete=function(t){var e=this.__data__,i=ii(e,t);return!(i<0||(i==e.length-1?e.pop():Je.call(e,i,1),0))},ti.prototype.get=function(t){var e=this.__data__,i=ii(e,t);return i<0?void 0:e[i][1]},ti.prototype.has=function(t){return ii(this.__data__,t)>-1},ti.prototype.set=function(t,e){var i=this.__data__,r=ii(i,t);return r<0?i.push([t,e]):i[r][1]=e,this},ei.prototype.clear=function(){this.__data__={hash:new Ze,map:new(Ke||ti),string:new Ze}},ei.prototype.delete=function(t){return ni(this,t).delete(t)},ei.prototype.get=function(t){return ni(this,t).get(t)},ei.prototype.has=function(t){return ni(this,t).has(t)},ei.prototype.set=function(t,e){return ni(this,t).set(t,e),this},oi.Cache=ei;var li=oi,ci=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var i=-1;return t.some(function(t,r){return t[0]===e&&(i=r,!0)}),i}return function(){function e(){this.__entries__=[]}var i={size:{configurable:!0}};return i.size.get=function(){return this.__entries__.length},e.prototype.get=function(e){var i=t(this.__entries__,e),r=this.__entries__[i];return r&&r[1]},e.prototype.set=function(e,i){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=i:this.__entries__.push([e,i])},e.prototype.delete=function(e){var i=this.__entries__,r=t(i,e);~r&&i.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var i=0,r=this.__entries__;i<r.length;i+=1){var n=r[i];t.call(e,n[1],n[0])}},Object.defineProperties(e.prototype,i),e}()}(),ui="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,hi="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),fi="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(hi):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)},di=2,pi=["top","right","bottom","left","width","height","size","weight"],vi="undefined"!=typeof MutationObserver,yi=function(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t,e){var i=!1,r=!1,n=0;function s(){i&&(i=!1,t()),r&&a()}function o(){fi(s)}function a(){var t=Date.now();if(i){if(t-n<di)return;r=!0}else i=!0,r=!1,setTimeout(o,e);n=t}return a}(this.refresh.bind(this),20)};yi.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},yi.prototype.removeObserver=function(t){var e=this.observers_,i=e.indexOf(t);~i&&e.splice(i,1),!e.length&&this.connected_&&this.disconnect_()},yi.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},yi.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),t.length>0},yi.prototype.connect_=function(){ui&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),vi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},yi.prototype.disconnect_=function(){ui&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},yi.prototype.onTransitionEnd_=function(t){var e=t.propertyName;void 0===e&&(e=""),pi.some(function(t){return!!~e.indexOf(t)})&&this.refresh()},yi.getInstance=function(){return this.instance_||(this.instance_=new yi),this.instance_},yi.instance_=null;var bi=function(t,e){for(var i=0,r=Object.keys(e);i<r.length;i+=1){var n=r[i];Object.defineProperty(t,n,{value:e[n],enumerable:!1,writable:!1,configurable:!0})}return t},mi=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||hi},gi=Si(0,0,0,0);function xi(t){return parseFloat(t)||0}function wi(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];return e.reduce(function(e,i){return e+xi(t["border-"+i+"-width"])},0)}function Ei(t){var e=t.clientWidth,i=t.clientHeight;if(!e&&!i)return gi;var r=mi(t).getComputedStyle(t),n=function(t){for(var e={},i=0,r=["top","right","bottom","left"];i<r.length;i+=1){var n=r[i],s=t["padding-"+n];e[n]=xi(s)}return e}(r),s=n.left+n.right,o=n.top+n.bottom,a=xi(r.width),l=xi(r.height);if("border-box"===r.boxSizing&&(Math.round(a+s)!==e&&(a-=wi(r,"left","right")+s),Math.round(l+o)!==i&&(l-=wi(r,"top","bottom")+o)),!function(t){return t===mi(t).document.documentElement}(t)){var c=Math.round(a+s)-e,u=Math.round(l+o)-i;1!==Math.abs(c)&&(a-=c),1!==Math.abs(u)&&(l-=u)}return Si(n.left,n.top,a,l)}var _i="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof mi(t).SVGGraphicsElement}:function(t){return t instanceof mi(t).SVGElement&&"function"==typeof t.getBBox};function Oi(t){return ui?_i(t)?function(t){var e=t.getBBox();return Si(0,0,e.width,e.height)}(t):Ei(t):gi}function Si(t,e,i,r){return{x:t,y:e,width:i,height:r}}var ki=function(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Si(0,0,0,0),this.target=t};ki.prototype.isActive=function(){var t=Oi(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},ki.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t};var Ai=function(t,e){var i,r,n,s,o,a,l,c=(r=(i=e).x,n=i.y,s=i.width,o=i.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,l=Object.create(a.prototype),bi(l,{x:r,y:n,width:s,height:o,top:n,right:r+s,bottom:o+n,left:r}),l);bi(this,{target:t,contentRect:c})},Li=function(t,e,i){if(this.activeObservations_=[],this.observations_=new ci,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=i};Li.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof mi(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new ki(t)),this.controller_.addObserver(this),this.controller_.refresh())}},Li.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof mi(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},Li.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},Li.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},Li.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new Ai(t.target,t.broadcastRect())});this.callback_.call(t,e,t),this.clearActive()}},Li.prototype.clearActive=function(){this.activeObservations_.splice(0)},Li.prototype.hasActive=function(){return this.activeObservations_.length>0};var Mi="undefined"!=typeof WeakMap?new WeakMap:new ci,Ti=function(t){if(!(this instanceof Ti))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var e=yi.getInstance(),i=new Li(t,e,this);Mi.set(this,i)};["observe","unobserve","disconnect"].forEach(function(t){Ti.prototype[t]=function(){return(e=Mi.get(this))[t].apply(e,arguments);var e}});var ji=void 0!==hi.ResizeObserver?hi.ResizeObserver:Ti,Ni=!("undefined"==typeof window||!window.document||!window.document.createElement),Ri=function(){function t(e,i){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.onScroll=function(){r.scrollXTicking||(window.requestAnimationFrame(r.scrollX),r.scrollXTicking=!0),r.scrollYTicking||(window.requestAnimationFrame(r.scrollY),r.scrollYTicking=!0)},this.scrollX=function(){r.axis.x.isOverflowing&&(r.showScrollbar("x"),r.positionScrollbar("x")),r.scrollXTicking=!1},this.scrollY=function(){r.axis.y.isOverflowing&&(r.showScrollbar("y"),r.positionScrollbar("y")),r.scrollYTicking=!1},this.onMouseEnter=function(){r.showScrollbar("x"),r.showScrollbar("y")},this.onMouseMove=function(t){r.mouseX=t.clientX,r.mouseY=t.clientY,(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&r.onMouseMoveForAxis("x"),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&r.onMouseMoveForAxis("y")},this.onMouseLeave=function(){r.onMouseMove.cancel(),(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&r.onMouseLeaveForAxis("x"),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&r.onMouseLeaveForAxis("y"),r.mouseX=-1,r.mouseY=-1},this.onWindowResize=function(){r.scrollbarWidth=$t(),r.hideNativeScrollbar()},this.hideScrollbars=function(){r.axis.x.track.rect=r.axis.x.track.el.getBoundingClientRect(),r.axis.y.track.rect=r.axis.y.track.el.getBoundingClientRect(),r.isWithinBounds(r.axis.y.track.rect)||(r.axis.y.scrollbar.el.classList.remove(r.classNames.visible),r.axis.y.isVisible=!1),r.isWithinBounds(r.axis.x.track.rect)||(r.axis.x.scrollbar.el.classList.remove(r.classNames.visible),r.axis.x.isVisible=!1)},this.onPointerEvent=function(t){var e,i;r.axis.x.scrollbar.rect=r.axis.x.scrollbar.el.getBoundingClientRect(),r.axis.y.scrollbar.rect=r.axis.y.scrollbar.el.getBoundingClientRect(),(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&(i=r.isWithinBounds(r.axis.x.scrollbar.rect)),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&(e=r.isWithinBounds(r.axis.y.scrollbar.rect)),(e||i)&&(t.preventDefault(),t.stopPropagation(),"mousedown"===t.type&&(e&&r.onDragStart(t,"y"),i&&r.onDragStart(t,"x")))},this.drag=function(e){var i=r.axis[r.draggedAxis].track,n=i.rect[r.axis[r.draggedAxis].sizeAttr],s=r.axis[r.draggedAxis].scrollbar;e.preventDefault(),e.stopPropagation();var o=(("y"===r.draggedAxis?e.pageY:e.pageX)-i.rect[r.axis[r.draggedAxis].offsetAttr]-r.axis[r.draggedAxis].dragOffset)/i.rect[r.axis[r.draggedAxis].sizeAttr]*r.contentEl[r.axis[r.draggedAxis].scrollSizeAttr];"x"===r.draggedAxis&&(o=r.isRtl&&t.getRtlHelpers().isRtlScrollbarInverted?o-(n+s.size):o,o=r.isRtl&&t.getRtlHelpers().isRtlScrollingInverted?-o:o),r.contentEl[r.axis[r.draggedAxis].scrollOffsetAttr]=o},this.onEndDrag=function(t){t.preventDefault(),t.stopPropagation(),document.removeEventListener("mousemove",r.drag),document.removeEventListener("mouseup",r.onEndDrag)},this.el=e,this.flashTimeout,this.contentEl,this.offsetEl,this.maskEl,this.globalObserver,this.mutationObserver,this.resizeObserver,this.scrollbarWidth,this.minScrollbarWidth=20,this.options=Gt({},t.defaultOptions,i),this.classNames=Gt({},t.defaultOptions.classNames,this.options.classNames),this.isRtl,this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}}},this.recalculate=de(this.recalculate.bind(this),64),this.onMouseMove=de(this.onMouseMove.bind(this),64),this.hideScrollbars=je(this.hideScrollbars.bind(this),this.options.timeout),this.onWindowResize=je(this.onWindowResize.bind(this),64,{leading:!0}),t.getRtlHelpers=li(t.getRtlHelpers),this.getContentElement=this.getScrollElement,this.init()}var e,i,r;return e=t,r=[{key:"getRtlHelpers",value:function(){var e=document.createElement("div");e.innerHTML='<div class="hs-dummy-scrollbar-size"><div style="height: 200%; width: 200%; margin: 10px 0;"></div></div>';var i=e.firstElementChild;document.body.appendChild(i);var r=i.firstElementChild;i.scrollLeft=0;var n=t.getOffset(i),s=t.getOffset(r);i.scrollLeft=999;var o=t.getOffset(r);return{isRtlScrollingInverted:n.left!==s.left&&s.left-o.left!=0,isRtlScrollbarInverted:n.left!==s.left}}},{key:"initHtmlApi",value:function(){this.initDOMLoadedElements=this.initDOMLoadedElements.bind(this),"undefined"!=typeof MutationObserver&&(this.globalObserver=new MutationObserver(function(e){e.forEach(function(e){Array.from(e.addedNodes).forEach(function(e){1===e.nodeType&&(e.hasAttribute("data-simplebar")?!e.SimpleBar&&new t(e,t.getElOptions(e)):Array.from(e.querySelectorAll("[data-simplebar]")).forEach(function(e){!e.SimpleBar&&new t(e,t.getElOptions(e))}))}),Array.from(e.removedNodes).forEach(function(t){1===t.nodeType&&(t.hasAttribute("data-simplebar")?t.SimpleBar&&t.SimpleBar.unMount():Array.from(t.querySelectorAll("[data-simplebar]")).forEach(function(t){t.SimpleBar&&t.SimpleBar.unMount()}))})})}),this.globalObserver.observe(document,{childList:!0,subtree:!0})),"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?window.setTimeout(this.initDOMLoadedElements):(document.addEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.addEventListener("load",this.initDOMLoadedElements))}},{key:"getElOptions",value:function(t){return Array.from(t.attributes).reduce(function(t,e){var i=e.name.match(/data-simplebar-(.+)/);if(i){var r=i[1].replace(/\W+(.)/g,function(t,e){return e.toUpperCase()});switch(e.value){case"true":t[r]=!0;break;case"false":t[r]=!1;break;case void 0:t[r]=!0;break;default:t[r]=e.value}}return t},{})}},{key:"removeObserver",value:function(){this.globalObserver.disconnect()}},{key:"initDOMLoadedElements",value:function(){document.removeEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.removeEventListener("load",this.initDOMLoadedElements),Array.from(document.querySelectorAll("[data-simplebar]")).forEach(function(e){e.SimpleBar||new t(e,t.getElOptions(e))})}},{key:"getOffset",value:function(t){var e=t.getBoundingClientRect();return{top:e.top+(window.pageYOffset||document.documentElement.scrollTop),left:e.left+(window.pageXOffset||document.documentElement.scrollLeft)}}}],(i=[{key:"init",value:function(){this.el.SimpleBar=this,this.initDOM(),Ni&&(this.scrollbarWidth=$t(),this.recalculate(),this.initListeners())}},{key:"initDOM",value:function(){var t=this;if(Array.from(this.el.children).filter(function(e){return e.classList.contains(t.classNames.wrapper)}).length)this.wrapperEl=this.el.querySelector(".".concat(this.classNames.wrapper)),this.contentEl=this.el.querySelector(".".concat(this.classNames.content)),this.offsetEl=this.el.querySelector(".".concat(this.classNames.offset)),this.maskEl=this.el.querySelector(".".concat(this.classNames.mask)),this.placeholderEl=this.el.querySelector(".".concat(this.classNames.placeholder)),this.heightAutoObserverWrapperEl=this.el.querySelector(".".concat(this.classNames.heightAutoObserverWrapperEl)),this.heightAutoObserverEl=this.el.querySelector(".".concat(this.classNames.heightAutoObserverEl)),this.axis.x.track.el=this.el.querySelector(".".concat(this.classNames.track,".").concat(this.classNames.horizontal)),this.axis.y.track.el=this.el.querySelector(".".concat(this.classNames.track,".").concat(this.classNames.vertical));else{for(this.wrapperEl=document.createElement("div"),this.contentEl=document.createElement("div"),this.offsetEl=document.createElement("div"),this.maskEl=document.createElement("div"),this.placeholderEl=document.createElement("div"),this.heightAutoObserverWrapperEl=document.createElement("div"),this.heightAutoObserverEl=document.createElement("div"),this.wrapperEl.classList.add(this.classNames.wrapper),this.contentEl.classList.add(this.classNames.content),this.offsetEl.classList.add(this.classNames.offset),this.maskEl.classList.add(this.classNames.mask),this.placeholderEl.classList.add(this.classNames.placeholder),this.heightAutoObserverWrapperEl.classList.add(this.classNames.heightAutoObserverWrapperEl),this.heightAutoObserverEl.classList.add(this.classNames.heightAutoObserverEl);this.el.firstChild;)this.contentEl.appendChild(this.el.firstChild);this.offsetEl.appendChild(this.contentEl),this.maskEl.appendChild(this.offsetEl),this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl),this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl),this.wrapperEl.appendChild(this.maskEl),this.wrapperEl.appendChild(this.placeholderEl),this.el.appendChild(this.wrapperEl)}if(!this.axis.x.track.el||!this.axis.y.track.el){var e=document.createElement("div"),i=document.createElement("div");e.classList.add(this.classNames.track),i.classList.add(this.classNames.scrollbar),this.options.autoHide||i.classList.add(this.classNames.visible),e.appendChild(i),this.axis.x.track.el=e.cloneNode(!0),this.axis.x.track.el.classList.add(this.classNames.horizontal),this.axis.y.track.el=e.cloneNode(!0),this.axis.y.track.el.classList.add(this.classNames.vertical),this.el.appendChild(this.axis.x.track.el),this.el.appendChild(this.axis.y.track.el)}this.axis.x.scrollbar.el=this.axis.x.track.el.querySelector(".".concat(this.classNames.scrollbar)),this.axis.y.scrollbar.el=this.axis.y.track.el.querySelector(".".concat(this.classNames.scrollbar)),this.el.setAttribute("data-simplebar","init")}},{key:"initListeners",value:function(){var t=this;this.options.autoHide&&this.el.addEventListener("mouseenter",this.onMouseEnter),["mousedown","click","dblclick","touchstart","touchend","touchmove"].forEach(function(e){t.el.addEventListener(e,t.onPointerEvent,!0)}),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),this.contentEl.addEventListener("scroll",this.onScroll),window.addEventListener("resize",this.onWindowResize),"undefined"!=typeof MutationObserver&&(this.mutationObserver=new MutationObserver(function(e){e.forEach(function(e){e.target!==t.el&&t.isChildNode(e.target)&&!e.addedNodes.length||t.recalculate()})}),this.mutationObserver.observe(this.el,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this.resizeObserver=new ji(this.recalculate),this.resizeObserver.observe(this.el)}},{key:"recalculate",value:function(){var t=this.heightAutoObserverEl.offsetHeight<=1;this.elStyles=window.getComputedStyle(this.el),this.isRtl="rtl"===this.elStyles.direction,this.contentEl.style.padding="".concat(this.elStyles.paddingTop," ").concat(this.elStyles.paddingRight," ").concat(this.elStyles.paddingBottom," ").concat(this.elStyles.paddingLeft),this.contentEl.style.height=t?"auto":"100%",this.placeholderEl.style.width="".concat(this.contentEl.scrollWidth,"px"),this.placeholderEl.style.height="".concat(this.contentEl.scrollHeight,"px"),this.wrapperEl.style.margin="-".concat(this.elStyles.paddingTop," -").concat(this.elStyles.paddingRight," -").concat(this.elStyles.paddingBottom," -").concat(this.elStyles.paddingLeft),this.axis.x.track.rect=this.axis.x.track.el.getBoundingClientRect(),this.axis.y.track.rect=this.axis.y.track.el.getBoundingClientRect(),this.axis.x.isOverflowing=(this.scrollbarWidth?this.contentEl.scrollWidth:this.contentEl.scrollWidth-this.minScrollbarWidth)>Math.ceil(this.axis.x.track.rect.width),this.axis.y.isOverflowing=(this.scrollbarWidth?this.contentEl.scrollHeight:this.contentEl.scrollHeight-this.minScrollbarWidth)>Math.ceil(this.axis.y.track.rect.height),this.axis.x.isOverflowing="hidden"!==this.elStyles.overflowX&&this.axis.x.isOverflowing,this.axis.y.isOverflowing="hidden"!==this.elStyles.overflowY&&this.axis.y.isOverflowing,this.axis.x.forceVisible="x"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.y.forceVisible="y"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el.style.width="".concat(this.axis.x.scrollbar.size,"px"),this.axis.y.scrollbar.el.style.height="".concat(this.axis.y.scrollbar.size,"px"),this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y"),this.hideNativeScrollbar()}},{key:"getScrollbarSize",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y",i=this.scrollbarWidth?this.contentEl[this.axis[e].scrollSizeAttr]:this.contentEl[this.axis[e].scrollSizeAttr]-this.minScrollbarWidth,r=this.axis[e].track.rect[this.axis[e].sizeAttr];if(this.axis[e].isOverflowing){var n=r/i;return t=Math.max(~~(n*r),this.options.scrollbarMinSize),this.options.scrollbarMaxSize&&(t=Math.min(t,this.options.scrollbarMaxSize)),t}}},{key:"positionScrollbar",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y",i=this.contentEl[this.axis[e].scrollSizeAttr],r=this.axis[e].track.rect[this.axis[e].sizeAttr],n=parseInt(this.elStyles[this.axis[e].sizeAttr],10),s=this.axis[e].scrollbar,o=this.contentEl[this.axis[e].scrollOffsetAttr],a=(o="x"===e&&this.isRtl&&t.getRtlHelpers().isRtlScrollingInverted?-o:o)/(i-n),l=~~((r-s.size)*a);l="x"===e&&this.isRtl&&t.getRtlHelpers().isRtlScrollbarInverted?l+(r-s.size):l,s.el.style.transform="x"===e?"translate3d(".concat(l,"px, 0, 0)"):"translate3d(0, ".concat(l,"px, 0)")}},{key:"toggleTrackVisibility",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y",e=this.axis[t].track.el,i=this.axis[t].scrollbar.el;this.axis[t].isOverflowing||this.axis[t].forceVisible?(e.style.visibility="visible",this.contentEl.style[this.axis[t].overflowAttr]="scroll"):(e.style.visibility="hidden",this.contentEl.style[this.axis[t].overflowAttr]="hidden"),this.axis[t].isOverflowing?i.style.visibility="visible":i.style.visibility="hidden"}},{key:"hideNativeScrollbar",value:function(){if(this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-".concat(this.scrollbarWidth||this.minScrollbarWidth,"px"):0,this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-".concat(this.scrollbarWidth||this.minScrollbarWidth,"px"):0,!this.scrollbarWidth){var t=[this.isRtl?"paddingLeft":"paddingRight"];this.contentEl.style[t]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"calc(".concat(this.elStyles[t]," + ").concat(this.minScrollbarWidth,"px)"):this.elStyles[t],this.contentEl.style.paddingBottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"calc(".concat(this.elStyles.paddingBottom," + ").concat(this.minScrollbarWidth,"px)"):this.elStyles.paddingBottom}}},{key:"onMouseMoveForAxis",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y";this.axis[t].track.rect=this.axis[t].track.el.getBoundingClientRect(),this.axis[t].scrollbar.rect=this.axis[t].scrollbar.el.getBoundingClientRect(),this.isWithinBounds(this.axis[t].scrollbar.rect)?this.axis[t].scrollbar.el.classList.add(this.classNames.hover):this.axis[t].scrollbar.el.classList.remove(this.classNames.hover),this.isWithinBounds(this.axis[t].track.rect)?(this.showScrollbar(t),this.axis[t].track.el.classList.add(this.classNames.hover)):this.axis[t].track.el.classList.remove(this.classNames.hover)}},{key:"onMouseLeaveForAxis",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y";this.axis[t].track.el.classList.remove(this.classNames.hover),this.axis[t].scrollbar.el.classList.remove(this.classNames.hover)}},{key:"showScrollbar",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"y",e=this.axis[t].scrollbar.el;this.axis[t].isVisible||(e.classList.add(this.classNames.visible),this.axis[t].isVisible=!0),this.options.autoHide&&this.hideScrollbars()}},{key:"onDragStart",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",i=this.axis[e].scrollbar.el,r="y"===e?t.pageY:t.pageX;this.axis[e].dragOffset=r-i.getBoundingClientRect()[this.axis[e].offsetAttr],this.draggedAxis=e,document.addEventListener("mousemove",this.drag),document.addEventListener("mouseup",this.onEndDrag)}},{key:"getScrollElement",value:function(){return this.contentEl}},{key:"removeListeners",value:function(){this.options.autoHide&&this.el.removeEventListener("mouseenter",this.onMouseEnter),this.contentEl.removeEventListener("scroll",this.onScroll),window.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver.disconnect()}},{key:"unMount",value:function(){this.removeListeners(),this.el.SimpleBar=null}},{key:"isChildNode",value:function(t){return null!==t&&(t===this.el||this.isChildNode(t.parentNode))}},{key:"isWithinBounds",value:function(t){return this.mouseX>=t.left&&this.mouseX<=t.left+t.width&&this.mouseY>=t.top&&this.mouseY<=t.top+t.height}}])&&Xt(e.prototype,i),r&&Xt(e,r),t}();return Ri.defaultOptions={autoHide:!0,forceVisible:!1,classNames:{content:"simplebar-content",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover"},scrollbarMinSize:25,scrollbarMaxSize:0,timeout:1e3},Ni&&Ri.initHtmlApi(),Ri});
