{"type": "project", "license": "proprietary", "require": {"php": "^7.2", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "api-platform/core": "^2.4", "components/jquery": "^3.3", "composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^1.11", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^2.7", "fzaninotto/faker": "^1.8", "google/apiclient": "^2.14", "knpuniversity/oauth2-client-bundle": "^1.28", "league/oauth2-google": "^3.0", "lexik/jwt-authentication-bundle": "^2.6", "nelmio/cors-bundle": "^1.5", "phpdocumentor/reflection-docblock": "^5.2", "phpoffice/phpspreadsheet": "^1.12", "sensio/framework-extra-bundle": "^5.1", "symfony/asset": "4.2.*", "symfony/console": "4.2.*", "symfony/dotenv": "4.2.*", "symfony/expression-language": "4.2.*", "symfony/flex": "^1.1", "symfony/form": "4.2.*", "symfony/framework-bundle": "4.2.*", "symfony/http-client": "^4.2", "symfony/monolog-bundle": "^3.1", "symfony/phpunit-bridge": "^4.3", "symfony/process": "4.2.*", "symfony/property-access": "4.2.*", "symfony/property-info": "4.2.*", "symfony/security-bundle": "4.2.*", "symfony/serializer": "4.2.*", "symfony/swiftmailer-bundle": "^3.1", "symfony/translation": "4.2.*", "symfony/twig-bundle": "4.2.*", "symfony/validator": "4.2.*", "symfony/web-link": "4.2.*", "symfony/webpack-encore-bundle": "^1.2", "symfony/yaml": "4.2.*", "twbs/bootstrap": "4.0.0"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.1", "liip/test-fixtures-bundle": "^1.0.0", "phpunit/phpunit": "^8.5", "symfony/browser-kit": "4.2.*", "symfony/css-selector": "4.2.*", "symfony/debug-bundle": "4.2.*", "symfony/maker-bundle": "^1.0", "symfony/stopwatch": "4.2.*", "symfony/var-dumper": "4.2.*", "symfony/web-profiler-bundle": "4.2.*", "symfony/web-server-bundle": "4.2.*"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "4.2.*"}}}