<table class="table" {% if cumulativeSearch|default(false)  %} style="background-color: #e5e5e5" {% endif %}>
    <thead>
    <tr>
        {% if app.request.query.get('idProject') is defined and app.request.query.get('idProject') != null %}
            <th>
                <input type="button" id="check-all" value="Tout selectionner"/>
            </th>
        {% endif %}
        <th>Sujet</th>
        <th>Utilisateur</th>
        <th>Date</th>
        <th>Référence ticket</th>
        <th>Durée</th>
        <th>Type Intervention</th>
        <th>Client</th>
        <th>Projet</th>
        <th></th>
    </tr>
    </thead>
    <tbody>
    {% for task in tasks %}
        <tr id="results-filters">
            {% if app.request.query.get('idProject') is defined and app.request.query.get('idProject') != null %}
                <td><input class="pilotage" type="checkbox" name="tasks[]" value="{{ task.id }}"/></td>
            {% endif %}
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.subject | replace({'|':'<br/>'}) | raw }}
            </td>
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.user_firstname }}</td>
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.date|date('Y-m-d')|default('') }}</td>
            {% if task.refMantis matches '/^\\d+$/' %}
                <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'" style="cursor: pointer">
                    <a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}">
                        {{ task.refMantis }}</a>
                </td>
            {% else %}
                <td>{{ task.refMantis }}</td>
            {% endif %}
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.duration }}</td>
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.typeinter_name }}</td>
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.client_name }}</td>
            <td class="result" onclick="location.href='{{ path('task_show', { id: task.id }) }}'"
                style="cursor: pointer">{{ task.project_name }}</td>
            <td class="result">
                <a href="{{ path('task_edit', { id: task.id }) }}"><em class="icon icon-pencil"></em></a>
            </td>
        </tr>
    {% else %}
        <tr>
            <td colspan="8" id="no-record">no records found</td>
        </tr>
    {% endfor %}
    </tbody>
</table>