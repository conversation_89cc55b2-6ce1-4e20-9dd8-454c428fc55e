<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <title>Suivi des temps</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="{{ template_path }}/recherche/exportpdf/css/template.css">
</head>
<body>
<div id="container">
    <section id="memo">
        <div class="logo">
            <img src="{{ absolute_url(asset('build/logo_pdf.png')) }}"
                 data-logo="company_logo"/>
        </div>

        <div class="company-info">
            <div>IT-Room</div>

            <br/>

            <span>5 Allée Gabert</span>
            <span>59510 Hem</span>

            <br/>

            <span>03 20 82 82 45</span>
            <span>www.itroom.fr</span>
        </div>

    </section>

    <section id="invoice-title-number">

        <span id="title">Suivi des temps {{ client }}</span>

    </section>

    <div class="clearfix"></div>

    <section id="client-info">
        <div><span><b>Période :</b> {{ date_debut }} - {{ date_fin }}</span></div>
    </section>

    <div class="clearfix"></div>

    <section id="items">

        <table cellpadding="0" cellspacing="0">

            <tr>
                <th>Date</th> <!-- Dummy cell for the row number and row commands -->
                <th>Durée</th>
                <th>Intervenant</th>
                <th>Type</th>
                <th>Sujet</th>
                <th>Ref Ticket</th>
            </tr>

            {% for task in tasks %}
                <tr>
                    <td>
                        {% if cumulativeSearch %} {{ task.date}} {% else %} {{ task.date|date('d/m/Y') }} {% endif %}
                    </td>
                    <td>{{ task.duration }}</td>
                    <td>
                        {% if cumulativeSearch %} {{ task.users}} {% else %}{{ task.user_firstname }} {{ task.user_name }}{% endif %}

                    </td>
                    <td>{{ task.typeinter_name }}</td>
                    <td>{{ task.subject }}</td>
                    <td>{{ task.refMantis }}</td>
                </tr>
            {% endfor %}
        </table>

    </section>

    <section id="sum1" class="sums">
        <table cellpadding="0" cellspacing="0">
            <tr id="total1" data-hide-on-quote="true">
                <th>Temps total (heures)</th>
                <td>{{ total_heures }} heures</td>
            </tr>
        </table>
        <div class="clearfix"></div>
    </section>

    <section id="sum2" class="sums">
        <table cellpadding="0" cellspacing="0">
            <tr id="total2" data-hide-on-quote="true">
                <th>Temps total (jours)</th>
                <td>{{ total_jours }} jours</td>
            </tr>
        </table>

        <div class="clearfix"></div>
    </section>
    <div class="clearfix"></div>

</div>

</body>
</html>
