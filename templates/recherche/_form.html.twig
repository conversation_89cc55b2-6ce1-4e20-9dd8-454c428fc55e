{% import "_tpl/macro.html.twig" as mac %}

{{ form_start(form, {'attr': {'id': 'search_filters','action': app.request.pathInfo }}) }}

{% set searchChildren = ['refMantis', 'user', 'client', 'typeInter' , 'project' , 'dateD' , 'dateF', 'company', 'groupedBy'] %}

<div class="row">
    {% for key in form.children|keys %}
        {% if key in searchChildren %}
            <div class="col-sm-6 col-md-4 col-lg-3">
                {{ form_row(form[key]) }}
            </div>
        {% endif %}
    {% endfor %}
</div>

{{ form_row(form) }}
<div>
    {{ form_widget(form.search) }}
    {{ form_widget(form.print) }}
    {{ form_widget(form.export) }}
</div>
{{ form_end(form) }}