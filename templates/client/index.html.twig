{% extends 'base.html.twig' %}  
{% block body %}
<div class="{{ _col_table }}">
  <div class="title-form">
    <h1>{% block title %}Client index{% endblock %}</h1>
    {% if is_granted('ROLE_ADMIN') %}
    <div class="table-form">
        <a class="btn btn-outline-success btn mb-2 " href="{{ path('client_new') }}">Nouveau client</a>
      {% endif %}
    </div>
  </div>
  <table id="datatables" class="table">
    <thead>
      <tr>
        <th>Id</th>
        <th>Actif</th>
        <th>Nom</th>
        <th>Code Dolibarr</th>
        <th>actions</th>
      </tr>
    </thead>
    <tbody>
      {% for client in clients %}
      <tr>
        <td>{{ client.id }}</td>
        <td>{{ client.actif }}</td>
        <td>{{ client.name }}</td>
        <td>{{ client.codeDolibarr }}</td>
        <td>
          <a
            href="{{ path('client_show', { id: client.id }) }}"
            ><em class="icon icon-eye"></em></a
          >

          {% if is_granted('ROLE_ADMIN') %}
          <a
            href="{{ path('client_edit', { id: client.id }) }}"
            ><em class="icon icon-pencil"></em></a
          >
          {% endif %}
        </td>
      </tr>
      {% else %}
      <tr>
        <td colspan="4">no records found</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  
</div>
{% endblock %}