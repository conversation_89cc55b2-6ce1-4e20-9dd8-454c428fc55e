{% extends 'base.html.twig' %}
{% block page_class %}page-projects{% endblock %}
{% block body %}
    {% if project.chargeVendue > 0 %}
        {% set pourcentage_avancement = statitics.charge_consommee / project.chargeVendue * 100 %}
    {% endif %}
    <div class="col-12">
    <div>
        <a href="{{ path('project_index') }}" class="btn-back-top"><em class="icon icon-arrow-left-circle"></em></a>
    </div>
    <div class="row project-title-container">
        <h1 class="col-12 col-md-auto">{% block title %}Projet {{ project.name }} {% endblock %}</h1>
        <div class="col">
            <a class="btn btn-outline-secondary" href="{{ path('project_edit', { id: project.id }) }}">Modifier
                le projet</a>
        </div>
    </div>
    <div class="card-deck mb-3">
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">P<PERSON>riode</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">
                    {{ statitics.task_bounds.first_task_date|date('d/m/Y')|default('~') }} -
                    {{ statitics.task_bounds.last_task_date|date('d/m/Y')|default('~') }}</h5>
            </div>
        </div>
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Charge vendue (jours)</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">{{ project.chargeVendue|number_format(2,'.',' ') }}</h5>
            </div>
        </div>
        {% if pourcentage_avancement is not defined %}
            {% set class_pourcentage = '' %}
        {% elseif pourcentage_avancement <= constant('App\\Entity\\Project::SEUIL_POURCENTAGE_CONSOMME') %}
            {% set class_pourcentage = 'text-success' %}
        {% elseif pourcentage_avancement <= 100 %}
            {% set class_pourcentage = 'text-warning' %}
        {% else %}
            {% set class_pourcentage = 'text-danger' %}
        {% endif %}
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Charge consommée (jours)</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold {{ class_pourcentage }}">{{ statitics.charge_consommee|number_format(2,'.',' ') }}</h5>
            </div>
        </div>
    </div>
    <div  class="card-deck mb-3">
        {% set chargeDevBack = statitics.charge_tags[constant('App\\Entity\\User::TAG_BACK')] ?? 0 %}
        {% set chargeDevFront = statitics.charge_tags[constant('App\\Entity\\User::TAG_FRONT')] ?? 0 %}
        {% set chargeDesign = statitics.charge_tags[constant('App\\Entity\\User::TAG_DESIGN')] ?? 0 %}
        {% set chargeProjectManager = statitics.charge_tags[constant('App\\Entity\\User::TAG_MANAGER')] ?? 0 %}
        {% set chargeOther = statitics.charge_consommee - (chargeDevBack + chargeDevFront + chargeDesign + chargeProjectManager) %}
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Développement</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">
                    Back : {{ chargeDevBack|number_format(2,'.',' ') }} jours
                </h5>
                <h5 class="card-title font-weight-bold">
                    Front : {{ chargeDevFront|number_format(2,'.',' ') }} jours
                </h5>

            </div>
        </div>
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Design</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">
                    {{ chargeDesign|number_format(2,'.',' ') }} jours
                </h5>
            </div>
        </div>
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Pilotage</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">
                    {{ chargeProjectManager|number_format(2,'.',' ') }} jours
                </h5>
            </div>
        </div>
        <div class="card bg-light mb-3  text-center font-weight-bold">
            <div class="card-header">Utilisateurs sans tag</div>
            <div class="card-body">
                <h5 class="card-title font-weight-bold">
                    {{ chargeOther|number_format(2,'.',' ') }}  jours
                </h5>
            </div>
        </div>
    </div>
    {% if statitics.clients|length > 1 %}
        <div>
            <h2>Charge consommée par client</h2>
            <table class="table">
                <thead>
                <th style="width: 85%;">Client</th>
                <th style="text-align: center;">Charge consommée (en jours)</th>
                </thead>
                <tbody>
                {% for statistic in statitics.clients %}
                    <tr>
                        <td>{{ statistic.name }}</td>
                        <td style="text-align: center;">{{ statistic.total|number_format(2, '.', ' ') }}</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
    {% if statitics.charge_consommee_by_user|length > 0 %}
        <div>
            <form method="post" action="{{ path('change_project', {id: project.id } ) }}">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-little">
                            <thead>
                            <th>
                                <input type="button" id="check-all" value="Tout selectionner"/>
                            </th>
                            <th>Collaborateur</th>
                            <th>Tag</th>
                            <th style="text-align: center;">Charge consommée (en jours)</th>
                            </thead>
                            <tbody>
                            {% for statistic in statitics.charge_consommee_by_user %}
                                <tr style="cursor: pointer">
                                    <td><input class="pilotage" type="checkbox" name="ids[]"
                                               value="{{ statistic.id }}"/></td>
                                    <td onclick="location.href='{{ path('recherche', { idProject: project.id, idUser: statistic.id }) }}'">{{ statistic.name }}</td>
                                    <td onclick="location.href='{{ path('recherche', { idProject: project.id, idUser: statistic.id }) }}'">{{ statistic.tag }}</td>
                                    <td style="text-align: center;"
                                        onclick="location.href='{{ path('recherche', { idProject: project.id, idUser: statistic.id }) }}'">{{ statistic.total|number_format(2, '.', ' ') }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-4" style="display: none;" id="affectation-projet">
                        <div class="row">
                            {{ form_row(projectForm, {'label':false}) }}
                        </div>

                        <div class="form-group">
                            <input class="btn btn-outline-primary mb-2" type="submit" value="Modifier l'affectation"/>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}

    {% if interventionsByType|length > 0 %}
        <div>
            <form method="post" action="{{ path('change_project', {id: project.id } ) }}">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-little">
                            <thead>
                                <th>Type</th>
                                <th style="text-align: center;">Charge vendue</th>
                                <th style="text-align: center;">Charge consommée</th>
                                <th style="text-align: center;">Pourcentage consommé</th>
                            </thead>
                            <tbody>
                                {% for intervention in interventionsByType %}
                                    <tr style="cursor: pointer">
                                        <td>{{ intervention.type }}</td>
                                        <td style="text-align: center;">
                                            {% if intervention.type == 'Projet - Développement (front et back)' %}
                                                {{ project.chargeTech | number_format(2, '.', ' ') }}
                                            {% elseif intervention.type == 'Projet - Pilotage & coordination' %}
                                                {{ project.pilotage | number_format(2, '.', ' ') }}
                                            {% elseif intervention.type == 'Projet - Recette & Garantie' %}
                                                {{ project.recette | number_format(2, '.', ' ') }}
                                            {% elseif intervention.type == 'Projet - Conception technique et fonctionnelle détaillée' %}
                                                {{ project.conception | number_format(2, '.', ' ') }}
                                            {% elseif intervention.type == 'Projet - Migration & Mise en production' %}
                                                {{ project.mep | number_format(2, '.', ' ') }}
                                            {% endif %}
                                        </td>
                                        <td style="text-align: center;">{{ intervention.duration|number_format(2, '.', ' ') }}</td>
                                        <td style="text-align: center;">
                                            {% if intervention.pourcentageConsomme is not null %}
                                                {{ intervention.pourcentageConsomme|number_format(2, '.', ' ') }}%                                                
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}


    <table class="table table-little">
        <thead>
        <tr>
            <th colspan="2">Collaborateur autorisé</th>
        </tr>
        </thead>
        <tbody>
        {% for userAuthorized in userAuthorizeds %}
        <tr>
            <td onclick="location.href='{{ path('user_show', { id: userAuthorized.id }) }}'">{{ userAuthorized.name }} {{ userAuthorized.firstName }}</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>

    <div>
        <div class="table-form d-flex">
            <a href="{{ path('project_index') }}"><em
                        class="icon icon-arrow-left-circle"></em></a>
            {% if project.tasks.values == [] %}
                {{ include("project/_delete_form.html.twig") }}
            {% endif %}
        </div>
    </div>
{% endblock %}