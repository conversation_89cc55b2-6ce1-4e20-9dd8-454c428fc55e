{% extends 'base.html.twig' %}
{% block page_class %}page-projects{% endblock %}
{% block body %}
  <div class="{{ _col_table }}">
    <div class="bloc-menu">
      <div class="title-form">
        <h1>{% block title %}Liste des projets{% endblock %}</h1>
        <div class="table-form">
          {% if is_granted(constant('App\\Entity\\User::ROLE_SUPER_ADMIN')) %}
            <a class="btn btn-outline-primary btn mb-2 " href="{{ path('project_list_allow_user') }}">Exporter les utilisateurs autorisés par projet</a>
          {% endif %}
          <a class="btn btn-outline-success btn mb-2 " href="{{ path('project_new') }}">Nouveau projet</a>
        </div>
      </div>
      <div class="firstbloc">
        <div>
          <b class="black">Etat :</b>
          {% include "project/_filter.twig" with {'filterName': 'Tout', 'state': constant('App\\Entity\\Project::STATE_ALL'), 'type': filter.type, 'isEnabled': (constant('App\\Entity\\Project::STATE_ALL') is same as filter.state)} %}
          {% include "project/_filter.twig" with {'filterName': 'Actif', 'state': constant('App\\Entity\\Project::STATE_ACTIVE'), 'type': filter.type, 'isEnabled': (constant('App\\Entity\\Project::STATE_ACTIVE') is same as filter.state)} %}
          {% include "project/_filter.twig" with {'filterName': 'Inactif', 'state': constant('App\\Entity\\Project::STATE_INACTIVE'), 'type': filter.type, 'isEnabled': (constant('App\\Entity\\Project::STATE_INACTIVE') is same as filter.state)} %}
          {% include "project/_filter.twig" with {'filterName': 'Standby', 'state': constant('App\\Entity\\Project::STATE_STANDBY'), 'type': filter.type, 'isEnabled': (constant('App\\Entity\\Project::STATE_STANDBY') is same as filter.state)} %}
        </div>
        <div class="margintop10">
          <b class="black">Projet :</b>
          {% include "project/_filter.twig" with {'filterName': 'Tout', 'state': filter.state, 'type': constant('App\\Entity\\Company::ALL'), 'isEnabled': (constant('App\\Entity\\Company::ALL') is same as filter.type)} %}
          {% include "project/_filter.twig" with {'filterName': 'IT-Room', 'state': filter.state, 'type': constant('App\\Entity\\Company::IT_ROOM'), 'isEnabled': (constant('App\\Entity\\Company::IT_ROOM') is same as filter.type)} %}
          {% include "project/_filter.twig" with {'filterName': 'IT-Ref', 'state': filter.state, 'type': constant('App\\Entity\\Company::IT_REF'), 'isEnabled': (constant('App\\Entity\\Company::IT_REF') is same as filter.type)} %}
        </div>
      </div>
    </div>
    <br/>
    <table id="datatables" class="table" style="max-width: 1100px">
      <thead>
      <tr>
        <th>Client</th>
        <th>Nom projet</th>
        <th>Chef de projet</th>
        <th>Id</th>
        <th>Actif</th>
        <th width="15 px">Réf dolibarr</th>
        <th>Charge vendue</th>
        <th>Charge conso</th>
        <th width="15 px">% Conso</th>
        <th width="15 px">Avt projet</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      {% for project in projects %}
        {% if project.pourcentageConsomme is empty %}
          {% set class_pourcentage = '' %}
        {% elseif project.pourcentageConsomme <= constant('App\\Entity\\Project::SEUIL_POURCENTAGE_CONSOMME') %}
          {% set class_pourcentage = 'text-success' %}
        {% elseif project.pourcentageConsomme <= 100 %}
          {% set class_pourcentage = 'text-warning' %}
        {% else %}
          {% set class_pourcentage = 'text-danger' %}
        {% endif %}
        <tr onclick="location.href='{{ path('project_show', { id: project.id }) }}'" style="cursor: pointer">
          <td>{{ project.client_name }}</td>
          <td>{{ project.name }}</td>
          <td>{{ project.manager_first_name }} {{ project.manager_name }}</td>
          <td>{{ project.id }}</td>
          <td>
            {% if project.actif == 1 %}
              <i class="icon icon-control-play" style="color:green"></i>
            {% elseif project.actif == 2 %}
              <i class="icon icon-control-pause" style="color: orange"></i>
            {% else %}
              <i class="icon icon-close" style="color:red"></i>
            {% endif %}
          </td>
          {% if project.reference != null %}
            <td>{{ project.reference }}</td>
          {% else %}
            <td class="tdNull"></td>
          {% endif %}
          <td>{{ project.chargeVendue|number_format(2, '.', ' ') }}</td>
          <td>{{ project.chargeConsommee|number_format(2, '.', ' ') }}</td>
          <td class="{{ class_pourcentage }}">{% if project.chargeVendue is not empty %}{{ project.pourcentageConsomme|number_format(0, '.', ' ') }}%{% endif %}</td>
          <td>{% if project.avancement != null %}{{ project.avancement|number_format(0, '.', ' ') }}%{% endif %}</td>
          <td>
            <a
                    href="{{ path('project_edit', { id: project.id }) }}"
            ><em class="icon icon-pencil"></em></a
            >
          </td>
        </tr>
      {% else %}
        <tr>
          <td colspan="3">Aucun enregistrement trouvé</td>
        </tr>
      {% endfor %}
      </tbody>
    </table>

  </div>
{% endblock %}
