<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>{% block title %}Welcome!{% endblock %}</title>

    <!-- Prevent the demo from appearing in search engines -->
    <meta name="robots" content="noindex">

    <!-- Simplebar -->
    <link type="text/css" href="{{ asset('assets/vendor/simplebar.min.css') }}" rel="stylesheet">

    <!-- App CSS -->
    <link type="text/css" href="{{ asset('assets/css/app.css') }}" rel="stylesheet">

    <!-- Material Design Icons -->
    <link type="text/css" href="{{ asset('assets/css/vendor-material-icons.css') }}" rel="stylesheet">

    <!-- Font Awesome FREE Icons -->
    <link type="text/css" href="{{ asset('assets/css/vendor-fontawesome-free.css') }}" rel="stylesheet">

    <!-- Dragula -->
    <link type="text/css" href="{{ asset('assets/vendor/dragula/dragula.min.css') }}" rel="stylesheet">

    <link type="text/css" href="{{ asset('css/trello.base.css') }}" rel="stylesheet">

    {% block stylesheets %}{% endblock %}
</head>

<body class="layout-mini trello">

    <div class="preloader"></div>

    <!-- Header Layout -->
    <div class="mdk-header-layout js-mdk-header-layout">

    <!-- Header -->

        <div id="header" class="mdk-header js-mdk-header m-0" data-fixed>
            <div class="mdk-header__content">

                <div class="navbar navbar-expand-sm navbar-main navbar-dark bg-dark  pr-0" id="navbar" data-primary>
                    <div class="container-fluid p-0">

                        <!-- Navbar Brand -->
                        <a href="mini-dashboard.html" class="navbar-brand ">
                            <img class="navbar-brand-icon" src="{{ asset('build/logo_sdt.png') }}" width="22" alt="IT-Room - Saisie des temps">
                            <span>IT-Room - Saisie des temps</span>
                        </a>


                        <div class="navbar-collapse collapse flex">
                            <ul class="nav navbar-nav">
                                <li class="nav-item dropdown">
                                    <a href="{{ path('home') }}" class="nav-link">Home</a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a href="{{ path('app_dashboard_mantis') }}" class="nav-link">Dashboards</a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Saisie des temps</a>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item " href="{{ path('home') }}">Lancer un chrono</a>
                                        <a class="dropdown-item" href="{{ path('task_new') }}">Nouvelle intervention</a>
                                        <a class="dropdown-item" href="{{ path('task_index') }}">Liste des interventions</a>
                                        <a class="dropdown-item" href="{{ path('calendar') }}">Calendrier</a>
                                        <a class="dropdown-item" href="{{ path('recherche') }}">Recherche</a>
                                        <a class="dropdown-item" href="{{ path('reporting_time_check') }}">Vérification des temps</a>
                                    </div>
                                </li>
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Congés</a>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="{{ path('conge_index') }}">Mes congés</a>
                                        <a class="dropdown-item" href="{{ path('conge_dashboard') }}">Tableau de bord</a>
                                        <a class="dropdown-item" href="{{ path('conge_calendrier') }}">Calendrier</a>
                                        {% if is_granted('ROLE_GESTION_CONGES') %}
                                            <a class="dropdown-item" href="{{ path('conge_validation') }}">Validation</a>
                                        {% endif %}
                                    </div>
                                </li>
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Configuration</a>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="{{ path('client_actif_index') }}">Clients</a>
                                        <a class="dropdown-item" href="{{ path('user_actif_index') }}">Projets</a>
                                        <a class="dropdown-item" href="{{ path('type_inter_actif_index') }}">Utilisateurs</a>
                                        <a class="dropdown-item" href="{{ path('project_actif_index') }}">Type d'intervention</a>
                                    </div>
                                </li>
                            </ul>
                        </div>


                        <!--<ul class="nav navbar-nav ml-auto d-none d-md-flex">
                            <li class="nav-item">
                                <a href="" class="nav-link">
                                    <i class="material-icons">help_outline</i> Get Help
                                </a>
                            </li>
                        </ul>-->

                        <ul class="nav navbar-nav d-none d-sm-flex border-left navbar-height align-items-center">
                            <li class="nav-item dropdown">
                                <a href="#account_menu" class="nav-link dropdown-toggle" data-toggle="dropdown" data-caret="false">
                                    <span class="ml-1 d-flex-inline">
                                        <span class="text-light">{{ app.user }}</span>
                                    </span>
                                </a>
                                <div id="account_menu" class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item" href="{{ path('user_edit', { id: app.user.id }) }}">Mon profil</a>
                                    <a class="dropdown-item" href="{{ path('security_logout') }}">Déconnexion</a>
                                </div>
                            </li>
                        </ul>

                    </div>
                </div>

            </div>
        </div>

        <!-- Header Layout Content -->
        <div class="mdk-header-layout__content">

            <div class="mdk-drawer-layout js-mdk-drawer-layout" data-push data-responsive-width="992px">
                <div class="mdk-drawer-layout__content page">

                    {% block body %}{% endblock %}
                </div>
                <!-- // END drawer-layout__content -->


            </div>
            <!-- // END drawer-layout -->

        </div>
        <!-- // END header-layout__content -->

    </div>
    <!-- // END header-layout -->

    {% block modals %}{% endblock %}

<!-- jQuery -->
<script src="{{ asset('assets/vendor/jquery.min.js') }}"></script>

<!-- Bootstrap -->
<script src="{{ asset('assets/vendor/popper.min.js') }}"></script>
<script src="{{ asset('assets/vendor/bootstrap.min.js') }}"></script>

<!-- Simplebar -->
<script src="{{ asset('assets/vendor/simplebar.min.js') }}"></script>

<!-- DOM Factory -->
<script src="{{ asset('assets/vendor/dom-factory.js') }}"></script>

<!-- MDK -->
<script src="{{ asset('assets/vendor/material-design-kit.js') }}"></script>

<!-- App -->
<script src="{{ asset('assets/js/toggle-check-all.js') }}"></script>
<script src="{{ asset('assets/js/check-selected-row.js') }}"></script>
<script src="{{ asset('assets/js/dropdown.js') }}"></script>
<script src="{{ asset('assets/js/sidebar-mini.js') }}"></script>
<script src="{{ asset('assets/js/app.js') }}"></script>

<!-- Dragula -->
<script src="{{ asset('assets/vendor/dragula/dragula.min.js') }}"></script>
<script src="{{ asset('assets/js/dragula.js') }}"></script>

<script>
    (function() {
        'use strict';

        // ENABLE sidebar menu tabs
        $('#sidebar-mini-tabs [data-toggle="tab"]').on('click', function(e) {
            e.preventDefault()
            $(this).tab('show')
        })
    })()
</script>

{% block javascripts %}{% endblock %}
</body>

</html>