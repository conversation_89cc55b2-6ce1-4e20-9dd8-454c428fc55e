{% extends 'base.html.twig' %}
{% block page_class %}page-user{% endblock %}
{% block body %}
<div class="{{ _col_table }}">
  <div class="title-form">
    <h1>{% block title %}Liste des utilisateurs{% endblock %}</h1>
    <div class="first-bloc">
      <div>
        <b class="black">Activité :</b>
        {% include "user/_filter.html.twig" with {'filterName': 'Tout', 'activity': constant('App\\Entity\\User::FILTER_ACTIVE_ALL'), 'company': filter.company, 'filter': constant('App\\Entity\\User::FILTER_ACTIVE_ALL'), 'isEnabled': (constant('App\\Entity\\User::FILTER_ACTIVE_ALL') is same as filter.activity)} %}
        {% include "user/_filter.html.twig" with {'filterName': 'Actif', 'activity': constant('App\\Entity\\User::FILTER_ACTIVE'), 'company': filter.company, 'filter': constant('App\\Entity\\User::FILTER_ACTIVE'), 'isEnabled': (constant('App\\Entity\\User::FILTER_ACTIVE') is same as filter.activity)} %}
        {% include "user/_filter.html.twig" with {'filterName': 'Inactif', 'activity': constant('App\\Entity\\User::FILTER_INACTIVE'), 'company': filter.company, 'filter': constant('App\\Entity\\User::FILTER_INACTIVE'), 'isEnabled': (constant('App\\Entity\\User::FILTER_INACTIVE') is same as filter.activity)} %}
      </div>
      <div>
        <b class="black">Entité :</b>
        {% include "user/_filter.html.twig" with {'filterName': 'Tout', 'activity': filter.activity, 'company': constant('App\\Entity\\User::COMPANY_ALL'), 'filter': constant('App\\Entity\\User::COMPANY_ALL'), 'isEnabled': (constant('App\\Entity\\User::COMPANY_ALL') is same as filter.company)} %}
        {% for company in companies %}
          {% include "user/_filter.html.twig" with {'filterName': company.name, 'activity': filter.activity, 'company': company.name, 'filter': company.name, 'isEnabled': (company.name is same as filter.company)} %}
        {% endfor %}

      </div>
    </div>
    <div class="table-form">
      {% if is_granted(constant('App\\Entity\\User::ROLE_SUPER_ADMIN')) %}
        <a class="btn btn-outline-primary btn mb-2 " href="{{ path('user_actif_list') }}">Exporter les utilisateurs actifs</a>
        <a class="btn btn-outline-primary btn mb-2 " href="{{ path('user_actif_list', {'active': 0}) }}">Exporter les utilisateurs inactifs</a>
      {% endif %}
      {% if is_granted(constant('App\\Entity\\User::ROLE_ADMIN')) %}
        <a class="btn btn-outline-success btn mb-2 " href="{{ path('user_new') }}">Nouvel utilisateur</a>
      {% endif %}
    </div>
  </div>
  <table class="table">
    <thead>
      <tr>
        <th>Id</th>
        <th>Actif</th>
        <th>Mail</th>
        <th>Username</th>
        <th>Nom</th>
        <th>FirstName</th>
        <th>Rôle</th>
        <th>Entité</th>
        <th>actions</th>
      </tr>
    </thead>
    <tbody>
      {% for user in users %}
      <tr>
        <td>{{ user.id }}</td>
        <td>{{ user.actif|replace({'1': 'oui'}) }}</td>
        <td>{{ user.mail }}</td>
        <td>{{ user.username }}</td>
        <td>{{ user.name }}</td>
        <td>{{ user.firstName }}</td>
        <td>{{ user.highestRole }}</td>
        <td>{{ user.company }}</td>
        <td>
          <a href="{{ path('user_show', { id: user.id }) }}">
            <em class="icon icon-eye"></em>
          </a>
          {% if is_granted('ROLE_ADMIN') %}
            <a href="{{ path('user_edit', { id: user.id }) }}">
              <em class="icon icon-pencil"></em>
            </a>
          {% endif %}
        </td>
      </tr>
      {% else %}
      <tr>
        <td colspan="6">no records found</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  
</div>
{% endblock %}
