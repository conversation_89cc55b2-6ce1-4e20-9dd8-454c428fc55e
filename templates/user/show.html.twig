{% extends 'base.html.twig' %} {% block body
%}
<div class="{{ _col_table }}">
  <h1>{% block title %}Utilisateur{% endblock %}</h1>
  <table class="table">
    <tbody>
      <tr>
        <th>Id</th>
        <td>{{ user.id }}</td>
      </tr>
      <tr>
        <th>Mail</th>
        <td>{{ user.mail }}</td>
      </tr>
      <tr>
        <th>Nom</th>
        <td>{{ user.name }}</td>
      </tr>
      <tr>
        <th>FirstName</th>
        <td>{{ user.firstName }}</td>
      </tr>
      <tr>
        <th>Entité</th>
        <td>{{ user.company }}</td>
      </tr>
      <tr>
        <th>Matricule</th>
        <td>{{ user.registrationNumber }}</td>
      </tr>
      <tr>
        <th>Password</th>
        <td>{{ user.password }}</td>
      </tr>
      <tr>
        <th>En régie</th>
        <td>{{ not user.onSite ? 'Oui':'Non'}}</td>
      </tr>
    </tbody>
  </table>
  <table class="table table-little">
    <thead>
    <tr>
      <th colspan="2">Projet autorisé</th>
    </tr>
    </thead>
    <tbody>
    {% for project in user.userProjects %}
      <tr>
        <td onclick="location.href='{{ path('project_show', { id: project.id }) }}'">{{ project.name }}</td>
      </tr>
    {% endfor %}
    </tbody>
  </table>
  <div class="table-form">
    {% if is_granted('ROLE_ADMIN') or user.id is same as(app.user.id) %}
      <a class="btn btn-outline-secondary mb-2" href="{{ path('user_edit', { id: user.id }) }}">Modifier</a>
      {{ include("user/_delete_form.html.twig") }}
    {% endif %}
    <a href="{{ path('user_index') }}"><em class="icon icon-arrow-left-circle"></em></a>
  </div>
</div>
{% endblock %}
