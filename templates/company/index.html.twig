{% extends 'base.html.twig' %}
{% block body %}
    <div class="{{ _col_table }}">
        <div class="title-form">
            <h1>{% block title %}Liste des Entités{% endblock %}</h1>
            {% if is_granted('ROLE_ADMIN') %}
                <div class="table-form">
                    <a class="btn btn-outline-success btn mb-2 " href="{{ path('company_new') }}">Nouvelle entité</a>
                </div>
            {% endif %}
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>Id</th>
                <th>Nom</th>
                <th>Accessibilité projet</th>
                <th>Accessibilité utilisateur</th>
                {% if is_granted('ROLE_ADMIN') %}
                    <th>Action</th>
                {% endif %}
            </tr>
            </thead>
            <tbody>
            {% for company in companies %}
                <tr>
                    <td>{{ company.id }}</td>
                    <td>{{ company.name }}</td>
                    <td class="allow{{ company.projectEnabled ? '' : '-not'}}">
                        {{ company.projectEnabled ? 'oui' : 'non'}}
                    </td>
                    <td class="allow{{ company.userEnabled ? '' : '-not'}}">
                        {{ company.userEnabled ? 'oui' : 'non'}}
                    </td>
                    {% if is_granted('ROLE_ADMIN') %}
                        <td>
                            <a href="{{ path('company_edit', { id: company.id }) }}">
                                <em class="icon icon-pencil"></em>
                            </a>
                        </td>
                    {% endif %}
                </tr>
            {% else %}
                <tr>
                    <td colspan="3">Aucune donnée trouvée</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}
