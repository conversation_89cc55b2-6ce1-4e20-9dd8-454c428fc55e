{% extends 'base.html.twig' %}

{% block title %}Suivi TMA{% endblock %}
{% block id_page %}suivi-tma{% endblock %}

{% block body %}

    <h1>Suivi des TMA</h1>

    <table class="table table-striped">
        <thead>
        <th></th>
        {% for month in suivi_tma.intervals %}
            <th>{{ month.label }}</th>
        {% endfor %}
        </thead>
        <tbody>
        {%  for key, detail in suivi_tma.details %}
            <tr>
                <td class="project-name"><a  target="_blank" href="{{ path('project_edit', {'id': detail.id}) }}">{{ key }}</a></td>
                {% for month in suivi_tma.intervals %}
                    <td>
                    {% if detail.months[month.date] is defined %}
                        {{ detail.months[month.date]|number_format(2) }}
                    {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <tr>
                <td></td>
                {% for year,quarter in suivi_tma.quarters %}
                    {% for quarter_id, quarter_lenth in quarter %}
                        <td class="{{ detail.quarters[year][quarter_id]['ratio-color']|default('') }}" style="text-align: center" colspan="{{ quarter_lenth }}">
                            {% if detail.quarters[year][quarter_id] is defined %}

                                {{  detail.quarters[year][quarter_id].value|number_format(2) }} {% if detail.chargeVendue is not null %} ({{  detail.quarters[year][quarter_id].ratio|number_format(2) }}%) {% endif %}
                            {% endif %}
                        </td>
                    {% endfor %}
                {% endfor %}
            </tr>
        {% endfor %}
        </tbody>
    </table>

{% endblock %}
