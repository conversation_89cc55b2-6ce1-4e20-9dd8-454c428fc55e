{% extends 'base.html.twig' %}  
{% block body %}
<div class="{{ _col_table }}">
  <div class="title-form">
    <h1>{% block title %}Liste des types d'intervention{% endblock %}</h1>
    <div class="table-form">
        <a class="btn btn-outline-success btn mb-2 " href="{{ path('type_inter_new') }}">Nouvelle intervention</a>
    </div>
  </div>
  <table class="table">
    <thead>
      <tr>
        <th>Id</th>
        <th>Actif</th>
        <th>Nom</th>
        <th>actions</th>
      </tr>
    </thead>
    <tbody>
      {% for type_inter in type_inters %}
      <tr>
        <td>{{ type_inter.id }}</td>
        <td>{{ type_inter.actif }}</td>
        <td>{{ type_inter.name }}</td>
        <td>
          <a href="{{ path('type_inter_show', { id: type_inter.id }) }}">
            <em class="icon icon-eye"></em></a
          >
          {% if is_granted('ROLE_ADMIN') %}
          <a
            href="{{ path('type_inter_edit', { id: type_inter.id }) }}"
            ><em class="icon icon-pencil"></em></a
          >
          {% endif %}
        </td>
      </tr>
      {% else %}
      <tr>
        <td colspan="3">no records found</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  
</div>
{% endblock %}
