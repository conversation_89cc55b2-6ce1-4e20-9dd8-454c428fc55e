{% extends 'base.html.twig' %}

{% block title %}Hello ReportingController!{% endblock %}

{% block body %}
    <div class="col-12">
        {% import "_tpl/macro.html.twig" as mac %}
        {{ form_start(form) }}

        <div style="display: flex; align-items: center">
            <h1 style="margin: 0 20px 0 0;">Saisie des temps du {{ date|date('d/m/Y') }}</h1>
            {{ form_widget(form.date) }}
            {{ form_widget(form.change_date) }}
        </div>

        <br><br>

        <table class="table">
            <thead>
            <tr>
                <th scope="col" width="40%">Utilisateur/Description</th>
                <th scope="col">Temps</th>
                <th scope="col">Client</th>
                <th scope="col">Projet</th>
                <th scope="col">Type intervention</th>
                <th scope="col">Ticket mantis</th>
            </tr>
            </thead>
                {% if taskReporting.total >= 7 %}
                    {% set class_total = 'text-success' %}
                {% else %}
                    {% set class_total = 'text-danger' %}
                {% endif %}
                <tbody>
                <tr class="font-weight-bold">
                    <td>
                        <a>{{ taskReporting.firstName }} {{ taskReporting.name }}</a>
                    </td>
                    <td class="{{ class_total }}">{{ taskReporting.total|default(0) }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                </tbody>
                <tbody id="task-{{ taskReporting.id }}" class="collapse show">

                {% if taskReporting.tasks is defined %}
                    {% for task in taskReporting.tasks %}
                        {% if task.client.id is same as(task.project.client_id) %}
                            {% set class_client_project = '' %}
                        {% else %}
                            {% set class_client_project = 'text-warning' %}
                        {% endif %}
                        <tr>
                            <td>{{ task.subject }}</td>
                            <td>{{ task.duration }}</td>
                            <td class="{{ class_client_project }}">{{ task.client.name }}</td>
                            <td class="{{ class_client_project }}">{{ task.project.name }}</td>
                            <td>{{ task.typeInter.name }}</td>
                            <td>
                                <a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}" target="_blank">
                                    {{ task.refMantis }}
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                {% endif %}
                </tbody>
        </table><br>

        <h2>Prochaine journée</h2>

        {{ form_widget(form.reporting_text) }}
        <p id="reporting_recipients">Ce reporting sera envoyé à : {{ reporting_recipients|join(', ', ' et ')|default('-') }}</p>
        {{ form_widget(form.reporting_validation) }}
        {{ form_end(form) }}
    </div>
{% endblock %}
