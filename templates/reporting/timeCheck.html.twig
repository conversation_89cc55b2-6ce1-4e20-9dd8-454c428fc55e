{% extends 'base.html.twig' %}

{% block title %}Hello ReportingController!{% endblock %}

{% block body %}
<style>
    .table th, .table td {
        text-align: left;
        vertical-align: middle;
    }

    .table th:nth-child(1),
    .table td:nth-child(1) {
        width: 40%;
    }

    .table th:nth-child(2),
    .table td:nth-child(2) {
        width: 15%;
    }

    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(4),
    .table td:nth-child(4),
    .table th:nth-child(5),
    .table td:nth-child(5),
    .table th:nth-child(6),
    .table td:nth-child(6) {
        width: 15%;
    }
</style>

<div class="col-12">
    <h1>Saisie des temps</h1>
    {% import "_tpl/macro.html.twig" as mac %}
    {{ form_start(form) }}
    <div class="row">
        <div class="col-md-6">
            {{ form_row(form.startDate) }}
        </div>
        <div class="col-md-6">
            {{ form_row(form.endDate) }}
        </div>
    </div>
    <div class="d-flex justify-content-between">
        <button class="btn btn-outline-primary">Rechercher</button>
        <a href="{{ path('reporting_export_csv', { startDate: form.startDate.vars.value|date('Y-m-d'), endDate: form.endDate.vars.value|date('Y-m-d') }) }}" class="btn btn-outline-success">
            Exporter en CSV
        </a>
    </div>
    {{ form_end(form) }}

    <table class="table">
        <thead>
            <tr>
                <th scope="col" width="40%">Utilisateur/Description</th>
                <th scope="col">Temps</th>
                <th scope="col">Client</th>
                <th scope="col">Projet</th>
                <th scope="col">Type intervention</th>
                <th scope="col">Ticket mantis</th>
            </tr>
        </thead>
        {% for user in taskReporting %}
            {% set userClass = user.total >= 7 ? 'text-success' : 'text-danger' %}
            <tbody>
                <tr class="font-weight-bold">
                    <td>
                        <a class="clickable" data-toggle="collapse" data-target="#user-{{ user.id }}-clients" aria-expanded="false" aria-controls="user-{{ user.id }}-clients">
                            + {{ user.firstName }} {{ user.name }}
                        </a>
                    </td>
                    <td class="{{ userClass }}">{{ user.total|default(0) }}</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr id="user-{{ user.id }}-clients" class="collapse">
                    <td colspan="6">
                        <table class="table">
                            {% for client in user.clients %}
                                <tr class="font-weight-bold text-primary">
                                    <td>
                                        <a class="clickable" data-toggle="collapse" data-target="#user-{{ user.id }}-client-{{ client.id }}" aria-expanded="false" aria-controls="user-{{ user.id }}-client-{{ client.id }}">
                                            + {{ client.name }}
                                        </a>
                                    </td>
                                    <td>{{ client.total|default(0) }}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr id="user-{{ user.id }}-client-{{ client.id }}" class="collapse">
                                    <td colspan="6">
                                        <table class="table">
                                            {% for project in client.projects %}
                                                <tr class="font-weight-bold text-secondary">
                                                    <td>
                                                        <a class="clickable" data-toggle="collapse" data-target="#user-{{ user.id }}-client-{{ client.id }}-project-{{ project.id }}" aria-expanded="false" aria-controls="user-{{ user.id }}-client-{{ client.id }}-project-{{ project.id }}">
                                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+ {{ project.name }}
                                                        </a>
                                                    </td>
                                                    <td>{{ project.total|default(0) }}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr id="user-{{ user.id }}-client-{{ client.id }}-project-{{ project.id }}" class="collapse">
                                                    <td colspan="6">
                                                        <table class="table">
                                                            {% for task in project.tasks %}
                                                                <tr>
                                                                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ task.subject }}</td>
                                                                    <td>{{ task.duration }}</td>
                                                                    <td>{{ client.name }}</td>
                                                                    <td>{{ project.name }}</td>
                                                                    <td>{{ task.typeInter.name }}</td>
                                                                    <td>
                                                                        <a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}" target="_blank">
                                                                            {{ task.refMantis }}
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            {% endfor %}
                                                        </table>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </table>
                                    </td>
                                </tr>
                            {% endfor %}
                        </table>
                    </td>
                </tr>
            </tbody>
        {% endfor %}
    </table>
</div>
<div class="col-12">
    <h2>Utilisateurs sans tâches ou congés</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Email</th>
            </tr>
        </thead>
        <tbody>
            {% for user in usersWithoutTasks %}
                <tr>
                    <td>{{ user.name }}</td>
                    <td>{{ user.firstName }}</td>
                    <td>{{ user.mail }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
