{% if projects_active is not empty %}
    <h1>Rapport des projets actifs au {{ date_report }}</h1>
    <table class="text-align: center;" width="100%" border="1" cellpadding="0" cellspacing="0" bgcolor="#ffffff">
        <thead>
        <tr>
            <th style="width: 50px; text-align: center">Id</th>
            <th style="width: 180px;">Client</th>
            <th>Projet</th>
            <th style="width: 130px;">Reference commande</th>
            <th>Chef de projet</th>
            <th>Type</th>
            <th>Charge vendue</th>
            <th>Charge consommée</th>
            <th>Pourcentage consommé</th>
            <th>Pourcentage avancement</th>
            <th>Date de recette</th>
            <th>Date de production</th>
        </tr>
        </thead>
        <tbody>
        {% for project in projects_active %}

            {% if project.pourcentageConsomme is empty %}
                {% set style_pourcentage = '' %}
            {% elseif project.pourcentageConsomme <= constant('App\\Entity\\Project::SEUIL_POURCENTAGE_CONSOMME') %}
                {% set style_pourcentage = 'color: #3fb618' %}
            {% elseif project.pourcentageConsomme <= 100 %}
                {% set style_pourcentage = 'color: #ffc107' %}
            {% else %}
                {% set style_pourcentage = 'color: #a94442' %}
            {% endif %}
            <tr class="text-align: center;">
                <td style="text-align: center">{{ project.id }}</td>
                <td>{{ project.client_name }}</td>
                <td>{{ project.name }}<br>
                    <span style="font-size: 80%">{% if project.first_task_date is not empty %}du  {{ project.first_task_date|date('d/m/Y') }} au {% endif %}
                        {% if project.last_task_date is not empty %} {{ project.last_task_date|date('d/m/Y') }} {% endif %}</span>
                </td>
                <td>{{ project.reference }}</td>
                <td>{{ project.manager_name }}</td>
                <td>
                    {% if project.type is same as (1) %}
                        Moyen
                    {% elseif project.type is same as (2) %}
                        Forfait
                    {% elseif project.type is same as (3) %}
                        Autre
                    {% endif %}
                </td>

                <td style="text-align: center">{{ project.chargeVendue }}</td>
                <td style="text-align: center">{{ project.chargeConsommee|number_format(2, '.', ' ') }}
                    (+ {{ project.deltaChargeConsommee|number_format(2, '.', ' ') }} )
                </td>
                <td style="text-align: center">
                    <span style="{{ style_pourcentage }}">{% if project.chargeVendue is not empty %}{{ project.pourcentageConsomme|number_format(2, '.', ' ') }}%{% endif %}</span>
                    {#{% if project.deltaPourcentageConsomme is not empty %} (+ {{ project.deltaPourcentageConsomme|number_format(2, '.', ' ') }} %) {% endif %}#}
                </td>
                <td style="text-align: center">{{ project.avancement }}</td>
                <td style="text-align: center">{% if project.dateRecette is not empty %} {{ project.dateRecette|date('d/m/Y') }} {% endif %}</td>
                <td style="text-align: center">{% if project.dateProduction is not empty %} {{ project.dateProduction|date('d/m/Y') }} {% endif %}</td>

            </tr>
        {% else %}
            <tr>
                <td colspan="3">Aucun enregistrement trouvé</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endif %}

{% if projects_standby is not empty %}
    <h1>Rapport des projets en standby au {{ date_report }}</h1>
    <table class="text-align: center;" width="100%" border="1" cellpadding="0" cellspacing="0" bgcolor="#ffffff">
        <thead>
        <tr>
            <th style="width: 50px; text-align: center">Id</th>
            <th style="width: 180px;">Client</th>
            <th>Projet</th>
            <th style="width: 130px;">Reference commande</th>
            <th>Chef de projet</th>
            <th>Engagement</th>
            <th>Charge vendue</th>
            <th>Charge consommée</th>
            <th>Pourcentage consommé</th>
            <th>Pourcentage avancement</th>
            <th>Date de recette</th>
            <th>Date de production</th>
        </tr>
        </thead>
        <tbody>
        {% for project in projects_standby %}

            {% if project.pourcentageConsomme is empty %}
                {% set style_pourcentage = '' %}
            {% elseif project.pourcentageConsomme <= constant('App\\Entity\\Project::SEUIL_POURCENTAGE_CONSOMME') %}
                {% set style_pourcentage = 'color: #3fb618' %}
            {% elseif project.pourcentageConsomme <= 100 %}
                {% set style_pourcentage = 'color: #ffc107' %}
            {% else %}
                {% set style_pourcentage = 'color: #a94442' %}
            {% endif %}
            <tr class="text-align: center;">
                <td style="text-align: center">{{ project.id }}</td>
                <td>{{ project.client_name }}</td>
                <td>{{ project.name }}<br>
                    <span style="font-size: 80%">{% if project.first_task_date is not empty %}du  {{ project.first_task_date|date('d/m/Y') }} au {% endif %}
                        {% if project.last_task_date is not empty %} {{ project.last_task_date|date('d/m/Y') }} {% endif %}</span>
                </td>
                <td>{{ project.reference }}</td>
                <td>{{ project.manager_name }}</td>
                <td>
                    {% if project.type is same as (1) %}
                        Engagement
                    {% elseif project.type is same as (2) %}
                        Forfait
                    {% endif %}
                </td>
                <td style="text-align: center">{{ project.chargeVendue }}</td>
                <td style="text-align: center">{{ project.chargeConsommee|number_format(2, '.', ' ') }}
                    (+ {{ project.deltaChargeConsommee|number_format(2, '.', ' ') }} )
                </td>
                <td style="text-align: center">
                    <span style="{{ style_pourcentage }}">{% if project.chargeVendue is not empty %}{{ project.pourcentageConsomme|number_format(2, '.', ' ') }}%{% endif %}</span>
                    {#{% if project.deltaPourcentageConsomme is not empty %} (+ {{ project.deltaPourcentageConsomme|number_format(2, '.', ' ') }} %) {% endif %}#}
                </td>
                <td style="text-align: center">{{ project.avancement }}</td>
                <td style="text-align: center">{% if project.dateRecette is not empty %} {{ project.dateRecette|date('d/m/Y') }} {% endif %}</td>
                <td style="text-align: center">{% if project.dateProduction is not empty %} {{ project.dateProduction|date('d/m/Y') }} {% endif %}</td>

            </tr>
        {% else %}
            <tr>
                <td colspan="3">Aucun enregistrement trouvé</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endif %}


{% if detail_projet_aucun is not empty and detail_projet_aucun.clients %}
    <h1>Détail des saisies sans projet</h1>

    <table class="text-align: center;" width="100%" border="1" cellpadding="0" cellspacing="0" bgcolor="#ffffff">
        <thead>
        <th style="">Client</th>
        <th style="text-align: center;">Charge consommée (en jours)</th>
        </thead>
        <tbody>
        {% for statistic in detail_projet_aucun.clients %}
            <tr>
                <td>{{ statistic.name }}<br>
                    <span style="font-size: 80%">{% if statistic.first_task_date is not empty %}du  {{ statistic.first_task_date|date('d/m/Y') }} au {% endif %}
                        {% if statistic.last_task_date is not empty %} {{ statistic.last_task_date|date('d/m/Y') }} {% endif %}</span>
                </td>
                <td style="text-align: center;">{{ statistic.total|number_format(2, '.', ' ') }}
                    (+ {{ statistic.deltaTotal|number_format(2, '.', ' ') }})
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>


{% endif %}
