{% block body %}
    <div><PERSON><PERSON>r,</div><br>

    <div style="font-weight: bold; text-decoration: underline;">Aujourd'hui j'ai fait :</div>
    {% for task in tasksReporting.tasks %}
        <div> -   {{ task.client.name }} / {{ task.project.name }} / <a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}">{{ task.refMantis }}</a>. {{ task.duration }}h : <b>{{ task.subject }}</b></div>
    {% endfor %}

    <br>
    <div style="font-weight: bold; text-decoration: underline;">Prochaine journée :</div>
    <div style="white-space: pre-line;">
        {{ reportingText }}
    </div>

    <br><div>Cordialement,</div>
    <div>{{ userFullName }}</div>
{% endblock %}