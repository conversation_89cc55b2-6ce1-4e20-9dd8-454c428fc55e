{% block body %}
    <p><PERSON><PERSON><PERSON>, voici la synthèse de la des temps du {{ "now"|date("d/m/Y")}} :</p>
    <table style="padding: 10px; border: 1px solid black; border-collapse: collapse">
        <thead>
        <tr>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">Nom / Prénom</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">Client</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">Projet</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">N° de Ticket</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">Type d'inter</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse">Résumé de l'intervention</th>
            <th style="padding: 10px; border: 1px solid black; border-collapse: collapse"><PERSON><PERSON><PERSON> de l'intervention</th>
        </tr>
        </thead>
        {% for userName, value in userTasks %}
            {% for task in value %}
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse">{{ task.user_name }} {{ task.user_firstname }} </td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse">{{ task.client_name }}</td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse">{{ task.project_name }}</td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse"><a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}">
                                {{ task.refMantis }}</a></td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse">{{ task.typeinter_name }} </td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse">{{ task.subject }}</td>
                        <td style="padding: 10px; border: 1px solid black; border-collapse: collapse;text-align: center;">{{ task.duration }} h</td>
                    </tr>
            {% endfor %}
            {% if userName %}
                    <tr style="border-right: none">
                        <td style="padding: 10px; border-bottom: 5px solid black; border-right : 1px solid black; border-left:1px solid black ;border-top: 1px solid black; border-collapse: collapse">{{ userName }}</td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-top: 1px solid black; border-collapse: collapse"></td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-top: 1px solid black; border-collapse: collapse"></td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-top: 1px solid black; border-collapse: collapse"></td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-top: 1px solid black; border-collapse: collapse"></td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-top: 1px solid black; border-collapse: collapse"></td>
                        <td style="padding: 10px; border-bottom: 5px solid black; border-left: 1px solid black; border-top: 1px solid black; border-collapse: collapse; text-align: center;">Total : {{ totalHoursByUser[userName] }} h</td>
                    </tr>
            {% endif %}
        {% endfor %}
                </tbody>
    </table>
{% endblock %}