{% extends 'base.html.twig' %}
{% block body %}
    <div class="col-12">
        <div class="title-bloc">
            <h1>
                {% block title %}Bonjour, bienvenue sur l'outil de suivi de temps{% endblock %}
            </h1>
            {% if app.user %}
                <span class="subtitle-index">
                    {{ app.user.firstname }}
                    {{ app.user.name }}
                    :
                    {{ app.user.highestRole }}
                </span>
            {% endif %}
            {% if assignment %}
                {% set allDates = assignment|keys %}

                <style>
                    tbody tr {
                        height: 100px;
                    }

                    tbody {
                        display: table-row-group;
                    }
                    table {
                        border-collapse: separate;
                        border-spacing: 0;
                        width: 100%;
                        margin: 1.5rem 0;
                        font-family: "Segoe UI", Roboto, sans-serif;
                        font-size: 0.95rem;
                    }

                    th, td {
                        padding: 0.75rem 1rem;
                        text-align: center;
                        vertical-align: middle;
                    }

                    th {
                        border: 0.5px solid #dee2e6;
                        border-right: none;
                    }

                    th:last-child {
                        border-right: 0.5px solid #dee2e6;
                    }

                    thead th {
                        background-color: #f8f9fa;
                        color: #2780e3;
                        font-weight: 600;
                        font-size: 0.95rem;
                    }

                    thead th:first-child {
                        background-color: transparent;
                        border: none;
                    }

                    tbody td:first-child {
                        background-color: #f2f2f2;
                        font-weight: 500;
                        text-align: left;
                    }

                    td {
                        background-color: #ffffff;
                        border-right: 0.5px solid #dee2e6;
                    }

                    em {
                        display: block;
                        margin-top: 0.25rem;
                        font-size: 0.85rem;
                        color: #6c757d;
                        font-style: italic;
                    }

                    tr:hover td:not(:first-child) {
                        background-color: #f9fbfc;
                    }

                    tr:last-child td {
                        border-bottom: 0.5px solid #dee2e6;
                    }

                    tr td:last-child {
                        border-right: 0.5px solid #dee2e6;
                    }

                    tr td:first-child {
                        border-left: 0.5px solid #dee2e6;
                    }

                    tr:first-child td:first-child {
                        border-top: 0.5px solid #dee2e6;
                    }
                </style>
                <div style="margin-top: 32px; margin-bottom: 16px; display: flex; flex-direction: row; justify-content: space-between">
                    <div>
                        <h2 style="margin-bottom: 0px">Mon plan de charge</h2>
                        <p><em>Jusqu'au prochain vendredi inclus</em></p>
                    </div>

                    {% if is_granted('ROLE_ADMIN') %}
                        <a href="#" id="admin-update-btn" class="btn-submit btn btn-outline-primary" style="height: fit-content">
                            ADMIN: Mettre à jour
                        </a>
                    {% endif %}
                </div>
                <table>
                    <colgroup>
                        <col style="width: 0.4%;">      <!-- 1ère colonne -->
                        {% for date in allDates %}
                            <col style="width: 1%;">
                        {% endfor %}
                    </colgroup>
                    <thead>
                    <tr>
                        <th></th>
                        {% for date in allDates %}
                            <th>{{ date }}</th>
                        {% endfor %}
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td><em>Matin</em></td>
                        {% for date in allDates %}
                            {% set modalId = 'modal-0-' ~ loop.index0 %}
                            <td>
                                {{ assignment[date][0].value }}
                                {% if assignment[date][0].comment is not empty %}
                                    {% if assignment[date][0].comment|length > 48 %}
                                        <button type="button" class="btn btn-link" data-toggle="modal" data-target="#{{ modalId }}">
                                            <em>Note: {{ assignment[date][0].comment[:48] }}...</em>
                                        </button>
                                    {% else %}
                                        <em>Note: {{ assignment[date][0].comment }}</em>
                                    {% endif %}
                                 {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td><em>Après-midi</em></td>
                        {% for date in allDates %}
                            {% set modalId = 'modal-1-' ~ loop.index0 %}
                            <td>
                                {{ assignment[date][1].value }}
                                {% if assignment[date][1].comment is not empty %}
                                    {% if assignment[date][1].comment|length > 48 %}
                                        <button type="button" class="btn btn-link" data-toggle="modal" data-target="#{{ modalId }}">
                                            <em>Note: {{ assignment[date][1].comment[:32] }}...</em>
                                        </button>
                                    {% else %}
                                        <em>Note: {{ assignment[date][1].comment }}</em>
                                    {% endif %}
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                    </tbody>
                </table>

                {% for date in allDates %}
                    {% for i, singleAssignment in assignment[date] %}
                        {% if singleAssignment.comment is not empty and singleAssignment.comment|length > 48 %}
                            {% set modalId = 'modal-' ~ i ~ '-' ~ loop.parent.loop.index0 %}
                            <div class="modal fade" tabindex="-1" role="dialog" id="{{ modalId }}">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" style="display: flex; align-items: center"><em class="icon icon-info" style="margin-right: 10px"></em>{{ date }} - {{ singleAssignment.value }}</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>{{ singleAssignment.comment }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endfor %}
            {% endif %}
        </div>
    </div>
    <div class="col-12">
        <div class="skill-chrono-index">
            <div class="chrono-index">
                {{ include("sdt/chronoTask.html.twig") }}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const updateBtn = document.getElementById('admin-update-btn');
            updateBtn.addEventListener('click', function (e) {
                e.preventDefault();

                // Set loading text
                updateBtn.innerHTML = 'Chargement...';
                updateBtn.classList.add('disabled'); // Optional: visually disable button

                fetch('{{ path('update_assignments') }}', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                    .then(response => {
                        if (!response.ok) throw new Error('Request failed');
                        return response.text();
                    })
                    .then(() => {
                        location.reload();
                    })
                    .catch(err => {
                        console.error(err);
                        alert("Erreur lors de la mise à jour.");
                        updateBtn.innerHTML = 'ADMIN: Mettre à jour'; // Revert text on error
                        updateBtn.classList.remove('disabled');
                    });
            });
        });
    </script>
{% endblock %}
