<div id="task_new" class="col row chrono-task" xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="input-number-styled">
        <h2 class="home-title">Lancer un Chrono</h2>
        <span class="d-none" id="user_id">{{ app.user.id }}</span>
        {{ include("sdt/chronoForm.html.twig") }}
        <button class="btn btn-info mt-1" id="btn_submit_new_task" style="margin-left:30%; "type="submit" v-on:click="create" value="create">
            Lancer un timer
        </button>
    </div>
    <div class="col-lg-8" v-if="!empty">
        <h2>Tâches en cours</h2>
        <table class="table mt-4 table-form input-number-styled form-select2-adaptative" style="width:30%" id="chrono_table">
            <thead>
            <tr>
                <th></th>
                <th>Ticket <PERSON></th>
                <th>R<PERSON>umé</th>
                <th>Temps</th>
                <th v-for="lib in select_libelle">
                    ${lib}
                </th>
                <th>En cours</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(task, index) in tasks" :class="state_class(task.state)">
                <th>
                    <button aria-label="Close" class="close" type="button" v-on:click="task_delete(index)">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </th>
                <th>
                    <input type="text" v-model="task.timer_task_refMantis" @change="form_check(index)"/>
                </th>
                <th>
                    <textarea type="text" v-model="task.timer_task_subject" @change="form_check(index)"></textarea>
                </th>
                <th>${task.time}</th>
                <th>
                    <select2 class="form-control" 
                        :options="select_options['timer_task_client']"  
                        v-model="task['timer_task_client']" 
                        :task-index="index"
                        :type="'client'" 
                        :status="status"
                        @input="emittedChangeClient($event, index)"
                        :disabled="task.disabled.client"
                        >
                    </select2>
                </th>
                <th>
                    <select2 class="form-control"  
                        :options="select_options['timer_task_typeInter']"  
                        v-model="task['timer_task_typeInter']" 
                        :task-index="index"
                        :type="'typeInter'"
                        :status="status"
                        >
                    </select2>
                </th>
                <th>
                    <div class="position-relative d-inline-block">
                        <template v-if="task.loading">
                            <div class="block_input_loader">
                                <div class="wrap_spinner_loader">
                                    <div class="spinner-loader"></div>
                                </div>
                            </div>
                        </template>
                        <select2 class="form-control"  
                            :options="findProject(task.id)"  
                            v-model="task['timer_task_project']" 
                            :task-index="index"
                            :type="'project'" 
                            :status="status"
                            :disabled="task.disabled.project"
                            :class="{'loading': task.loading}"
                            >
                        </select2>
                    </div>
                </th>
                <th>
                    <label class="switch">
                        <input :checked="task.is_playing" type="checkbox" v-on:click="switch_pause(index)">
                        <span class="slider round"></span>
                    </label>
                </th>
                <th v-show="task.state==0">
                    <button type="button" class="btn btn-sm btn-outline-success btn-send" v-on:click="submit(index)"
                            :disabled="!task.valid"><em class="icon icon-send icon-paper-plane"></em>
                    </button>
                </th>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<div id="el"></div>

{% block javascripts %}
    {{ encore_entry_script_tags("task_new") }}
    
{% endblock %}
