{% extends 'base.html.twig' %}
{% block body %}
  <div class="col col-content">
    <style>
      .example-wrapper {
        margin: 1em auto;
        max-width: 500px;
        width: 95%;
        font: 18px/1.5 sans-serif;
      }
      .example-wrapper code {
        background: #f5f5f5;
        padding: 2px 6px;
      }
    </style>

    <div class="example-wrapper">
      <h1>
        {% block title %}Formulaire de connexion !{% endblock %}
      </h1>
      {% if error %}
        <div>{{ error.messageKey|trans(error.messageData, 'security') }}</div>
      {% endif %}
      <form action="{{ path('security_login') }}" method="post" class="">
        <div class="form-group">
          <label for="username">Nom d'utilisateur
          </label>

          <input type="text" id="username" name="_username" class="username form-control" value="{{ last_username }}" placeholder="Nom d'utilisateur"/>
        </div>

        <div class="form-group">
          <label for="username">Mot de passe
          </label>
          <input type="password" id="password" class="password  form-control" name="_password" placeholder="Mot de passe"/>
        </div>
        <button type="submit" class="btn-submit btn btn-outline-primary">
          Connexion
        </button>
      </form>
      <a href="{{ path('security_login_google') }}" class="btn-submit btn btn-outline-primary">
        Se connecter avec Google
      </a>
      <small id="resetPassword" class="form-text text-muted">
        <a href="{{ path('security_reset_password') }}">Réinitialiser mon mot de passe</a >
      </small>
    </div>
  </div>
{% endblock %}

{% block navbar %}

{% endblock %}