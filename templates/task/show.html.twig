{% extends 'base.html.twig' %}  {% block body %}
    <div class="{{ _col_table }}">
        <h1>{% block title %}Task{% endblock %}</h1>
        <table class="table">
            <tbody>
            <tr>
                <th>Id</th>
                <td>{{ task.id }}</td>
            </tr>
            <tr>
                <th>Subject</th>
                <td>{{ task.subject }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{{ task.createdAt ? task.createdAt|date('Y-m-d H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>Date</th>
                <td>{{ task.date ? task.date|date('Y-m-d H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>Content</th>
                <td>{{ task.content }}</td>
            </tr>
            <tr>
                <th>RefMantis</th>
                <td>{{ task.refMantis }}</td>
            </tr>
            <tr>
                <th>Duration</th>
                <td>{{ task.duration }}</td>
            </tr>
            </tbody>
            <tfoot></tfoot>
        </table>

        <div class="table-form">
            <a class="btn btn-outline-secondary mb-2" href="{{ path('task_edit', { id: task.id }) }}">Modifier</a>
            {{ include("task/_delete_form.html.twig") }}
            <a href="#" onClick="history.back()"><em class="icon icon-arrow-left-circle"></em></a>
        </div>
    </div>
{% endblock %}
