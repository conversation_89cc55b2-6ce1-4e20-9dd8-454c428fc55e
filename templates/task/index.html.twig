{% extends 'base.html.twig' %}
{% block stylesheets %}
    {{parent()}}
    <link rel="stylesheet" href="assets/css/lucas.css">
{% endblock %}
{% block body %}
    <div class="{{ _col_table }} bloc-list">
        <div class="title-form">
            <h1>
                {% block title %}Toutes les interventions{% endblock %}
            </h1>
            <div class="table-form">
                <a class="btn btn-outline-success"  href="{{ path('task_new') }}">Nouvelle intervention</a>
            </div>
        </div>
        {% if msg == "" %}
            message null
        {% endif %}
        <hr/>
        {{ msg }}
        <hr/>
        
        <table class="table table-large" style="width:100%;">
            <thead>
            <tr>
                <th>Subject</th>
                <th>User</th>
                <th>Date</th>
                <th>RefMantis</th>
                <th>Duration</th>
                <th>Type Inter</th>
                <th>Client</th>
                <th>Projet</th>
                <th>actions</th>
            </tr>
            </thead>
            <tbody>
            {% for task in tasks %}
                <tr>
                    <td style="width:330px">{{ task.subject }}</td>
                    <td>{{ task.user.firstname }}</td>
                    <td>{{ task.date ? task.date|date('Y-m-d') : '' }}</td>
                    <td>
                        <a href="https://mantis.itroom.fr/view.php?id={{ task.refMantis }}">
                            {{ task.refMantis }}</a>
                    </td>
                    <td>{{ task.duration }}</td>
                    <td>{{ task.typeInter.name }}</td>
                    <td>{{ task.client.name }}</td>
                    <td>{{ task.project.name }}</td>
                    <td>
                        <a href="{{ path('task_show', { id: task.id }) }}"><em class="icon icon-eye"></em></a>
                        <a href="{{ path('task_edit', { id: task.id }) }}"><em class="icon icon-pencil"></em></a>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="8">no records found</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        
    </div>
{% endblock %}