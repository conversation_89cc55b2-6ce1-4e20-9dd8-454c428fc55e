{#Calendrier#}
{#La page a pour but d'avoir une vue globale des congés demandés et validés#}
{#On pourrait créer une demande de congé en cliquant sur un jour. Le formulaire de création de la demande serait pré rempli avec la date cliquée en date de début.#}

{#Pour les personnes habilitées à accepter les congés#}
{#Il y a également un bouton disponible pour exporter en CSV tous les congés de l'année#}

{% extends "base.html.twig" %}
{% block body %}
    {% set is_admin = is_granted('ROLE_GESTION_CONGES') %}
    <div class="col-12">
        <h1>{% block title %}Congé - Calendrier{% endblock %}</h1>
        {% if is_admin %}
            <div class="mb-3 d-flex justify-content-between">
                <div>
                    {{ form_start(form) }}
                    {{ form_end(form) }}
                </div>
                <div>
                    <p>Exporter congés au format csv de l'année : </p>
                    {% for i in -1..1 %}
                        {% set year = ("now +"~i~" years")|date("Y") %}
                        <a class="btn btn-outline-success"
                           href="{{ path('conge_csv', {'annee': year}) }}"> {{ year }} </a>
                    {% endfor %}
                </div>
                <div>
                    <p>Exporter congés au format csv du mois </p>
                    <div class="form-group d-flex">
                        <input id="export-mont-input" type="month" class="form-control">
                        <a id="export-month" class="btn btn-outline-success" href="#">OK</a>
                    </div>

                </div>
            </div>
        {% endif %}
        <div id="calendar" class="calendar-conge" data-events="{{ conges|json_encode }}"></div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags("calendar-conge") }}
{% endblock %}
