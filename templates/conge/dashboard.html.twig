{% import "_tpl/macro.html.twig" as macro %}

{#Tableau de bord#}
{#La page a pour but de faire un état des lieux au jour le jour.#}
{#Pratique en cas d'appel, lorsqu'on demande un interlocuteur en congé.#}
{#Cela pourra également aider les personnes en charge des projets à distribuer les charges de travail en fonction des disponibilités des collaborateurs.#}
{#Une partie montrant qui est en congé à un jour donné (aujourd'hui par défaut), et jusqu'à quand.#}
{#Une autre partie montrant les congés à venir (dans les 30 jours suivant)#}

{#Pour les personnes habilitées à accepter les congés#}
{#Une partie montrant les dernières demandes de congés#}
 {# {% set conges =  [{"id":4609,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"20-09-2020","dateFin":"24-09-2020","user":74,"dureeDebut":"journee","dureeFin":"matin"},{"id":1213,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"18-02-2020","dateFin":"03-03-2020","user":71,"dureeDebut":"journee","dureeFin":"matin"},{"id":7734,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"12-05-2020","dateFin":"17-05-2020","user":97,"dureeDebut":"journee","dureeFin":"matin"},{"id":8481,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"16-03-2020","dateFin":"21-03-2020","user":63,"dureeDebut":"journee","dureeFin":"matin"},{"id":9780,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"23-04-2020","dateFin":"04-05-2020","user":98,"dureeDebut":"journee","dureeFin":"matin"},{"id":8724,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"15-03-2020","dateFin":"28-03-2020","user":59,"dureeDebut":"journee","dureeFin":"matin"},{"id":221,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"05-12-2019","dateFin":"14-12-2019","user":71,"dureeDebut":"journee","dureeFin":"matin"},{"id":5095,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"22-06-2020","dateFin":"04-07-2020","user":68,"dureeDebut":"journee","dureeFin":"matin"},{"id":3159,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"28-07-2020","dateFin":"02-08-2020","user":68,"dureeDebut":"journee","dureeFin":"matin"},{"id":873,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"04-10-2019","dateFin":"08-10-2019","user":98,"dureeDebut":"journee","dureeFin":"matin"},{"id":67,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"11-01-2020","dateFin":"12-01-2020","user":84,"dureeDebut":"journee","dureeFin":"matin"},{"id":2798,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"26-05-2020","dateFin":"04-06-2020","user":67,"dureeDebut":"journee","dureeFin":"matin"},{"id":2741,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"29-04-2020","dateFin":"02-05-2020","user":99,"dureeDebut":"journee","dureeFin":"matin"},{"id":9760,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"05-08-2020","dateFin":"09-08-2020","user":68,"dureeDebut":"journee","dureeFin":"matin"},{"id":1292,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"16-10-2019","dateFin":"25-10-2019","user":88,"dureeDebut":"journee","dureeFin":"matin"},{"id":4320,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"05-09-2020","dateFin":"12-09-2020","user":86,"dureeDebut":"journee","dureeFin":"matin"},{"id":5250,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"08-10-2019","dateFin":"20-10-2019","user":82,"dureeDebut":"journee","dureeFin":"matin"},{"id":8183,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"16-10-2019","dateFin":"18-10-2019","user":104,"dureeDebut":"journee","dureeFin":"matin"},{"id":1216,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"30-11-2019","dateFin":"14-12-2019","user":66,"dureeDebut":"journee","dureeFin":"matin"},{"id":2097,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"15-04-2020","dateFin":"27-04-2020","user":61,"dureeDebut":"journee","dureeFin":"matin"},{"id":5524,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"02-02-2020","dateFin":"15-02-2020","user":65,"dureeDebut":"journee","dureeFin":"matin"},{"id":7598,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"08-11-2019","dateFin":"11-11-2019","user":88,"dureeDebut":"journee","dureeFin":"matin"},{"id":5422,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"04-07-2020","dateFin":"07-07-2020","user":58,"dureeDebut":"journee","dureeFin":"matin"},{"id":3934,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"18-06-2020","dateFin":"23-06-2020","user":94,"dureeDebut":"journee","dureeFin":"matin"},{"id":4962,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"12-11-2019","dateFin":"18-11-2019","user":82,"dureeDebut":"journee","dureeFin":"matin"},{"id":6115,"dateCreation":"27-09-2019","etat":"refuse","dateDebut":"11-02-2020","dateFin":"25-02-2020","user":103,"dureeDebut":"journee","dureeFin":"matin"},{"id":7649,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"10-06-2020","dateFin":"11-06-2020","user":92,"dureeDebut":"journee","dureeFin":"matin"},{"id":6557,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"09-03-2020","dateFin":"20-03-2020","user":102,"dureeDebut":"journee","dureeFin":"matin"},{"id":7950,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"15-02-2020","dateFin":"17-02-2020","user":76,"dureeDebut":"journee","dureeFin":"matin"},{"id":454,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"06-04-2020","dateFin":"11-04-2020","user":100,"dureeDebut":"journee","dureeFin":"matin"},{"id":2202,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"19-04-2020","dateFin":"25-04-2020","user":75,"dureeDebut":"journee","dureeFin":"matin"},{"id":2086,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"05-12-2019","dateFin":"17-12-2019","user":81,"dureeDebut":"journee","dureeFin":"matin"},{"id":2250,"dateCreation":"27-09-2019","etat":"en attente","dateDebut":"11-03-2020","dateFin":"22-03-2020","user":96,"dureeDebut":"journee","dureeFin":"matin"},{"id":2619,"dateCreation":"27-09-2019","etat":"accepte","dateDebut":"16-09-2020","dateFin":"27-09-2020","user":70,"dureeDebut":"journee","dureeFin":"matin"}] %} #}
{% extends 'base.html.twig' %}

{% block body %}
    <div class="col-12">
        <h1>{% block title %}Congé - Tableau de bord{% endblock %}</h1>
    </div>
    <div class="col-xl-6 conge-dashboard-list">
        <h2>Congés aujourd'hui</h2>
        <ul class="list-group">
            {% for conge in congeDuJour %}
                <li class="list-group-item">
                    <span>{{ conge.name }} {{ conge.firstName }}</span>
                    <span class="text-capitalize">
                       {% if conge.dureeFin == "null" %}
                           {{ conge.dureeDebut }}
                       {% else %}
                           jusqu'au {{ macro.format_date_conge(conge.dateFin, conge.dureeFin) }}
                       {% endif %}
                    </span>
                </li>
            {% else %}
                <li class="list-group-item bg-transparent">Aucun</li>
            {% endfor %}
        </ul>
    </div>
    <div class="col-xl-6 conge-dashboard-list">
        <h2>Congés à venir</h2>
        <ul class="list-group">
            {% for conge in congeAVenir %}
                <li class="list-group-item">
                    <span>{{ conge.name }} {{ conge.firstName }}</span>
                    <p class="text-capitalize">
                        {{ macro.format_date_conge(conge.dateDebut, conge.dureeDebut) }}
                        {% if conge.dateDebut != conge.dateFin %}
                            - {{ macro.format_date_conge(conge.dateFin, conge.dureeFin) }}
                        {% endif %}
                    </p>
                </li>
            {% else %}
                <li class="list-group-item bg-transparent">Aucun</li>
            {% endfor %}
        </ul>
    </div>
    <div class="col-xl-6 conge-dashboard-list">
        <h2>Congés en attente</h2>
        <ul class="list-group">
            {% for conge in congesEnAttente %}
                <li class="list-group-item">
                    <span>{{ conge.name }} {{ conge.firstName }}</span>
                    <p class="text-capitalize">
                        {{ macro.format_date_conge(conge.dateDebut, conge.dureeDebut) }}
                        {% if conge.dateDebut != conge.dateFin %}
                            - {{ macro.format_date_conge(conge.dateFin, conge.dureeFin) }}
                        {% endif %}
                    </p>
                </li>
            {% else %}
                <li class="list-group-item bg-transparent">Aucun</li>
            {% endfor %}
        </ul>
    </div>
{% endblock %}



