{% import "_tpl/macro.html.twig" as macro %}

{#Validation de congés#}
{#Cette page ne sera vue que par les personnes habilitées à valider des congés#}
{#C'est une liste de toutes les demandes de congés rangés par date de création.#}
{#On peut connaitre le demandeur, la période du congé, son état (en attente, accepté, refusé)#}
{#Des boutons d'action sont disponibles pour accepter ou refuser une demande.#}
{#Au refus d'une demande,  une petite popin demande un commentaire facultatif qui sera envoyé au demandeur.#}

{% extends 'base.html.twig' %}
{% block body %}
    <div class="col-12">
        <h1>{% block title %}Congé - Validation{% endblock %}</h1>
        <table class="table">
            <thead>
            <tr>
                <th>Personne</th>
                <th>Date de début</th>
                <th>Date de fin</th>
                <th>Date de création</th>
                <th>Motif</th>
                <th>Etat</th>
            </tr>
            </thead>
            <tbody>
            {% for conge in congesEnAttente %}
                <tr>
                    <td>
                        {{ conge.name }} {{ conge.firstName }}
                    </td>
                    <td class="text-capitalize">
                        {{ macro.format_date_conge_two(conge.dateDebut, conge.dureeDebut) }}
                    </td>
                    <td class="text-capitalize">
                        {{ macro.format_date_conge_two(conge.dateFin, conge.dureeFin) }}
                    </td>
                    <td>
                        {{ conge.dateCreation|date('d/m/y') }}
                    </td>
                    <td>
                        {{ conge.reason|trans }}
                    </td>
                    <td>
                        <a class="btn btn-sm btn-success" href="{{ path('conge_validate', { 'id': conge.id }) }}">
                            Valider
                        </a>
                        <a class="btn btn-sm btn-danger" href="{{ path('conge_refuse', { 'id': conge.id }) }}">
                            Refuser
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}
