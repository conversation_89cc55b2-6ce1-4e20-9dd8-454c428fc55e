{% macro menuItem(name, url, subs = false) %}
    {% spaceless %}
        {% set currentUrl = path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')) %}
        {% if not subs %}
            {% set isActive = currentUrl == url %}
            <li class="nav-item {% if isActive %}active{% endif %}">
                <a class="nav-link" href="{{ url }}">{{ name }}
                    {% if isActive %}
                        <span class="sr-only">(current)</span>
                    {% endif %}
                </a>
            </li>
        {% else %}
            {% set isActive = false %}
            {% for sub in subs %}
                {% if sub.url == currentUrl %}
                    {% set isActive = true %}
                {% endif %}
            {% endfor %}
            <li class="nav-item {% if isActive %}active{% endif %}">
                <a class="nav-link dropdown-toggle" href="#" data-toggle="dropdown">
                    {{ name }}
                </a>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    {% for sub in subs %}
                        <a class="dropdown-item" href="{{ sub.url }}">{{ sub.name }}</a>
                    {% endfor %}
                </div>
            </li>
        {% endif %}
    {% endspaceless %}
{% endmacro %}

{# macro generant un formulaire "form" à "col" colonnes
Les champs de type submit et hidden ne sont pas comptés dans les colonnes #}
{% macro form_2column(form, col = 2) %}
    <div id="demande-conge" class="row">
        {% if not 12 is divisible by(col) %}
            {% set col = ((col / 2) | round()) * 2 %}
        {% endif %}
        {% if col > 6 %}
            {% set col = 6 %}
        {% endif %}
        {% set col_class = "col-md-"~ 12 / col %}
        {% set child_length = 0 %}
        {% set form_map = [] %}
        {% for key, champ in form.children %}
            {% if champ.vars.block_prefixes[1] != "submit" and champ.vars.block_prefixes[1] != "hidden" %}
                {% set form_map = form_map | merge([key]) %}
            {% endif %}
        {% endfor %}
        {% set limits = [0] %}
        {% for i in 1..col %}
            {% set item = ((form_map | length) - 1) / (col / i) %}
            {% set limits = limits | merge([item]) %}
        {% endfor %}
        {% set col_count = 0 %}
        <div id="form-conge" class="{{ col_class }}">
            {% for i in 0..col - 1 %}
                {% set start = limits[i] | round(0, "floor") %}
                {% set end = limits[i + 1] | round(0, "floor") %}
                    {% for j in start..end %}
                        {% set child = form.children[form_map[j]] %}
                        {{ form_row(child) }}
                    {% endfor %}
            {% endfor %}
        </div>
    </div>
{% endmacro %}

{% macro form_2column_VueJS(form, col = 2) %}
    <div class="row">
        {% if not 12 is divisible by(col) %}
            {% set col = ((col / 2) | round()) * 2 %}
        {% endif %}
        {% if col > 6 %}
            {% set col = 6 %}
        {% endif %}
        {% set col_class = "col-md-"~ 12 / col %}
        {% set child_length = 0 %}
        {% set form_map = [] %}
        {% for key, champ in form.children %}
            {% if champ.vars.block_prefixes[1] != "submit" and champ.vars.block_prefixes[1] != "hidden" %}
                {% set form_map = form_map | merge([key]) %}
            {% else %}
                {% do
                    champ.setRendered() %}
            {% endif %}
        {% endfor %}
        {% set limits = [0] %}
        {% for i in 1..col %}
            {% set item = ((form_map | length) - 1) / (col / i) %}
            {% set limits = limits | merge([item]) %}
        {% endfor %}
        {% set col_count = 0 %}
        {% for i in 0..col - 1 %}
            {% set start = limits[i] | round(0, "floor") %}
            {% set end = limits[i + 1] | round(0, "floor") %}
            <div class="{{ col_class }}">
                {% for j in start..end %}
                    {% set child = form.children[form_map[j]] %}
                    {% set attr = {'attr': {'v-model' : "task_new." ~ child.vars.id}} %}
                    {% if child.vars.choices is defined %}
                        {% form_theme child "_tpl/formTheme-chrono.html.twig" %}
                        <span class="select-options d-none" 
                            select-id="{{ child.vars.id }}"
                            name="{{ child.vars.name|capitalize }}"
                            {% if child.vars.name|lower == 'client' or child.vars.name|lower == 'typeinter' %}
                                data-selected-id="{{child.vars.value}}"
                            {% elseif child.vars.name|lower == 'project' %}
                                {% if child.vars.preferred_choices|length > 0 %}
                                    {% set keys = child.vars.preferred_choices|keys %}
                                    data-selected-id="{{keys[0]}}"
                                {% endif %}
                            {% endif %}
                            >
                            {{ child.vars.choices|json_encode() }}
                        </span>
                        {% set attr = {'attr': {'v-model' : "task_new." ~ child.vars.id, ":options":"select_options."~ child.vars.id} } %}
                    {% endif %}
                    {{ form_row(child, attr) }}
                {% endfor %}
            </div>
        {% endfor %}
    </div>
{% endmacro %}

{# {% form_theme child "_tpl/formTheme-chrono.html.twig" %}
    <span class="select-options d-none" select-id="{{child.vars.id}}" name="{{child.vars.name|capitalize}}">
      {{ child.vars.choices|json_encode() }}</span>
    {{ form_row(child, {'attr': {'v-model' : "task_new." ~ child.vars.id, ":options":"select_options."~ child.vars.id}}
    ) }}
  {% else %}#}


{% macro format_date_conge(date, duree) %}
    <span class="font-weight-bold">{{ date | date("d-m-Y") }}</span>
    {% if duree != "null" and duree !="journée complète" %}
        <span class="date-day-duree">{{ duree }}</span>
    {% endif %}
{% endmacro %}

{% macro format_date_conge_two(date, duree) %}
    <span>{{ date | date("d-m-Y") }}</span>
    {% if duree != "null" and duree !="journée complète" %}
        <span class="date-day-duree">{{ duree }}</span>
    {% endif %}
{% endmacro %}
