{% use "bootstrap_4_layout.html.twig" %}
{# https://github.com/symfony/symfony/blob/master/src/Symfony/Bridge/Twig/Resources/views/Form/form_div_layout.html.twig
https://github.com/symfony/symfony/blob/master/src/Symfony/Bridge/Twig/Resources/views/Form/bootstrap_base_layout.html.twig
https://github.com/symfony/symfony/blob/master/src/Symfony/Bridge/Twig/Resources/views/Form/bootstrap_4_layout.html.twig #}

{# ajout de la class select2 sur les <select> #}
{%- block choice_widget_collapsed -%}
  {%- if required and placeholder is none and not placeholder_in_choices and not multiple and (attr.size is not defined or attr.size <= 1) -%}
    {% set required = false %}
  {%- endif -%}
  <select class="form-control select2" {{ block('widget_attributes') }} {% if multiple %} multiple="multiple" {% endif %}>
    {%- if placeholder is not none -%}
      <option value="" {% if required and value is empty %} selected="selected" {% endif %}>{{ placeholder != '' ? (translation_domain is same as(false) ? placeholder : placeholder|trans({}, translation_domain)) }}</option>
    {%- endif -%}
    {%- if preferred_choices|length > 0 -%}
      {% set options = preferred_choices %}
      {{- block('choice_widget_options') -}}
      {%- if choices|length > 0 and separator is not none -%}
        <option disabled="disabled">{{ separator }}</option>
      {%- endif -%}
    {%- endif -%}
    {%- set options = choices -%}
    {{- block('choice_widget_options') -}}
  </select>
{%- endblock choice_widget_collapsed -%}

{% block button_widget -%}
  {%- set attr = attr|merge({class: (attr.class|default('btn-outline-secondary') ~ ' btn')|trim}) -%}
  {{- parent() -}}
{%- endblock button_widget %}

{% block submit_widget -%}
  {%- set attr = attr|merge({class: (attr.class|default('btn-outline-primary'))|trim}) -%}
  {{- parent() -}}
{%- endblock submit_widget %}

{#
{% block form_group_class -%}
col-auto
{%- endblock form_group_class %}
#}
{#
{% block form_label_class -%}

{%- endblock form_label_class %}
#}

{%- block widget_container_attributes -%}

  {%- if id is not empty %}id="{{ id }}"{% endif -%}

  {{ block('attributes') }}
  class="form-container"

{%- endblock widget_container_attributes -%}

{% block button_row -%}
  <div class="form-group form-submit">
    {{- form_widget(form) -}}
  </div>
{%- endblock button_row %}

{#{%- block checkbox_widget -%}#}
{#      <div class="custom-control custom-checkbox mb-3">#}
{#      <input type="checkbox" class="custom-control-input" id="customCheck" name="{{ block('widget_attributes') }}" {% if value is defined %} value="{{ value }}" {% endif %} {% if checked %} checked="checked" {% endif %}">#}
{#      <label class="custom-control-label" for="customCheck">{{label}}</label>#}
{#    </div>#}
{#{%- endblock checkbox_widget -%}#}
