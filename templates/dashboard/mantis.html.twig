{% extends 'base-trelloLike.html.twig' %}

{% block title %}
    Mantis Dashboard
{% endblock %}

{% block body %}
    <div class="container page__heading-container">
        <div class="page__heading d-flex align-items-center">
            <div class="flex">
                <h1 class="m-0">Mantis Dashboard</h1>

                <div class="mt-3 form-group">
                    <label class="select-label" for="projects">Choisissez un projet :</label>
                    <select id="projects" class="form-control" style="max-width: 50%">
                        {% for project in projects %}
                            {% if project.subProjects is defined %}
                                <optgroup label="{{ project.name }}">
                                    {% for subProject in project.subProjects %}
                                        <option value="{{ subProject.id }}" {% if projectId == subProject.id  %}selected="selected"{% endif %}>
                                            {{ subProject.name }}
                                        </option>
                                    {% endfor %}
                                </optgroup>
                            {% else %}
                                <option value="{{ project.id }}" {% if projectId == project.id  %}selected="selected"{% endif %}>
                                    {{ project.name }}
                                </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>

            </div>
            <button type="button" class="btn btn-success ml-3" data-toggle="modal" data-target="#modal-large">
                Nouveau ticket
            </button>
        </div>
    </div>

    <div class="trello-container">
        <div class="trello-board container-fluid page__container">
            {% if issues|length > 0 %}
                {% for status, listIssues in issues %}
                    <div class="trello-board__tasks">
                        <div class="card bg-light border">
                            <div class="card-header card-header-sm bg-white">
                                <h4 class="card-header__title">{{ status|capitalize }} ({{ listIssues|length }})</h4>
                            </div>
                            <div class="card-body p-2">
                                <div class="trello-board__tasks-list card-list" id="trello-tasks-{{ loop.index }}" data-status="{{ status }}">
                                    {% for issue in listIssues %}
                                        <div class="trello-board__tasks-item card shadow-none border" data-issue="{{ issue.id }}">
                                            <div class="p-3">
                                                <p class="m-0 d-flex align-items-center">
                                                    <strong><a href="{{ mantis_api_url }}view.php?id={{ issue.id }}" target="_blank">{{ issue.summary }}</a></strong>
                                                    <span class="badge {{ issue.severity.id > 50 ? 'badge-danger' : (status == 'resolved' ? 'badge-success' : 'badge-light-gray') }} ml-auto">{{ status == 'resolved' ? status|upper : issue.severity.label|upper }}</span>
                                                </p>

                                                <p class="d-flex align-items-center mb-2">
                                                    <i class="material-icons icon-16pt mr-2 text-muted">folder_open</i>
                                                    <span class="text-muted mr-3">{{ issue.category.name }}</span>
                                                    {% if issue.notes is defined %}
                                                        <i class="material-icons icon-16pt mr-2 text-muted">comment</i>
                                                        <a href="{{ mantis_api_url }}view.php?id={{ issue.id }}#bugnotes"
                                                           target="_blank" class="text-muted">
                                                            <strong>{{ issue.notes|length }}</strong> comment(s)
                                                        </a>
                                                    {% endif %}
                                                </p>
                                                <div class="media align-items-center">
                                                    <div class="media-body">

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <button class="btn btn-light btn-block mt-2" data-toggle="modal" data-target="#modal-large">+ Ajouter un ticket</button>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block modals %}
    <div id="modal-large" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="modal-large-title" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-large-title">Création d'un nouveau ticket sur mantis</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div> <!-- // END .modal-header -->
                <form action="{{ path('app_mantis_createissue') }}" method="POST">
                    <div class="modal-body">
                            <div class="row">
                                <div class="col">
                                    <input type="text" class="form-control form-control-lg" required="required"
                                           name="issue[summary]" placeholder="Résumé">
                                </div>
                                <div class="col">
                                    <input type="hidden" name="issue[project]" value="{{ projectId }}">
                                    <select class="form-control form-control-lg" disabled>
                                        {% for project in projects %}
                                            {% if project.subProjects is defined %}
                                                <optgroup label="{{ project.name }}">
                                                    {% for subProject in project.subProjects %}
                                                        <option value="{{ subProject.id }}" {% if projectId == subProject.id  %}selected="selected"{% endif %}>
                                                            {{ subProject.name }}
                                                        </option>
                                                    {% endfor %}
                                                </optgroup>
                                            {% else %}
                                                <option value="{{ project.id }}" {% if projectId == project.id  %}selected="selected"{% endif %}>
                                                    {{ project.name }}
                                                </option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col">
                                    <textarea class="form-control form-control-lg" required="required"
                                              name="issue[description]" rows="10" placeholder="Description"></textarea>
                                </div>
                            </div>

                    </div> <!-- // END .modal-body -->
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">Fermer</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div> <!-- // END .modal-footer -->
                </form>
            </div> <!-- // END .modal-content -->
        </div> <!-- // END .modal-dialog -->
    </div> <!-- // END .modal -->
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">
        $(function() {
            let projectSelector = $('#projects');
            projectSelector.on('change', function() {
                window.location.href = '{{ path('project_view') }}/' + $(this).val();
            });

            let dragulaContainers = [
                document.getElementById('trello-tasks-1'),
                document.getElementById('trello-tasks-2'),
                document.getElementById('trello-tasks-3'),
                document.getElementById('trello-tasks-4')
            ];

            dragula(dragulaContainers).on('drop', function (el) {
                $.ajax({
                    url: '{{ path('app_mantis_dropissue') }}',
                    data: {
                        issueId: el.dataset.issue,
                        status: el.parentNode.dataset.status
                    }
                });
            });
        });
    </script>
{% endblock %}