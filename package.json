{"devDependencies": {"@hotwired/stimulus": "^3.0.0", "@symfony/stimulus-bridge": "^3.0.0", "@symfony/webpack-encore": "^1.7.0", "core-js": "^3.0.0", "file-loader": "^6.0.0", "regenerator-runtime": "^0.13.2", "sass": "^1.77.8", "sass-loader": "^12.0.0", "vue": "^2.5", "vue-loader": "^15.9.5", "vue-template-compiler": "^2.7.16", "webpack-notifier": "^1.6.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"@popperjs/core": "^2.11.8", "bootstrap": "4.3", "datatables.net": "^2.1.3", "datatables.net-dt": "^2.1.3", "fullcalendar": "^3.10.0", "jquery": "^3.7.1", "moment": "^2.24.0", "popper.js": "^1.16.1", "select2": "^4.1.0-rc.0", "select2-bootstrap-theme": "^0.1.0-beta.10", "simple-line-icons": "^2.5.5"}}