<?php

require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load(__DIR__ . '/.env');

// Créer le kernel Symfony
$kernel = new \App\Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

try {
    // Récupérer le service WeeklyReportService
    $weeklyReportService = $container->get('App\Service\WeeklyReportService');
    
    // Tester la génération du rapport
    $report = $weeklyReportService->getWeeklyReport();
    
    echo "Rapport généré avec succès !\n";
    echo "Nombre de lignes dans le rapport : " . count($report['report']) . "\n";
    
    // Afficher les premières lignes pour vérifier la structure
    echo "\nPremières lignes du rapport :\n";
    foreach (array_slice($report['report'], 0, 5) as $i => $line) {
        echo "Ligne " . ($i + 1) . " : ";
        echo "Nom: " . ($line['nom'] ?? 'N/A') . ", ";
        echo "Prénom: " . ($line['prenom'] ?? 'N/A') . ", ";
        echo "Client: " . ($line['client'] ?? 'N/A') . ", ";
        echo "Temps: " . ($line['temps_passe'] ?? 'N/A') . ", ";
        echo "IT Room: " . ($line['temps_itroom'] ?? 'N/A') . "\n";
    }
    
} catch (Exception $e) {
    echo "Erreur lors du test : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}
