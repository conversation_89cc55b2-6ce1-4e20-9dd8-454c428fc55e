{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f7d48d57b0ee1471d190c4c1949efea9", "packages": [{"name": "api-platform/core", "version": "v2.4.7", "source": {"type": "git", "url": "https://github.com/api-platform/core.git", "reference": "53793261113b18931ce8a9e2f174f59fdaa0c1c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/api-platform/core/zipball/53793261113b18931ce8a9e2f174f59fdaa0c1c5", "reference": "53793261113b18931ce8a9e2f174f59fdaa0c1c5", "shasum": ""}, "require": {"doctrine/inflector": "^1.0", "php": ">=7.1", "psr/cache": "^1.0", "psr/container": "^1.0", "symfony/http-foundation": "^3.4 || ^4.0", "symfony/http-kernel": "^3.4 || ^4.0", "symfony/property-access": "^3.4 || ^4.0", "symfony/property-info": "^3.4 || ^4.0", "symfony/serializer": "^4.2.6", "symfony/web-link": "^4.1", "willdurand/negotiation": "^2.0.3"}, "conflict": {"doctrine/common": "<2.7", "doctrine/mongodb-odm": "<2.0"}, "require-dev": {"behat/behat": "^3.1", "behat/mink": "^1.7", "behat/mink-browserkit-driver": "^1.3.1", "behat/mink-extension": "^2.2", "behat/symfony2-extension": "^2.1.1", "behatch/contexts": "3.1.0", "doctrine/annotations": "^1.7", "doctrine/data-fixtures": "^1.2.2", "doctrine/doctrine-bundle": "^1.8", "doctrine/doctrine-cache-bundle": "^1.3.5", "doctrine/mongodb-odm": "^2.0@beta", "doctrine/mongodb-odm-bundle": "^4.0@beta", "doctrine/orm": "^2.6.3", "elasticsearch/elasticsearch": "^6.0", "friendsofsymfony/user-bundle": "^2.2@dev", "guzzlehttp/guzzle": "^6.0", "jangregor/phpstan-prophecy": "^0.4.2", "justinrainbow/json-schema": "^5.0", "nelmio/api-doc-bundle": "^2.13.4", "phpdocumentor/reflection-docblock": "^3.0 || ^4.0", "phpdocumentor/type-resolver": "^0.3 || ^0.4", "phpspec/prophecy": "^1.8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.11.10", "phpstan/phpstan-doctrine": "^0.11.5", "phpstan/phpstan-phpunit": "^0.11.2", "phpstan/phpstan-symfony": "^0.11.6", "phpunit/phpunit": "^7.5.2 || ^8.0", "psr/log": "^1.0", "ramsey/uuid": "^3.7", "ramsey/uuid-doctrine": "^1.4", "sebastian/object-enumerator": "^3.0.3", "symfony/asset": "^3.4 || ^4.0", "symfony/cache": "^3.4 || ^4.0", "symfony/config": "^3.4 || ^4.0", "symfony/console": "^3.4 || ^4.0", "symfony/css-selector": "^3.4 || ^4.0", "symfony/debug": "^3.4 || ^4.0", "symfony/dependency-injection": "^3.4 || ^4.0", "symfony/doctrine-bridge": "^3.4 || ^4.0", "symfony/dom-crawler": "^3.4 || ^4.0", "symfony/event-dispatcher": "^3.4 || ^4.0", "symfony/expression-language": "^3.4 || ^4.0", "symfony/finder": "^3.4 || ^4.0", "symfony/form": "^3.4 || ^4.0", "symfony/framework-bundle": "^4.2", "symfony/mercure-bundle": "*", "symfony/messenger": "^4.2", "symfony/phpunit-bridge": "^4.3@dev", "symfony/routing": "^3.4 || ^4.0", "symfony/security-bundle": "^3.4 || ^4.0", "symfony/security-core": "^3.4 || ^4.0", "symfony/twig-bundle": "^3.4 || ^4.0", "symfony/validator": "^3.4 || ^4.0", "symfony/web-profiler-bundle": "^4.2", "symfony/yaml": "^3.4 || ^4.0", "twig/twig": "^1.41 || ^2.10", "webonyx/graphql-php": ">=0.13 <1.0"}, "suggest": {"doctrine/mongodb-odm-bundle": "To support MongoDB. Only versions 4.0 and later are supported.", "elasticsearch/elasticsearch": "To support Elasticsearch.", "friendsofsymfony/user-bundle": "To use the FOSUserBundle bridge.", "guzzlehttp/guzzle": "To use the HTTP cache invalidation system.", "phpdocumentor/reflection-docblock": "To support extracting metadata from PHPDoc.", "psr/cache-implementation": "To use metadata caching.", "ramsey/uuid": "To support <PERSON>'s UUID identifiers.", "symfony/cache": "To have metadata caching when using Symfony integration.", "symfony/config": "To load XML configuration files.", "symfony/expression-language": "To use authorization features.", "symfony/security": "To use authorization features.", "symfony/twig-bundle": "To use the Swagger UI integration.", "symfony/web-profiler-bundle": "To use the data collector.", "webonyx/graphql-php": "To support GraphQL."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "autoload": {"psr-4": {"ApiPlatform\\Core\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dunglas.fr"}], "description": "Build a fully-featured hypermedia or GraphQL API in minutes", "homepage": "https://api-platform.com", "keywords": ["Hydra", "JSON-LD", "api", "graphql", "hal", "jsonapi", "openapi", "rest", "swagger"], "support": {"issues": "https://github.com/api-platform/core/issues", "source": "https://github.com/api-platform/core/tree/v2.4.7"}, "time": "2019-09-17T14:41:55+00:00"}, {"name": "components/jquery", "version": "v3.7.1", "source": {"type": "git", "url": "https://github.com/components/jquery.git", "reference": "8edc7785239bb8c2ad2b83302b856a1d61de60e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/jquery/zipball/8edc7785239bb8c2ad2b83302b856a1d61de60e7", "reference": "8edc7785239bb8c2ad2b83302b856a1d61de60e7", "shasum": ""}, "type": "component", "extra": {"component": {"scripts": ["jquery.js"], "files": ["jquery.min.js", "jquery.min.map", "jquery.slim.js", "jquery.slim.min.js", "jquery.slim.min.map"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "JS Foundation and other contributors"}], "description": "jQuery JavaScript Library", "homepage": "http://jquery.com", "support": {"forum": "http://forum.jquery.com", "irc": "irc://irc.freenode.org/jquery", "issues": "https://github.com/jquery/jquery/issues", "source": "https://github.com/jquery/jquery", "wiki": "http://docs.jquery.com/"}, "time": "2023-09-22T01:43:46+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.11.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "28101e20776d8fa20a00b54947fbae2db0d09103"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/28101e20776d8fa20a00b54947fbae2db0d09103", "reference": "28101e20776d8fa20a00b54947fbae2db0d09103", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12", "doctrine/doctrine-cache-bundle": "~1.2", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1", "symfony/config": "^3.4|^4.1", "symfony/console": "^3.4|^4.1", "symfony/dependency-injection": "^3.4|^4.1", "symfony/doctrine-bridge": "^3.4|^4.1", "symfony/framework-bundle": "^3.4|^4.1"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "7.0", "symfony/cache": "^3.4|^4.1", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^3.4|^4.1", "symfony/validator": "^3.4|^4.1", "symfony/web-profiler-bundle": "^3.4|^4.1", "symfony/yaml": "^3.4|^4.1", "twig/twig": "^1.34|^2.4"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/1.11.x"}, "time": "2019-06-04T07:35:05+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/6bee2f9b339847e8a984427353670bad4e7bdccb", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "^1.0", "php": "^7.1", "symfony/doctrine-bridge": "^3.4|^4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "^7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/security-acl": "^2.8", "symfony/validator": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "support": {"issues": "https://github.com/doctrine/DoctrineCacheBundle/issues", "source": "https://github.com/doctrine/DoctrineCacheBundle/tree/1.4.0"}, "abandoned": true, "time": "2019-11-29T11:22:01+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "0a9a4ffdb8713d0b7f2bbc04b0bbfa2991b2a238"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/0a9a4ffdb8713d0b7f2bbc04b0bbfa2991b2a238", "reference": "0a9a4ffdb8713d0b7f2bbc04b0bbfa2991b2a238", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0|~2.0", "doctrine/migrations": "~3.0", "php": "^7.2|^8.0", "symfony/framework-bundle": "~3.4|~4.0|~5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.0.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-migrations-bundle", "type": "tidelift"}], "time": "2021-03-18T21:01:51+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/migrations", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "69eaf2ca5bc48357b43ddbdc31ccdffc0e2a0882"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/69eaf2ca5bc48357b43ddbdc31ccdffc0e2a0882", "reference": "69eaf2ca5bc48357b43ddbdc31ccdffc0e2a0882", "shasum": ""}, "require": {"doctrine/dbal": "^2.10", "doctrine/event-manager": "^1.0", "ocramius/package-versions": "^1.3", "ocramius/proxy-manager": "^2.0.2", "php": "^7.2", "psr/log": "^1.1.3", "symfony/console": "^3.4||^4.0||^5.0", "symfony/stopwatch": "^3.4||^4.0||^5.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "doctrine/orm": "^2.6", "doctrine/persistence": "^1.3||^2.0", "doctrine/sql-formatter": "^1.0", "ext-pdo_sqlite": "*", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-symfony": "^0.12", "phpunit/phpunit": "^8.4", "symfony/process": "^3.4||^4.0||^5.0", "symfony/yaml": "^3.4||^4.0||^5.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations", "php"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2020-06-21T08:55:42+00:00"}, {"name": "doctrine/orm", "version": "2.7.5", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "01187c9260cd085529ddd1273665217cae659640"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/01187c9260cd085529ddd1273665217cae659640", "reference": "01187c9260cd085529ddd1273665217cae659640", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.11.1", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11 || ^3.0", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/inflector": "^1.0", "doctrine/instantiator": "^1.3", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3 || ^2.0", "ext-pdo": "*", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^8.0", "symfony/yaml": "^3.4|^4.0|^5.0", "vimeo/psalm": "^3.11"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.7.5"}, "time": "2020-12-03T08:52:14+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "shasum": ""}, "require": {"doctrine/annotations": "^1.0 || ^2.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.4"}, "abandoned": "roave/better-reflection", "time": "2023-07-27T18:11:59+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.17.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "bbc513d79acf6691fa9cf10f192c90dd2957f18c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/bbc513d79acf6691fa9cf10f192c90dd2957f18c", "reference": "bbc513d79acf6691fa9cf10f192c90dd2957f18c", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.17.0"}, "time": "2023-11-17T15:01:25+00:00"}, {"name": "fig/link-util", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/link-util.git", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link-util/zipball/5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "shasum": ""}, "require": {"php": ">=5.5.0", "psr/link": "~1.0@dev"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "^5.1", "squizlabs/php_codesniffer": "^2.3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Fig\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common utility implementations for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"issues": "https://github.com/php-fig/link-util/issues", "source": "https://github.com/php-fig/link-util/tree/1.1.2"}, "time": "2021-02-03T23:36:04+00:00"}, {"name": "firebase/php-jwt", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.4.0"}, "time": "2023-02-09T21:01:23+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}, "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "google/apiclient", "version": "v2.14.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "789c8b07cad97f420ac0467c782036f955a2ad89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/789c8b07cad97f420ac0467c782036f955a2ad89", "reference": "789c8b07cad97f420ac0467c782036f955a2ad89", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0||~6.0", "google/apiclient-services": "~0.200", "google/auth": "^1.10", "guzzlehttp/guzzle": "~5.3.3||~6.0||~7.0", "guzzlehttp/psr7": "^1.8.4||^2.2.1", "monolog/monolog": "^1.17||^2.0||^3.0", "php": "^5.6|^7.0|^8.0", "phpseclib/phpseclib": "~2.0||^3.0.2"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2|^1.1", "composer/composer": "^1.10.22", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.14.0"}, "time": "2023-05-11T21:54:55+00:00"}, {"name": "google/apiclient-services", "version": "v0.302.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "ac872f59a7b4631b12628fe990c167d18a71c783"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/ac872f59a7b4631b12628fe990c167d18a71c783", "reference": "ac872f59a7b4631b12628fe990c167d18a71c783", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7||^8.5.13"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.302.0"}, "time": "2023-05-16T01:08:12+00:00"}, {"name": "google/auth", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "shasum": ""}, "require": {"firebase/php-jwt": "^5.5||^6.0", "guzzlehttp/guzzle": "^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": "^7.1||^8.0", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "0.7.0", "phpseclib/phpseclib": "^2.0.31||^3.0", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^7.5||^9.0.0", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.26.0"}, "time": "2023-04-05T15:11:57+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-07-18T10:29:17+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/jdorn/sql-formatter/issues", "source": "https://github.com/jdorn/sql-formatter/tree/v1.2.17"}, "time": "2014-01-12T16:20:24+00:00"}, {"name": "knpuniversity/oauth2-client-bundle", "version": "v1.34.0", "source": {"type": "git", "url": "https://github.com/knpuniversity/oauth2-client-bundle.git", "reference": "fd892fe2d4752116f09c85d1a3a34af91e1ddade"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/knpuniversity/oauth2-client-bundle/zipball/fd892fe2d4752116f09c85d1a3a34af91e1ddade", "reference": "fd892fe2d4752116f09c85d1a3a34af91e1ddade", "shasum": ""}, "require": {"league/oauth2-client": "^1.0|^2.0", "php": "^7.1.3", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0"}, "require-dev": {"league/oauth2-facebook": "^1.1|^2.0", "phpspec/prophecy": "^1.8", "phpstan/phpstan": "^0.11.16", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/security-guard": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/security-guard": "For integration with Symfony's Guard Security layer"}, "type": "symfony-bundle", "autoload": {"psr-4": {"KnpU\\OAuth2ClientBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Integration with league/oauth2-client to provide services", "homepage": "http://knpuniversity.com", "keywords": ["o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/knpuniversity/oauth2-client-bundle/issues", "source": "https://github.com/knpuniversity/oauth2-client-bundle/tree/master"}, "time": "2020-03-19T10:26:26+00:00"}, {"name": "lcobucci/jwt", "version": "3.4.6", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "3ef8657a78278dfeae7707d51747251db4176240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/3ef8657a78278dfeae7707d51747251db4176240", "reference": "3ef8657a78278dfeae7707d51747251db4176240", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"lcobucci/clock": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["compat/class-aliases.php", "compat/json-exception-polyfill.php", "compat/lcobucci-clock-polyfill.php"], "psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/3.4.6"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-09-28T19:18:28+00:00"}, {"name": "league/oauth2-client", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/160d6274b03562ebeb55ed18399281d8118b76c8", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.5", "squizlabs/php_codesniffer": "^2.3 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.7.0"}, "time": "2023-04-16T18:19:15+00:00"}, {"name": "league/oauth2-google", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-google.git", "reference": "6b79441f244040760bed5fdcd092a2bda7cf34c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-google/zipball/6b79441f244040760bed5fdcd092a2bda7cf34c6", "reference": "6b79441f244040760bed5fdcd092a2bda7cf34c6", "shasum": ""}, "require": {"league/oauth2-client": "^2.0"}, "require-dev": {"eloquent/phony-phpunit": "^2.0", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^6.0", "squizlabs/php_codesniffer": "^2.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://shadowhand.me"}], "description": "Google OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "google", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/thephpleague/oauth2-google/issues", "source": "https://github.com/thephpleague/oauth2-google/tree/3.0.4"}, "time": "2021-01-27T16:09:03+00:00"}, {"name": "lexik/jwt-authentication-bundle", "version": "v2.10.7", "source": {"type": "git", "url": "https://github.com/lexik/LexikJWTAuthenticationBundle.git", "reference": "79ba5af396c4f4e64fe9c8b9af65f8441fdb44cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lexik/LexikJWTAuthenticationBundle/zipball/79ba5af396c4f4e64fe9c8b9af65f8441fdb44cf", "reference": "79ba5af396c4f4e64fe9c8b9af65f8441fdb44cf", "shasum": ""}, "require": {"ext-openssl": "*", "lcobucci/jwt": "^3.2|^4.0", "namshi/jose": "^7.2", "php": ">=5.6", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/security-bundle": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/browser-kit": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"gesdinet/jwt-refresh-token-bundle": "Implements a refresh token system over Json Web Tokens in Symfony", "spomky-labs/lexik-jose-bridge": "Provides a JWT Token encoder with encryption support"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Lexik\\Bundle\\JWTAuthenticationBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/jeremyb"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/slashfan"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cedric-g"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lexik"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/chalasr"}, {"name": "Lexik Community", "homepage": "https://github.com/lexik/LexikJWTAuthenticationBundle/graphs/contributors"}], "description": "This bundle provides JWT authentication for your Symfony REST API", "homepage": "https://github.com/lexik/LexikJWTAuthenticationBundle", "keywords": ["Authentication", "JWS", "api", "bundle", "jwt", "rest", "symfony"], "support": {"issues": "https://github.com/lexik/LexikJWTAuthenticationBundle/issues", "source": "https://github.com/lexik/LexikJWTAuthenticationBundle/tree/v2.10.7"}, "funding": [{"url": "https://github.com/chalasr", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/lexik/jwt-authentication-bundle", "type": "tidelift"}], "time": "2021-05-12T09:32:35+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.1.0"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "namshi/jose", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/namshi/jose.git", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/namshi/jose/zipball/89a24d7eb3040e285dd5925fcad992378b82bcff", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff", "shasum": ""}, "require": {"ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "php": ">=5.5", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpseclib/phpseclib": "^2.0", "phpunit/phpunit": "^4.5|^5.0", "satooshi/php-coveralls": "^1.0"}, "suggest": {"ext-openssl": "Allows to use OpenSSL as crypto engine.", "phpseclib/phpseclib": "Allows to use Phpseclib as crypto engine, use version ^2.0."}, "type": "library", "autoload": {"psr-4": {"Namshi\\JOSE\\": "src/Namshi/JOSE/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (cirpo)", "email": "<EMAIL>"}], "description": "JSON Object Signing and Encryption library for PHP.", "keywords": ["JSON Web Signature", "JSON Web Token", "JWS", "json", "jwt", "token"], "support": {"issues": "https://github.com/namshi/jose/issues", "source": "https://github.com/namshi/jose/tree/master"}, "time": "2016-12-05T07:27:31+00:00"}, {"name": "nelmio/cors-bundle", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioCorsBundle.git", "reference": "10a24c10f242440211ed31075e74f81661c690d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioCorsBundle/zipball/10a24c10f242440211ed31075e74f81661c690d9", "reference": "10a24c10f242440211ed31075e74f81661c690d9", "shasum": ""}, "require": {"symfony/framework-bundle": "^2.7 || ^3.0 || ^4.0"}, "require-dev": {"matthiasnoback/symfony-dependency-injection-test": "^1.0 || ^2.0", "mockery/mockery": "^0.9 || ^1.0", "symfony/phpunit-bridge": "^2.7 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\CorsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioCorsBundle/contributors"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Symfony2 application", "keywords": ["api", "cors", "crossdomain"], "support": {"issues": "https://github.com/nelmio/NelmioCorsBundle/issues", "source": "https://github.com/nelmio/NelmioCorsBundle/tree/1.5.6"}, "time": "2019-06-17T08:53:14+00:00"}, {"name": "ocramius/proxy-manager", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/Ocramius/ProxyManager.git", "reference": "2d7cd2a79cd3ade90c46211baae1b88d47683917"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/2d7cd2a79cd3ade90c46211baae1b88d47683917", "reference": "2d7cd2a79cd3ade90c46211baae1b88d47683917", "shasum": ""}, "require": {"ocramius/package-versions": "^1.1.3", "php": "^7.2.0", "zendframework/zend-code": "^3.3.0"}, "require-dev": {"couscous/couscous": "^1.6.1", "ext-phar": "*", "humbug/humbug": "1.0.0-RC.0@RC", "nikic/php-parser": "^3.1.1", "padraic/phpunit-accelerator": "dev-master@DEV", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "dev-master#856eb10a81c1d27c701a83f167dc870fd8f4236a as 0.9.999", "phpstan/phpstan-phpunit": "dev-master#5629c0a1f4a9c417cb1077cf6693ad9753895761", "phpunit/phpunit": "^6.4.3", "squizlabs/php_codesniffer": "^2.9.1"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "homepage": "https://github.com/Ocramius/ProxyManager", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.2.4"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:15:28+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:18:48+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.19.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.2 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^2.18", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^0.12.82", "phpstan/phpstan-phpunit": "^0.12.18", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.19.0"}, "time": "2021-10-31T15:09:20+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/master"}, "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.3.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "5f75c4658b03301cba17baa15a840b57b72b4262"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/5f75c4658b03301cba17baa15a840b57b72b4262", "reference": "5f75c4658b03301cba17baa15a840b57b72b4262", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/persistence": "^1.0", "php": ">=7.1.3", "symfony/config": "^3.4|^4.2", "symfony/dependency-injection": "^3.4|^4.2", "symfony/framework-bundle": "^3.4|^4.2", "symfony/http-kernel": "^3.4|^4.2"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^3.4|^4.2", "symfony/dom-crawler": "^3.4|^4.2", "symfony/expression-language": "^3.4|^4.2", "symfony/finder": "^3.4|^4.2", "symfony/monolog-bridge": "^3.0|^4.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^3.4|^4.2", "symfony/twig-bundle": "^3.4|^4.2", "symfony/yaml": "^3.4|^4.2", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.3.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.3.1"}, "abandoned": "Symfony", "time": "2019-04-10T06:00:20+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/asset", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "82f5349982967842aeb7d2f8e876ac900be24f14"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/82f5349982967842aeb7d2f8e876ac900be24f14", "reference": "82f5349982967842aeb7d2f8e876ac900be24f14", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/http-foundation": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v4.2.10"}, "time": "2019-05-30T16:06:08+00:00"}, {"name": "symfony/cache", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "28286385757fa08c249254541ad814a8ccaff0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/28286385757fa08c249254541ad814a8ccaff0f7", "reference": "28286385757fa08c249254541ad814a8ccaff0f7", "shasum": ""}, "require": {"php": "^7.1.3", "psr/cache": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/contracts": "^1.0", "symfony/var-exporter": "^4.2"}, "conflict": {"doctrine/dbal": "<2.5", "symfony/dependency-injection": "<3.4", "symfony/var-dumper": "<3.4"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "~1.6", "doctrine/dbal": "~2.5", "predis/predis": "~1.1", "symfony/config": "~4.2", "symfony/dependency-injection": "~3.4|~4.1", "symfony/var-dumper": "^4.1.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/4.2"}, "time": "2019-11-11T12:44:10+00:00"}, {"name": "symfony/config", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "637588756df1fb1c801833aead54a7153967c0cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/637588756df1fb1c801833aead54a7153967c0cb", "reference": "637588756df1fb1c801833aead54a7153967c0cb", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/filesystem": "~3.4|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<3.4"}, "require-dev": {"symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/finder": "~3.4|~4.0", "symfony/messenger": "~4.1", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v4.2.11"}, "time": "2019-07-18T10:29:22+00:00"}, {"name": "symfony/console", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "fc2e274aade6567a750551942094b2145ade9b6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/fc2e274aade6567a750551942094b2145ade9b6c", "reference": "fc2e274aade6567a750551942094b2145ade9b6c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.4|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/4.2"}, "time": "2019-07-24T17:13:20+00:00"}, {"name": "symfony/contracts", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/contracts.git", "reference": "e3cc841da1eaf10088250a10618aa223eeb82280"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/contracts/zipball/e3cc841da1eaf10088250a10618aa223eeb82280", "reference": "e3cc841da1eaf10088250a10618aa223eeb82280", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0"}, "replace": {"symfony/cache-contracts": "self.version", "symfony/event-dispatcher-contracts": "self.version", "symfony/http-client-contracts": "self.version", "symfony/service-contracts": "self.version", "symfony/translation-contracts": "self.version"}, "require-dev": {"symfony/polyfill-intl-idn": "^1.10"}, "suggest": {"psr/event-dispatcher": "When using the EventDispatcher contracts", "symfony/cache-implementation": "", "symfony/event-dispatcher-implementation": "", "symfony/http-client-implementation": "", "symfony/service-implementation": "", "symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\": ""}, "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A set of abstractions extracted out of the Symfony components", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/debug", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "f832ecacfb00688b498eedf9b10784e401d969a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/f832ecacfb00688b498eedf9b10784e401d969a6", "reference": "f832ecacfb00688b498eedf9b10784e401d969a6", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "~3.4|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.2.12"}, "abandoned": "symfony/error-handler", "time": "2019-07-23T09:50:15+00:00"}, {"name": "symfony/dependency-injection", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "39fe71bc481483f255e388a658c8f1104fec037e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/39fe71bc481483f255e388a658c8f1104fec037e", "reference": "39fe71bc481483f255e388a658c8f1104fec037e", "shasum": ""}, "require": {"php": "^7.1.3", "psr/container": "^1.0", "symfony/contracts": "^1.1.1"}, "conflict": {"symfony/config": "<4.2", "symfony/finder": "<3.4", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0"}, "require-dev": {"symfony/config": "~4.2", "symfony/expression-language": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/4.2"}, "time": "2019-07-19T12:05:51+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "5e8d62453eda38a523a12cc918031cc8f48e4b65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/5e8d62453eda38a523a12cc918031cc8f48e4b65", "reference": "5e8d62453eda38a523a12cc918031cc8f48e4b65", "shasum": ""}, "require": {"doctrine/collections": "~1.0", "doctrine/event-manager": "~1.0", "doctrine/persistence": "~1.0", "php": "^7.1.3", "symfony/contracts": "^1.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/messenger": "<4.2"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.6", "doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/orm": "^2.4.5", "doctrine/reflection": "~1.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/form": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/messenger": "~4.2", "symfony/property-access": "~3.4|~4.0", "symfony/property-info": "~3.4|~4.0", "symfony/proxy-manager-bridge": "~3.4|~4.0", "symfony/security": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/translation": "~3.4|~4.0", "symfony/validator": "~3.4|~4.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v4.2.12"}, "time": "2019-07-27T06:08:43+00:00"}, {"name": "symfony/dotenv", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "6163f061011009655da1bc615b38941bc460ef1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/6163f061011009655da1bc615b38941bc460ef1b", "reference": "6163f061011009655da1bc615b38941bc460ef1b", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/process": "~3.4|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v4.2.10"}, "time": "2019-06-26T06:46:55+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "852548c7c704f14d2f6700c8d872a05bd2028732"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/852548c7c704f14d2f6700c8d872a05bd2028732", "reference": "852548c7c704f14d2f6700c8d872a05bd2028732", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.2"}, "time": "2019-06-26T06:46:55+00:00"}, {"name": "symfony/expression-language", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "ea23981c1dee4f2f901fce8345d34614237d57ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/ea23981c1dee4f2f901fce8345d34614237d57ca", "reference": "ea23981c1dee4f2f901fce8345d34614237d57ca", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/cache": "~3.4|~4.0", "symfony/contracts": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ExpressionLanguage Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v4.2.11"}, "time": "2019-05-30T16:06:08+00:00"}, {"name": "symfony/filesystem", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "1bd7aed2932cedd1c2c6cc925831dd8d57ea14bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/1bd7aed2932cedd1c2c6cc925831dd8d57ea14bf", "reference": "1bd7aed2932cedd1c2c6cc925831dd8d57ea14bf", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v4.2.10"}, "time": "2019-06-26T06:46:55+00:00"}, {"name": "symfony/finder", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "cecff7164790b0cd72be2ed20e9591d7140715e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/cecff7164790b0cd72be2ed20e9591d7140715e0", "reference": "cecff7164790b0cd72be2ed20e9591d7140715e0", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/4.2"}, "time": "2019-06-28T12:55:49+00:00"}, {"name": "symfony/flex", "version": "v1.21.7", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "33ce7c1c0fe78f8c3a106f193b6c0a7d82384462"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/33ce7c1c0fe78f8c3a106f193b6c0a7d82384462", "reference": "33ce7c1c0fe78f8c3a106f193b6c0a7d82384462", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/dotenv": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4.12|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v1.21.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-27T10:20:37+00:00"}, {"name": "symfony/form", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "c694ef3befb1e4bba58c33522533ecf1e11fd470"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/c694ef3befb1e4bba58c33522533ecf1e11fd470", "reference": "c694ef3befb1e4bba58c33522533ecf1e11fd470", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/options-resolver": "~4.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "~3.4|~4.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/doctrine-bridge": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<3.4", "symfony/translation": "<4.2", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/security-csrf": "~3.4|~4.0", "symfony/translation": "~4.2", "symfony/validator": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0"}, "suggest": {"symfony/framework-bundle": "For templating with PHP.", "symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Form Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/form/tree/v4.2.12"}, "time": "2019-07-19T16:57:10+00:00"}, {"name": "symfony/framework-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "ac12bd9f104bb2dc319b09426e767ecfa95688da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/ac12bd9f104bb2dc319b09426e767ecfa95688da", "reference": "ac12bd9f104bb2dc319b09426e767ecfa95688da", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/cache": "~4.2", "symfony/config": "~4.2", "symfony/contracts": "^1.0.2", "symfony/debug": "~4.0", "symfony/dependency-injection": "^4.2.5", "symfony/event-dispatcher": "^4.1", "symfony/filesystem": "~3.4|~4.0", "symfony/finder": "~3.4|~4.0", "symfony/http-foundation": "^4.2.5", "symfony/http-kernel": "^4.2", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^4.2.8"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.4", "symfony/console": "<3.4", "symfony/dotenv": "<4.2", "symfony/form": "<4.2", "symfony/messenger": "<4.2", "symfony/property-info": "<3.4", "symfony/serializer": "<4.2", "symfony/stopwatch": "<3.4", "symfony/translation": "<4.2", "symfony/twig-bridge": "<4.1.1", "symfony/validator": "<4.1", "symfony/workflow": "<4.1"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "fig/link-util": "^1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/asset": "~3.4|~4.0", "symfony/browser-kit": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~3.4|~4.0", "symfony/dom-crawler": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/form": "^4.2.3", "symfony/lock": "~3.4|~4.0", "symfony/messenger": "^4.2", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "~3.4|~4.0", "symfony/property-info": "~3.4|~4.0", "symfony/security": "~3.4|~4.0", "symfony/security-core": "~3.4|~4.0", "symfony/security-csrf": "~3.4|~4.0", "symfony/serializer": "^4.2", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~3.4|~4.0", "symfony/translation": "~4.2", "symfony/validator": "^4.1", "symfony/var-dumper": "~3.4|~4.0", "symfony/web-link": "~3.4|~4.0", "symfony/workflow": "^4.1", "symfony/yaml": "~3.4|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v4.2.12"}, "time": "2019-07-24T17:13:20+00:00"}, {"name": "symfony/http-client", "version": "v4.4.51", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "da88e5df8a24e7c229c3df4f19f24ac502b8532b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/da88e5df8a24e7c229c3df4f19f24ac502b8532b", "reference": "da88e5df8a24e7c229c3df4f19f24ac502b8532b", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^1.1.10|^2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1|2.0"}, "require-dev": {"guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4.13", "symfony/process": "^4.2|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.51"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-04T08:52:02+00:00"}, {"name": "symfony/http-foundation", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "2ae778ff4a1f8baba7724db1ca977ada3b796749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/2ae778ff4a1f8baba7724db1ca977ada3b796749", "reference": "2ae778ff4a1f8baba7724db1ca977ada3b796749", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "~3.4|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.2.12"}, "time": "2019-11-11T12:54:47+00:00"}, {"name": "symfony/http-kernel", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "8a7c5ef599466af6e972c705507f815df9c490ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/8a7c5ef599466af6e972c705507f815df9c490ae", "reference": "8a7c5ef599466af6e972c705507f815df9c490ae", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/contracts": "^1.0.2", "symfony/debug": "~3.4|~4.0", "symfony/event-dispatcher": "~4.1", "symfony/http-foundation": "^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<4.2", "symfony/translation": "<4.2", "symfony/var-dumper": "<4.1.1", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~3.4|~4.0", "symfony/dependency-injection": "^4.2", "symfony/dom-crawler": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/finder": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~3.4|~4.0", "symfony/translation": "~4.2", "symfony/var-dumper": "^4.1.1"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.2.12"}, "time": "2019-11-13T08:51:34+00:00"}, {"name": "symfony/inflector", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "275e54941a4f17a471c68d2a00e2513fc1fd4a78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/275e54941a4f17a471c68d2a00e2513fc1fd4a78", "reference": "275e54941a4f17a471c68d2a00e2513fc1fd4a78", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v4.2.6"}, "abandoned": "EnglishInflector from the String component", "time": "2019-01-16T20:31:39+00:00"}, {"name": "symfony/intl", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "94aed330eda5cfe171bbd596d898c065e94c132f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/94aed330eda5cfe171bbd596d898c065e94c132f", "reference": "94aed330eda5cfe171bbd596d898c065e94c132f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-intl-icu": "~1.0"}, "require-dev": {"symfony/filesystem": "~3.4|~4.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A PHP replacement layer for the C intl extension that includes additional data from the ICU library.", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v4.2.12"}, "time": "2019-07-24T14:47:26+00:00"}, {"name": "symfony/monolog-bridge", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "41b01701e23016b920638ed551c53f077daacefd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/41b01701e23016b920638ed551c53f077daacefd", "reference": "41b01701e23016b920638ed551c53f077daacefd", "shasum": ""}, "require": {"monolog/monolog": "~1.19", "php": "^7.1.3", "symfony/contracts": "^1.0", "symfony/http-kernel": "~3.4|~4.0"}, "conflict": {"symfony/console": "<3.4", "symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/security-core": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/event-dispatcher": "Needed when using log messages in console commands.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/4.2"}, "time": "2019-06-13T10:57:15+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/e495f5c7e4e672ffef4357d4a4d85f010802f940", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-06T15:12:11+00:00"}, {"name": "symfony/options-resolver", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "eda69aac1ea1f97a594dd9a5c64b7ff73a37ade2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/eda69aac1ea1f97a594dd9a5c64b7ff73a37ade2", "reference": "eda69aac1ea1f97a594dd9a5c64b7ff73a37ade2", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/4.2"}, "time": "2019-06-13T10:57:15+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v4.4.49", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "b3378a7d305666a69e08457b65af8bca179fbbbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/b3378a7d305666a69e08457b65af8bca179fbbbb", "reference": "b3378a7d305666a69e08457b65af8bca179fbbbb", "shasum": ""}, "require": {"php": ">=5.5.9"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0|<6.4,>=6.0|9.1.2"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0"}, "suggest": {"symfony/error-handler": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v4.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-08T14:39:54+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "808a4be7e0dd7fcb6a2b1ed2ba22dd581402c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/808a4be7e0dd7fcb6a2b1ed2ba22dd581402c5e2", "reference": "808a4be7e0dd7fcb6a2b1ed2ba22dd581402c5e2", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.2.12"}, "time": "2019-05-30T16:06:08+00:00"}, {"name": "symfony/property-access", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "c3532a4bdb785446970148da68e03dc11514e256"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/c3532a4bdb785446970148da68e03dc11514e256", "reference": "c3532a4bdb785446970148da68e03dc11514e256", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "~3.4|~4.0"}, "require-dev": {"symfony/cache": "~3.4|~4.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/4.2"}, "time": "2019-07-24T14:47:26+00:00"}, {"name": "symfony/property-info", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "c5d4e006eb3fb386c5b68cd3b1dbb3f0cc6516df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/c5d4e006eb3fb386c5b68cd3b1dbb3f0cc6516df", "reference": "c5d4e006eb3fb386c5b68cd3b1dbb3f0cc6516df", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "~3.4|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/serializer": "~3.4|~4.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v4.2.11"}, "time": "2019-07-21T17:35:01+00:00"}, {"name": "symfony/routing", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "1174ae15f862a0f2d481c29ba172a70b208c9561"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/1174ae15f862a0f2d481c29ba172a70b208c9561", "reference": "1174ae15f862a0f2d481c29ba172a70b208c9561", "shasum": ""}, "require": {"php": "^7.1.3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "~4.2", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/4.2"}, "time": "2019-06-26T13:53:23+00:00"}, {"name": "symfony/security-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "d731987aabf6b28b86faeb158973b0d0c22a0a09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/d731987aabf6b28b86faeb158973b0d0c22a0a09", "reference": "d731987aabf6b28b86faeb158973b0d0c22a0a09", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/config": "^4.2", "symfony/dependency-injection": "^4.2", "symfony/http-kernel": "^4.1", "symfony/security-core": "~4.2", "symfony/security-csrf": "~4.2", "symfony/security-guard": "~4.2", "symfony/security-http": "^4.2.12"}, "conflict": {"symfony/browser-kit": "<4.2", "symfony/console": "<3.4", "symfony/event-dispatcher": "<3.4", "symfony/framework-bundle": "<4.2", "symfony/twig-bundle": "<4.2", "symfony/var-dumper": "<3.4"}, "require-dev": {"doctrine/doctrine-bundle": "~1.5", "symfony/asset": "~3.4|~4.0", "symfony/browser-kit": "~4.2", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~3.4|~4.0", "symfony/dom-crawler": "~3.4|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/form": "~3.4|~4.0", "symfony/framework-bundle": "~4.2", "symfony/http-foundation": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/translation": "~3.4|~4.0", "symfony/twig-bridge": "~3.4|~4.0", "symfony/twig-bundle": "~4.2", "symfony/validator": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "twig/twig": "~1.34|~2.4"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/4.2"}, "time": "2019-11-12T13:02:45+00:00"}, {"name": "symfony/security-core", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "3ec42b5dbeee143715da686539751ea762dd8564"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/3ec42b5dbeee143715da686539751ea762dd8564", "reference": "3ec42b5dbeee143715da686539751ea762dd8564", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/ldap": "~3.4|~4.0", "symfony/validator": "~3.4|~4.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v4.2.11"}, "time": "2019-06-26T18:27:58+00:00"}, {"name": "symfony/security-csrf", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "ff004ea4d215fd4a740f6d6ca9643ff92326c16c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/ff004ea4d215fd4a740f6d6ca9643ff92326c16c", "reference": "ff004ea4d215fd4a740f6d6ca9643ff92326c16c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "~3.4|~4.0"}, "conflict": {"symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/http-foundation": "~3.4|~4.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/4.2"}, "time": "2019-05-30T16:06:08+00:00"}, {"name": "symfony/security-guard", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "ef6d700e6be1ca75bc2788068c62506ab11461bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/ef6d700e6be1ca75bc2788068c62506ab11461bd", "reference": "ef6d700e6be1ca75bc2788068c62506ab11461bd", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "~3.4.22|^4.2.3", "symfony/security-http": "~3.4|~4.0"}, "require-dev": {"psr/log": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-guard/tree/4.2"}, "time": "2019-07-02T13:59:44+00:00"}, {"name": "symfony/security-http", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "8024422eeaca7b0b33ce2900b2f75e20259de7aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/8024422eeaca7b0b33ce2900b2f75e20259de7aa", "reference": "8024422eeaca7b0b33ce2900b2f75e20259de7aa", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/property-access": "~3.4|~4.0", "symfony/security-core": "~3.4|~4.0"}, "conflict": {"symfony/security-csrf": "<3.4.11|~4.0,<4.0.11"}, "require-dev": {"psr/log": "~1.0", "symfony/routing": "~3.4|~4.0", "symfony/security-csrf": "^3.4.11|^4.0.11"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/4.2"}, "time": "2019-11-12T13:02:45+00:00"}, {"name": "symfony/serializer", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "8a2974c10e8eb8eb2d36a28c1bd68eb0b411cc60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/8a2974c10e8eb8eb2d36a28c1bd68eb0b411cc60", "reference": "8a2974c10e8eb8eb2d36a28c1bd68eb0b411cc60", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.4", "symfony/property-access": "<3.4", "symfony/property-info": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/property-access": "~3.4|~4.0", "symfony/property-info": "^3.4.13|~4.0", "symfony/validator": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/4.2"}, "time": "2019-07-19T12:05:51+00:00"}, {"name": "symfony/stopwatch", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "b1a5f646d56a3290230dbc8edf2a0d62cda23f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/b1a5f646d56a3290230dbc8edf2a0d62cda23f67", "reference": "b1a5f646d56a3290230dbc8edf2a0d62cda23f67", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v4.2.5"}, "time": "2019-01-16T20:31:39+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/defa9bdfc0191ed70b389cb93c550c6c82cf1745", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745", "shasum": ""}, "require": {"php": ">=7.0.0", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "conflict": {"twig/twig": "<1.41|<2.10"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.32|^4.3.5|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/master"}, "abandoned": "symfony/mailer", "time": "2019-11-07T21:01:35+00:00"}, {"name": "symfony/translation", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "4b84015894d980745b510ba90492722cafe2f90f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/4b84015894d980745b510ba90492722cafe2f90f", "reference": "4b84015894d980745b510ba90492722cafe2f90f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.1.1", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.2.12"}, "time": "2019-07-18T10:29:22+00:00"}, {"name": "symfony/twig-bridge", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "708a993aaf3b979738d6e0a12bf157f02fc94998"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/708a993aaf3b979738d6e0a12bf157f02fc94998", "reference": "708a993aaf3b979738d6e0a12bf157f02fc94998", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0.2", "twig/twig": "^1.41|^2.10"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<4.2.4", "symfony/translation": "<4.2"}, "require-dev": {"symfony/asset": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/finder": "~3.4|~4.0", "symfony/form": "^4.2.4", "symfony/http-foundation": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~3.4|~4.0", "symfony/security": "~3.4|~4.0", "symfony/security-acl": "~2.8|~3.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~3.4|~4.0", "symfony/translation": "~4.2", "symfony/var-dumper": "~3.4|~4.0", "symfony/web-link": "~3.4|~4.0", "symfony/workflow": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security": "For using the SecurityExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v4.2.12"}, "time": "2019-07-24T19:56:58+00:00"}, {"name": "symfony/twig-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "db06490aeabba37dfc55a53fbf901c75e0d4f7b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/db06490aeabba37dfc55a53fbf901c75e0d4f7b0", "reference": "db06490aeabba37dfc55a53fbf901c75e0d4f7b0", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "~4.2", "symfony/debug": "~4.0", "symfony/http-foundation": "~4.1", "symfony/http-kernel": "~4.1", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^4.2", "twig/twig": "~1.41|~2.10"}, "conflict": {"symfony/dependency-injection": "<4.1", "symfony/framework-bundle": "<4.1", "symfony/translation": "<4.2"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "symfony/asset": "~3.4|~4.0", "symfony/dependency-injection": "^4.2.5", "symfony/expression-language": "~3.4|~4.0", "symfony/finder": "~3.4|~4.0", "symfony/form": "~3.4|~4.0", "symfony/framework-bundle": "~4.1", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~3.4|~4.0", "symfony/translation": "^4.2", "symfony/web-link": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v4.2.11"}, "time": "2019-07-19T08:33:10+00:00"}, {"name": "symfony/validator", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "7b4485db55b7ea1a0d13d126c2781313017f815f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/7b4485db55b7ea1a0d13d126c2781313017f815f", "reference": "7b4485db55b7ea1a0d13d126c2781313017f815f", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<3.4", "symfony/intl": "<4.1", "symfony/translation": "<4.2", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "egulias/email-validator": "^1.2.8|~2.0", "symfony/cache": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/http-foundation": "~4.1", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~4.1", "symfony/property-access": "~3.4|~4.0", "symfony/translation": "~4.2", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/4.2"}, "time": "2019-07-19T12:05:51+00:00"}, {"name": "symfony/var-exporter", "version": "v4.4.43", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "4a7a3a3d55c471d396e6d28011368b7b83cb518b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/4a7a3a3d55c471d396e6d28011368b7b83cb518b", "reference": "4a7a3a3d55c471d396e6d28011368b7b83cb518b", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v4.4.43"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-27T11:44:32+00:00"}, {"name": "symfony/web-link", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/web-link.git", "reference": "47b8188b4bb8d24eef0bb287b0737d5b84a6cab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-link/zipball/47b8188b4bb8d24eef0bb287b0737d5b84a6cab8", "reference": "47b8188b4bb8d24eef0bb287b0737d5b84a6cab8", "shasum": ""}, "require": {"fig/link-util": "^1.0", "php": "^7.1.3", "psr/link": "^1.0"}, "require-dev": {"symfony/event-dispatcher": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0"}, "suggest": {"symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\WebLink\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebLink Component", "homepage": "https://symfony.com", "keywords": ["dns-prefetch", "http", "http2", "link", "performance", "prefetch", "preload", "prerender", "psr13", "push"], "support": {"source": "https://github.com/symfony/web-link/tree/v4.2.5"}, "time": "2019-01-16T20:31:39+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd", "reference": "c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/asset": "^3.4 || ^4.0 || ^5.0", "symfony/config": "^3.4 || ^4.0 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.0 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.0 || ^5.0", "symfony/service-contracts": "^1.0 || ^2.0"}, "require-dev": {"symfony/framework-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/phpunit-bridge": "^4.3.5 || ^5.0", "symfony/twig-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/web-link": "^3.4 || ^4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"thanks": {"name": "symfony/webpack-encore", "url": "https://github.com/symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration with your Symfony app & Webpack Encore!", "support": {"issues": "https://github.com/symfony/webpack-encore-bundle/issues", "source": "https://github.com/symfony/webpack-encore-bundle/tree/v1.8.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-28T17:07:25+00:00"}, {"name": "symfony/yaml", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "9468fef6f1c740b96935e9578560a9e9189ca154"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/9468fef6f1c740b96935e9578560a9e9189ca154", "reference": "9468fef6f1c740b96935e9578560a9e9189ca154", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/4.2"}, "time": "2019-07-24T14:47:26+00:00"}, {"name": "twbs/bootstrap", "version": "v4.0.0", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "8a628b943cf31ca0a002c08af661a95772480225"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/8a628b943cf31ca0a002c08af661a95772480225", "reference": "8a628b943cf31ca0a002c08af661a95772480225", "shasum": ""}, "replace": {"twitter/bootstrap": "self.version"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "jacobt<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "homepage": "https://getbootstrap.com", "keywords": ["JS", "css", "framework", "front-end", "mobile-first", "responsive", "sass", "web"], "support": {"issues": "https://github.com/twbs/bootstrap/issues", "source": "https://github.com/twbs/bootstrap/tree/v4-dev"}, "time": "2018-01-18T18:29:48+00:00"}, {"name": "twig/twig", "version": "v2.16.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/19185947ec75d433a3ac650af32fc05649b95ee1", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^5.4.9|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.16-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.16.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-09-09T17:53:56+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/03436ededa67c6e83b9b12defac15384cb399dc9", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "support": {"issues": "https://github.com/willdurand/Negotiation/issues", "source": "https://github.com/willdurand/Negotiation/tree/2.x"}, "time": "2017-05-14T17:21:12+00:00"}, {"name": "zendframework/zend-code", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-code.git", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-code/zipball/268040548f92c2bfcba164421c1add2ba43abaaa", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa", "shasum": ""}, "require": {"php": "^7.1", "zendframework/zend-eventmanager": "^2.6 || ^3.0"}, "conflict": {"phpspec/prophecy": "<1.9.0"}, "require-dev": {"doctrine/annotations": "^1.7", "ext-phar": "*", "phpunit/phpunit": "^7.5.16 || ^8.4", "zendframework/zend-coding-standard": "^1.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "zendframework/zend-stdlib": "Zend\\Stdlib component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev", "dev-develop": "3.5.x-dev", "dev-dev-4.0": "4.0.x-dev"}}, "autoload": {"psr-4": {"Zend\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "keywords": ["ZendFramework", "code", "zf"], "support": {"chat": "https://zendframework-slack.herokuapp.com", "docs": "https://docs.zendframework.com/zend-code/", "forum": "https://discourse.zendframework.com/c/questions/components", "issues": "https://github.com/zendframework/zend-code/issues", "rss": "https://github.com/zendframework/zend-code/releases.atom", "source": "https://github.com/zendframework/zend-code"}, "abandoned": "laminas/laminas-code", "time": "2019-12-10T19:21:15+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "a5e2583a211f73604691586b8406ff7296a946dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/a5e2583a211f73604691586b8406ff7296a946dd", "reference": "a5e2583a211f73604691586b8406ff7296a946dd", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev", "dev-develop": "3.3-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "support": {"issues": "https://github.com/zendframework/zend-eventmanager/issues", "source": "https://github.com/zendframework/zend-eventmanager/tree/master"}, "abandoned": "laminas/laminas-eventmanager", "time": "2018-04-25T15:33:34+00:00"}], "packages-dev": [{"name": "doctrine/data-fixtures", "version": "1.5.4", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "d25660094fb25ee139aac11c7f052bea169b3f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/d25660094fb25ee139aac11c7f052bea169b3f88", "reference": "d25660094fb25ee139aac11c7f052bea169b3f88", "shasum": ""}, "require": {"doctrine/common": "^2.13|^3.0", "doctrine/persistence": "^1.3.3|^2.0|^3.0", "php": "^7.2 || ^8.0"}, "conflict": {"doctrine/dbal": "<2.13", "doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"doctrine/coding-standard": "^10.0", "doctrine/dbal": "^2.13 || ^3.0", "doctrine/mongodb-odm": "^1.3.0 || ^2.0.0", "doctrine/orm": "^2.7.0", "ext-sqlite3": "*", "phpstan/phpstan": "^1.5", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^5.0 || ^6.0", "vimeo/psalm": "^4.10"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM 1.3 with PHP 7 (deprecated)", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "lib/Doctrine/Common/DataFixtures"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "https://www.doctrine-project.org", "keywords": ["database"], "support": {"issues": "https://github.com/doctrine/data-fixtures/issues", "source": "https://github.com/doctrine/data-fixtures/tree/1.5.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdata-fixtures", "type": "tidelift"}], "time": "2022-09-20T21:13:12+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "3.2.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "90e4a4f968b2dae40e290a6ee516957af043f16c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/90e4a4f968b2dae40e290a6ee516957af043f16c", "reference": "90e4a4f968b2dae40e290a6ee516957af043f16c", "shasum": ""}, "require": {"doctrine/data-fixtures": "^1.3", "doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.6.0", "php": "^7.1", "symfony/doctrine-bridge": "~3.4|^4.1", "symfony/framework-bundle": "^3.4|^4.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.4", "symfony/phpunit-bridge": "^4.1"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineFixturesBundle/issues", "source": "https://github.com/doctrine/DoctrineFixturesBundle/tree/3.2"}, "time": "2019-06-12T12:03:37+00:00"}, {"name": "liip/test-fixtures-bundle", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/liip/LiipTestFixturesBundle.git", "reference": "bcae25d4b703d1ef1aad833c5dda57202383d48d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/liip/LiipTestFixturesBundle/zipball/bcae25d4b703d1ef1aad833c5dda57202383d48d", "reference": "bcae25d4b703d1ef1aad833c5dda57202383d48d", "shasum": ""}, "require": {"doctrine/common": "^2.13 || ^3.0", "php": "^7.1 || ^8.0", "symfony/framework-bundle": "^3.4 || ^4.1 || ^5.0"}, "require-dev": {"doctrine/data-fixtures": "^1.3", "doctrine/doctrine-bundle": "^1.8 || ^2.0", "doctrine/doctrine-fixtures-bundle": "^3.0.2", "doctrine/orm": "^2.6", "monolog/monolog": "^1.11 || ^2.0", "phpunit/phpunit": "^7.5 || ^8.0", "symfony/monolog-bridge": ">=3", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4 || ^4.1 || ^5.0", "theofidry/alice-data-fixtures": "^1.0.1"}, "suggest": {"doctrine/dbal": "Required when using the fixture loading functionality with an ORM and SQLite", "doctrine/doctrine-fixtures-bundle": "Required when using the fixture loading functionality", "doctrine/orm": "Required when using the fixture loading functionality with an ORM and SQLite", "hautelook/alice-bundle": "Required when using loadFixtureFiles functionality with custom providers", "theofidry/alice-data-fixtures": "Required when using loadFixtureFiles functionality"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Liip\\TestFixturesBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Liip AG", "homepage": "http://www.liip.ch/"}, {"name": "Community contributions", "homepage": "https://github.com/liip/LiipTestFixturesBundle/contributors"}], "description": "This bundles enables efficient loading of Doctrine fixtures in functional test-cases for Symfony applications", "keywords": ["fixtures", "symfony", "testing"], "support": {"issues": "https://github.com/liip/LiipTestFixturesBundle/issues", "source": "https://github.com/liip/LiipTestFixturesBundle/tree/1.10.1"}, "time": "2020-12-09T18:31:14+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.17", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "40a4ed114a4aea5afd6df8d0f0c9cd3033097f66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/40a4ed114a4aea5afd6df8d0f0c9cd3033097f66", "reference": "40a4ed114a4aea5afd6df8d0f0c9cd3033097f66", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": ">=7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.3 || ^4.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.17"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:09:37+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "69deeb8664f611f156a924154985fbd4911eb36b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/69deeb8664f611f156a924154985fbd4911eb36b", "reference": "69deeb8664f611f156a924154985fbd4911eb36b", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:39:50+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "a691211e94ff39a34811abd521c31bd5b305b0bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/a691211e94ff39a34811abd521c31bd5b305b0bb", "reference": "a691211e94ff39a34811abd521c31bd5b305b0bb", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:42:41+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/9c1da83261628cb24b6a6df371b6e312b3954768", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/3.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2021-07-26T12:15:06+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.40", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "48ed828b72c35b38cdddcd9059339734cb06b3a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/48ed828b72c35b38cdddcd9059339734cb06b3a7", "reference": "48ed828b72c35b38cdddcd9059339734cb06b3a7", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.0", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.2", "phpunit/php-code-coverage": "^7.0.17", "phpunit/php-file-iterator": "^2.0.6", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.4", "sebastian/comparator": "^3.0.5", "sebastian/diff": "^3.0.6", "sebastian/environment": "^4.2.5", "sebastian/exporter": "^3.1.6", "sebastian/global-state": "^3.0.5", "sebastian/object-enumerator": "^3.0.5", "sebastian/resource-operations": "^2.0.3", "sebastian/type": "^1.1.5", "sebastian/version": "^2.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage", "phpunit/php-invoker": "To allow enforcing time limits"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/8.5.40"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2024-09-19T10:47:04+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:45:45+00:00"}, {"name": "sebastian/comparator", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:31:48+00:00"}, {"name": "sebastian/diff", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "98ff311ca519c3aa73ccd3de053bdb377171d7b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/98ff311ca519c3aa73ccd3de053bdb377171d7b6", "reference": "98ff311ca519c3aa73ccd3de053bdb377171d7b6", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:16:36+00:00"}, {"name": "sebastian/environment", "version": "4.2.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "56932f6049a0482853056ffd617c91ffcc754205"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/56932f6049a0482853056ffd617c91ffcc754205", "reference": "56932f6049a0482853056ffd617c91ffcc754205", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:49:59+00:00"}, {"name": "sebastian/exporter", "version": "3.1.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "1939bc8fd1d39adcfa88c5b35335910869214c56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/1939bc8fd1d39adcfa88c5b35335910869214c56", "reference": "1939bc8fd1d39adcfa88c5b35335910869214c56", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:21:38+00:00"}, {"name": "sebastian/global-state", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "91c7c47047a971f02de57ed6f040087ef110c5d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/91c7c47047a971f02de57ed6f040087ef110c5d9", "reference": "91c7c47047a971f02de57ed6f040087ef110c5d9", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:13:16+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "ac5b293dba925751b808e02923399fb44ff0d541"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/ac5b293dba925751b808e02923399fb44ff0d541", "reference": "ac5b293dba925751b808e02923399fb44ff0d541", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:54:02+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/1d439c229e61f244ff1f211e5c99737f90c67def", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:56:04+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/9bfd3c6f1f08c026f542032dfb42813544f7d64c", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T14:07:30+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "72a7f7674d053d548003b16ff5a106e7e0e06eee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/72a7f7674d053d548003b16ff5a106e7e0e06eee", "reference": "72a7f7674d053d548003b16ff5a106e7e0e06eee", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:59:09+00:00"}, {"name": "sebastian/type", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "18f071c3a29892b037d35e6b20ddf3ea39b42874"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/18f071c3a29892b037d35e6b20ddf3ea39b42874", "reference": "18f071c3a29892b037d35e6b20ddf3ea39b42874", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/1.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T14:04:07+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "3659f10d13d270b77506006bdf9250cac9268156"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/3659f10d13d270b77506006bdf9250cac9268156", "reference": "3659f10d13d270b77506006bdf9250cac9268156", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "~3.4|~4.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0", "symfony/process": "~3.4|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v4.2.10"}, "time": "2019-06-11T15:21:32+00:00"}, {"name": "symfony/css-selector", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "48eddf66950fa57996e1be4a55916d65c10c604a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/48eddf66950fa57996e1be4a55916d65c10c604a", "reference": "48eddf66950fa57996e1be4a55916d65c10c604a", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.2.8"}, "time": "2019-01-16T20:31:39+00:00"}, {"name": "symfony/debug-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "7ad133ba7c5c932bca671d118aa634cd77ebb39f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/7ad133ba7c5c932bca671d118aa634cd77ebb39f", "reference": "7ad133ba7c5c932bca671d118aa634cd77ebb39f", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/http-kernel": "~3.4|~4.0", "symfony/twig-bridge": "~3.4|~4.0", "symfony/var-dumper": "^4.1.1"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4"}, "require-dev": {"symfony/config": "~4.2", "symfony/dependency-injection": "~3.4|~4.0", "symfony/web-profiler-bundle": "~3.4|~4.0"}, "suggest": {"symfony/config": "For service container configuration", "symfony/dependency-injection": "For using as a service from the container"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DebugBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug-bundle/tree/4.2"}, "time": "2019-07-19T08:33:10+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "ba1da8fb10291714b8db153fcf7ac515e1a217bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/ba1da8fb10291714b8db153fcf7ac515e1a217bb", "reference": "ba1da8fb10291714b8db153fcf7ac515e1a217bb", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/4.2"}, "time": "2019-06-13T10:57:15+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.35.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "25058310408bb045772d2ec0ff7a3b6460fea2dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/25058310408bb045772d2ec0ff7a3b6460fea2dd", "reference": "25058310408bb045772d2ec0ff7a3b6460fea2dd", "shasum": ""}, "require": {"doctrine/inflector": "^1.2|^2.0", "nikic/php-parser": "^4.11", "php": ">=7.1.3", "symfony/config": "^4.0|^5.0|^6.0", "symfony/console": "^4.0|^5.0|^6.0", "symfony/dependency-injection": "^4.0|^5.0|^6.0", "symfony/deprecation-contracts": "^2.2", "symfony/filesystem": "^4.0|^5.0|^6.0", "symfony/finder": "^4.0|^5.0|^6.0", "symfony/framework-bundle": "^4.0|^5.0|^6.0", "symfony/http-kernel": "^4.0|^5.0|^6.0"}, "require-dev": {"composer/semver": "^3.0@dev", "doctrine/doctrine-bundle": "^1.8|^2.0", "doctrine/orm": "^2.3", "symfony/http-client": "^4.3|^5.0|^6.0", "symfony/phpunit-bridge": "^4.3|^5.0|^6.0", "symfony/process": "^4.0|^5.0|^6.0", "symfony/security-core": "^4.0|^5.0|^6.0", "symfony/yaml": "^4.0|^5.0|^6.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.35.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-13T01:26:11+00:00"}, {"name": "symfony/var-dumper", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "4e18e041a477edbb8c54e053f179672f9413816c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/4e18e041a477edbb8c54e053f179672f9413816c", "reference": "4e18e041a477edbb8c54e053f179672f9413816c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.2.11"}, "time": "2019-07-27T06:42:33+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "e7b916235f8a1d33010ab707fb08943cf8573c1e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/e7b916235f8a1d33010ab707fb08943cf8573c1e", "reference": "e7b916235f8a1d33010ab707fb08943cf8573c1e", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "^4.2", "symfony/http-kernel": "^4.2.6", "symfony/routing": "~3.4|~4.0", "symfony/twig-bundle": "~4.2", "symfony/var-dumper": "~3.4|~4.0", "twig/twig": "^1.41|^2.10"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<3.4", "symfony/messenger": "<4.2", "symfony/var-dumper": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebProfilerBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/4.2"}, "time": "2019-07-24T14:47:26+00:00"}, {"name": "symfony/web-server-bundle", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/web-server-bundle.git", "reference": "91945ba7f59f2a4b4194f018da9d7aaedaf88418"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-server-bundle/zipball/91945ba7f59f2a4b4194f018da9d7aaedaf88418", "reference": "91945ba7f59f2a4b4194f018da9d7aaedaf88418", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/process": "^3.4.2|^4.0.2"}, "suggest": {"symfony/expression-language": "For using the filter option of the log server.", "symfony/monolog-bridge": "For using the log server."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebServerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebServerBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/web-server-bundle/tree/v4.2.6"}, "abandoned": true, "time": "2019-03-04T10:37:56+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.2", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}