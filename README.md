#saisie-des-temps

<center><b>Installation du projet</b></center>

1. <PERSON><PERSON> le projet: <b>git clone github.com/it-room/saisie-des-temps</b>
2. A la racine du projet : <b>docker-compose build</b>

Si vous rencontrez cette erreur :

</b>WARN[0000] network default : network.external.name is deprecated in favor of network.name network suivitps declared as external, but could not be found.</b>
-> Dan<PERSON> le fichier docker-compose.yml, supprimez la ligne 57 « <b>external</b> » qui se situe dans les networks.

3. A la racine du projet : <b>docker-compose up -d</b>
4. Rentrer dans le container php (pour voir les containers php: docker ps) : <b>docker exec -it <nom_du_container_php> bash</b>
5. Dans le container php : <b>composer install</b>
6. Dans le container php : <b>yarn install</b>
7. Dans le container php : <b>yarn encore dev</b>
8. Dans le container php : <b>chown -R www-data:www-data var/</b>
9. Dans le container php : <b>php bin/console c:c</b>


<b>Pour intégrer la base de données</b> :
10. Demander le fichier SQL du projet
11. Se rendre à la racine du projet : <b>exit</b> (pour sortir du container php)
12. Importer la BDD : <b>docker exec -i <nom_du_container_sql> mysql -uuser -puserpass suivitps < chemin du fichier sql .sql</b>
13. Se rendre sur <b>http://localhost:8088</b>


Ne pas oublier de créer sa branche depuis la <b>master</b>.
Si ce problème survient :
-> <b>Création de branche depuis le terminal mais Visual Code Studio reste sur la master ?</b>

Il faut créer sa branche depuis Visual Code Studio (en cliquant sur la branche master en bas à gauche de Visual Code Studio) car sinon il prendra pas en compte ta branche créée depuis le terminal.

