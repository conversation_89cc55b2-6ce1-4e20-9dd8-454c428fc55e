require("../css/app.scss");

import $ from "jquery";

require("bootstrap");
import "select2";

import "datatables.net";

$(document).ready(function () {
  $('#datatables').DataTable(
    {
      "lengthMenu": [[100, 50, 25, 10, -1], [100, 50, 25, 10, "All"]],
      "language": {
        "url": "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/French.json"
      }
    }
  );
  $("select").each(function () {
    if ($(this).hasClass("select2")) {
      $(this).select2({ theme: "bootstrap" });
    }
  });

  let client = $('#task_client');
  const project = $('#task_project');
  if (project.val() !== '') {
    fetchProjectDescription(project.val());
    project.on('change', function (e) {
      fetchProjectDescription(e.target.value);
    })
  }
  client.on('change', function () {
    let form = $(this).closest('form');
    let data = {};
    data[client.attr('name')] = client.val();
    // Submit data via AJAX to the form's action path.
    $.ajax({
      url: form.attr('action'),
      type: form.attr('method'),
      data: data,
      success: function (html) {
        $('#task_project').parent().replaceWith(
          $(html).find('#task_project').parent()
        );
        $("#task_project").select2({ theme: "bootstrap" });
        $('#task_project').on('change', function (e) {
          fetchProjectDescription(e.target.value);
        })
      }
    });
  });

  function fetchProjectDescription(projectId) {
    $.ajax({
      url: client.data('routeProjectDescription') + '?id=' + projectId,
      type: 'GET',
      success : function (json) {
        updateProjectDescription(json.description)
      }
    })
  }

  function updateProjectDescription(description) {
    if ($('#project-description').text() !== '') {
      $('#project-description').text(description)
    } else {
      $('#task_project').parent().append('<p id="project-description">' + description + '</p>');
    }
  }

  if (client.length > 0 && $('#task_project').val() == "") {
    client.trigger('change');
  }

  let btn_submit_new_task = $('#btn_submit_new_task');
  if (btn_submit_new_task.length)  btn_submit_new_task.attr('disabled', true);

  let shouldSetProject = false;

  let clientChrono = $('#timer_task_client');
  clientChrono.on('change', function () {

    $("#timer_task_project")
      .empty()
      .attr('disabled', true)
      .addClass('loading')
      .siblings('.select2')
      .append(loaderSelect());

    $('span[select-id="timer_task_project"]').removeAttr('data-ajax-success-projects');

    $('#btn_submit_new_task').attr('disabled', true);
    clientChrono.attr('disabled', true); // pour ne pas changer d'option en cours de route, pour éviter l'erreur pendant plusieur ajax

    let form = $(this).closest('form');
    let data = {};
    data[clientChrono.attr('name')] = clientChrono.val();
    // Submit data via AJAX to the form's action path.
    $.ajax({
      url: form.attr('action'),
      type: form.attr('method'),
      data: data,
      success: function(html) {
        let projects = [];
        let idProject = '';
        if (shouldSetProject) {
          idProject = $('span[select-id="timer_task_project"').attr('data-selected-id');console.log(idProject)
          shouldSetProject = false;
        }
        $(html).find('#timer_task_project option').each((idx, el) => {
          projects.push( { 'text': $(el).text(), 'id': $(el).attr('value') } );
        });

        // Stockage temporaire de la liste des projets attachés au client, pour "new_task" avec vuejs
        $('span[select-id="timer_task_project"]').attr('data-ajax-success-projects', JSON.stringify(projects));

        $("#timer_task_project")
          .select2({theme: "bootstrap", data: projects})
          .val(idProject)
          .trigger('change')
          .removeAttr('disabled')
          .removeClass('loading'); // Note: le tpl du loader est supprimé suite au reset du select2

        $('#btn_submit_new_task').removeAttr('disabled');
        clientChrono.removeAttr('disabled');
      }
    });
  });

  if (clientChrono.length > 0 ) {
    let attrIdClient = $('span[select-id="timer_task_client"').attr('data-selected-id');
    if (attrIdClient != '') {
      clientChrono.val(attrIdClient);
      clientChrono.trigger('change');
      shouldSetProject = true; // pour activer le set auto de la liste des projets car le client est choisi
    }
    else 
      $("#timer_task_project")
        .attr('disabled', true) // désactivé car la liste est vide
        .addClass('loading'); // juste pour cacher l'"arrow" avec css
  }

  let typeInterChrono = $('#timer_task_typeInter');
  if (typeInterChrono.length > 0) {
    let attrIdTypeInter = $('span[select-id="timer_task_typeInter"').attr('data-selected-id');
    if (attrIdTypeInter != '') {
      typeInterChrono.val(attrIdTypeInter);
      typeInterChrono.trigger('change');
    }
  }

  //For Chrono ONLY
  /*let chronoClient = $('#timer_task_client');
  chronoClient.on('change', function () {
    let form = $(this).closest('form');

    let data = {};
    data[chronoClient.attr('name')] = chronoClient.val();
    // Submit data via AJAX to the form's action path.
    $.ajax({
      url: form.attr('action'),
      type: form.attr('method'),
      data: data,
      success: function (html) {
        $('#timer_task_project').parent().replaceWith(
          $(html).find('#timer_task_project').parent()
        );
        $("#timer_task_project").select2({ theme: "bootstrap" });
      }
    });
  });

  if (chronoClient.length > 0 && $('#timer_task_project').val() == "") {
    chronoClient.trigger('change');
  }*/


  $("#check-all").click(function () {
    $("input.pilotage").click();
  });

  changeProject();
  changeTask();
  changeDateTask();
  onSubmitFormTask();
});


function changeProject() {
  $(".pilotage").change(function () {

    $('#affectation-projet').toggle();
  })

  function getPilotageChecked() {
    let result = false;
    $(".pilotage").each(function () {
      if ($(this).prop('checked') == true) {
        result = true;
      }
    })
    return result;
  }
}

function changeTask() {
  $(".pilotage").change(function () {
    let checked = getPilotageChecked();
    if (checked) {
      $('#affectation-task').show();
    } else {
      $('#affectation-task').hide();
    }
  })

  function getPilotageChecked() {
    let result = false;
    $(".pilotage").each(function () {
      if ($(this).prop('checked') == true) {
        result = true;
      }
    })
    return result;
  }

  let projectInput = $("#list_project_name");
  let submitInput = $("#affectation-task input[type='submit");
  checkProject();
  projectInput.change(function (e) {
    checkProject();
  })

  function checkProject() {
    submitInput.prop("disabled", false);
    let project = projectInput.val();
    if (project == "") {
      submitInput.prop("disabled", "true");
    }
  }

  let projectInput2 = $("#list_project_name");
  let submitInput2 = $("#affectation-projet input[type='submit");
  checkProject2();
  projectInput2.change(function (e) {
    checkProject2();
  })

  function checkProject2() {
    submitInput2.prop("disabled", false);
    let project2 = projectInput2.val();
    if (project2 == "") {
      submitInput2.prop("disabled", "true");
    }
  }
}

function changeDateTask() {
  // pour changer la date sur le champ date si le paramètre filtre date existe
  if ($('[data-filter-date]').length && $('#task_date').length) {
    let filterDate = $('[data-filter-date]').attr('data-filter-date');

    if (filterDate) {
      let filterDateSplit = filterDate.split('-');
      // une petite conversion pour le format yyyy-mm-dd compatible
      $('#task_date').val(filterDateSplit[2] + '-' + filterDateSplit[1] + '-' + ((filterDateSplit[0] > 9) ? filterDateSplit[0] : '0' + filterDateSplit[0]))
    }
  }
}

function onSubmitFormTask() {
  $('form[name="task"]').on('submit', () => {
    $('#oneclick').prop('disabled', true);
  });
}

function loaderSelect() {
  let tpl = 
    `<div class="block_input_loader _loader">
      <div class="wrap_spinner_loader">
        <div class="spinner-loader"></div>
      </div>
    </div>`;

  return tpl;
}

const loader = 
    `<div id="divLoader" class="block_input_loader _loader">
      <div class="wrap_spinner_loader">
        <div class="spinner-loader"></div>
      </div>
    </div>`;

$("#recherche_search").on("click", function() {
  $('#tbody').append(loader);
  $(".result").remove();
  $('#no-record').remove();
})