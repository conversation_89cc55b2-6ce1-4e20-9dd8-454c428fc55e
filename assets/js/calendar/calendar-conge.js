import $ from "jquery";
import "fullcalendar";
import "fullcalendar/dist/locale/fr";

const endPlusMap = {"matin": 12, "apres-midi": 24};

$(function () {
    let container = $("#calendar");
    container.fullCalendar({
            events: container.data("events"),
            displayEventTime: false,
            minTime: "08:00:00",
            maxTime: "18:00:00",
            header: {
                left: 'month, agendaWeek',
                center: 'title',
                right: 'today, prev, next'
            },
            eventDataTransform: function (event) {
                event.start = new Date(event.dateDebut.date);
                let end = new Date(event.dateFin.date);
                let endPlus = endPlusMap[event.dureeFin] || 24;
                end = end.setHours(end.getHours() + endPlus + 2);
                event.end = end;
                event.title = event.name + ' ' + event.firstName;
                event.className = "state-am state-" + event.etat.replace(" ", "-").replace("é", "e").toLowerCase();

                if (event.dateDebut.date === event.dateFin.date) {
                    if (event.dureeDebut === 'matin') {
                        event.className += ' itr-calendar-state-pm';
                    } else if (event.dureeDebut === 'apres-midi') {
                        event.className += ' itr-calendar-state-am';
                    }
                } else {
                    if (event.dureeFin === 'matin') {
                        event.className += ' itr-calendar-state-pm';
                    }
                    if (event.dureeDebut === 'apres-midi') {
                        event.className += ' itr-calendar-state-am';
                    }
                }

                console.log(event);

                return event;
            },
            dayClick: function (info) {
                let date = info.format("D-MM-YYYY");
                location.href = "/conge/" + date;
            }
        }
    )

    initCongeExportMonth();


});


function initCongeExportMonth() {
    let input = $("#export-mont-input");
    let button = $("#export-month");
    input.change(function () {
        let val = $(this).val();
        let year = val.split("-")[0];
        let month = val.split("-")[1];
        button.attr("href", "/conge-csv/" + year + "/" + month);
    })
}
