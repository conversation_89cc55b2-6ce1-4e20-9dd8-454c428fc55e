import $ from "jquery";
import "fullcalendar";
import "fullcalendar/dist/locale/fr";

$(function () {
    $("#calendar").fullCalendar({
        weekends: false,
        lang: "fr",
        displayEventTime: false,
        eventClick: function (info) {
            window.location.href = "/task/" + info.task_id + "/edit";
        },
        dayClick: function(info){
            let date = info.format("D-MM-YYYY");
            location.href = "/task/new/?date="+date;
        },
        eventSources: [
            // your event source
            {
                url: "/calendar-api",
                type: "POST",
                data: function () {
                    return {
                        month: $("#calendar").fullCalendar("getDate").month(),
                        year: $("#calendar").fullCalendar("getDate").year(),
                        user_id: $("#form_filter_calendar #calendar_filter_user_id").val()
                    };
                },
                error: function () {
                    alert("there was an error while fetching events!");
                },

                color: '#006dae',
                textColor: '#ffffff',
            }


        ],
        eventRender: function (eventObj, el) {
            el.prop('title', eventObj.title);
        },
        eventAfterAllRender: function (view) {
            var date = 0;
            var dayDuration = 0;
            var durations = [];
            $('.total').remove();
            $('#calendar').fullCalendar('clientEvents', function (event) {
                date = event.start._i.substr(0, 10);
                if (durations[date] === undefined) {
                    durations[date] = 0;
                }
                durations[date] += parseFloat(event.duration);
            });

            Object.keys(durations).forEach(function (key, index) {
                var styles = {
                    color: '#FFF',
                    backgroundColor: (this[key] < 7.5) ? '#F90' : '#0ab604',
                    borderRadius: '50%',
                    padding: '1px',
                    display: 'inline-block',
                    width: '20px',
                    height: '20px',
                    textAlign: 'center'
                };
                var spanDuration = $('<span class="total">' + this[key] + '</span>').css(styles);
                $('td.fc-day-top[data-date="' + key + '"]').find('span').before(spanDuration);
            }, durations);
        }
    });


    $(document).on('submit', '#form_filter_calendar', function (event) {
        event.preventDefault();
        $('#calendar').fullCalendar('refetchEvents');
    });

});
