import $ from "jquery";

$(function(){

    let modalAdd = $("#modal-conge-new");
    if($(".invalid-feedback", modalAdd).length || modalAdd.data("auto-show")){
        modalAdd.modal();
    }
    let dateDebut = $("#conge_dateDebut"), dateFin = $("#conge_dateFin"), typeJourneeFin = $("#conge_dureeFin"),
    typeJourneeDebut = $("#conge_dureeDebut");
    dateDebut.add(dateFin).change(function(){
        typeJourneeFin.parent().show();
        typeJourneeDebut.parent().find("label").html("Durée debut");
        if(dateFin.val() == dateDebut.val()){
            typeJourneeFin.parent().hide();
            typeJourneeDebut.parent().find("label").html("Durée");
        }
    }).trigger("change");
});
