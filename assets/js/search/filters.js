import $ from "jquery";
import "select2";

$(document).ready(function () {
    $(document).on('change', '#search_filters :input', function () {
        updateFilters($('#search_filters'));
    });
    $(document).on('select2:select', '#search_filters select', function (e) {
        updateFilters($('#search_filters'));
    });
});

function updateFilters(form) {
    var data = {};
    form.find(':input').each(function () {
        data[$(this).attr('name')] = $(this).val();
    });
    $.ajax({
        url: form.attr('action'),
        type: form.attr('method'),
        data: data,
        success: function (html) {
            form.find('select').each(
                function () {
                    let selector = '#'+$(this).attr('id');
                    $(this).replaceWith($(html).find(selector));
                    $(selector).select2("destroy").select2({theme: "bootstrap"});
            }
            );
            // // Replace current position field ...
            // $('#meetup_position').replaceWith(
            //     // ... with the returned one from the AJAX response.
            //     $(html).find('#meetup_position')
            // );
            // // Position field now displays the appropriate positions.
        }
    });
}