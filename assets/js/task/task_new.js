/* jshint esversion: 6 */
import Vue from "vue";
import jq from "jquery";
import "select2";

Vue.config.productionTip = false;

function isEmpty(obj) {
    for (var key in obj) {
        if (obj.hasOwnProperty(key)) return false;
    }
    return true;
}

const state_msg = {
    0: "Tâche en cours",
    1: "Envoi en cours",
    2: "Envoi\&nbsp;ré<PERSON>ie"
};
const state_class = {
    0: "ajax-ready",
    1: "ajax-in",
    2: "ajax-success",
    4: "ajax-error"
};

/* localStorage.removeItem("tasks");*/
task_new_main();

function task_new_main() {
    let task_new = {};
    let tasks = get_save();
    let select_options = {};
    let select_projects = [];
    let select_libelle = {};

    update_time();

    jq(".select-options").each(function () {
        let current_id = jq(this).attr("select-id");
        select_libelle[current_id] = jq(this).attr("name");
        let json = JSON.parse(jq(this).html());
        let options = [];
        for (var i in json) {
            let item = {};
            item.id = json[i].value;
            item.text = json[i].label;
            options.push(item);
        }
        if(current_id == 'timer_task_project') {
            // liste des projets dans une variable autre que select_options, pour séparer les variables réactives
            for (var i in tasks) {
                select_projects.push({id: tasks[i].id, datas: tasks[i].project_list == "" ? [] : JSON.parse(tasks[i].project_list)});
                tasks[i].disabled = {'client': false, 'project': false};
                tasks[i].loading = false;
            }
        }
        else {
            select_options[current_id] = options;
        }
    });

    Vue.component("select2", {
        props: ["options", "value", "type", "status"],
        template: "#select2-template",
        mounted: function () {
            var vm = this;
            jq(this.$el)
                .select2({theme: "bootstrap", data: this.options})
                .val(this.value)
                .trigger("change")
                // emit event on change.
                .on("change", function () {
                    vm.$emit("input", this.value);
                });
        },
        watch: {
            value: function (value) {
                if (this.type) { // prop qui se trouve uniquement dans la liste des chronos
                    form.form_check(jq(this.$el).attr("task-index"));
                    // update value
                    if (this.status == 'none' || this.type == 'project') {
                        // pas de trigger change, pour ne pas éxécuter une fonction "dépendante", exemple recharger la liste des projets
                        jq(this.$el).val(value);
                    }
                    else {
                        jq(this.$el)
                            .select2({theme: "bootstrap"})
                            .val(value)
                            .trigger("change");
                    }
                }
            },
            options: function (options) {
                // update options
                jq(this.$el)
                    .empty()
                    .select2({theme: "bootstrap", data: options})
                    .val(this.value)
                    .trigger('change')
            }
        },
        destroyed: function () {
            jq(this.$el)
                .off()
                .select2("destroy");
        }
    });
    let form = new Vue({
        delimiters: ["${", "}"],
        el: "#task_new",
        data: {
            task_new: task_new,
            tasks: tasks,
            tmp: [],
            status: "none",
            time: time_now(),
            form_error_msg: "",
            bool_oui: {
                true: "Oui",
                false: "Non"
            },
            select_options: select_options,
            select_projects: select_projects,
            select_libelle: select_libelle
        },
        mounted: function () {
            for (let i in this.tasks) {
                this.form_check(i);
                // si pas de liste enregistrée dans le localstorage suite à un plantage du navigateur (par exemple), on recharge la liste
                if (!this.tasks[i].project_list) this.getListProject(this.tasks[i].timer_task_client, i);
            }
        },
        methods: {
            create: function (event) {
                event.preventDefault();
                create();
            },
            submit: function (id) {
                let task = this.tasks[id];
                task.state = 1;
                subtract_pause(task);
                let time_ellapsed = msToHourFloat(
                    calc_diff(task.start_time),
                    0.25,
                    true
                );
                ajax_send(this.tasks[id], time_ellapsed, id);
            },
            task_delete: function (id) {
                this.tmp = JSON.parse(JSON.stringify(this.tasks)) // pour converver temporairement une copie de l'array, pour vérification
                this.status = "deleting"; // pour traitement avec select2
                this.$delete(this.tmp, id); // mise à jour de la copie array

                this.$delete(this.tasks, id);

                for (let i in this.select_projects) {
                    if (this.select_projects[i].id == id) {
                        this.$delete(this.select_projects, i);
                        break;
                    }
                }

                setTimeout(() => this.status = "none"); // sans timing, juste pour passer d'un status à un autre. Toujours pour select2
            },
            switch_pause: function (id) {
                let task = this.tasks[id];
                if (task.is_playing) {
                    task.pause_time = time_now();
                } else {
                    subtract_pause(task);
                }
                task.is_playing = !task.is_playing;
            },
            save: function () {
                save();
            },
            form_check: function (ind) {
                let task = this.tasks[ind];
                if (task) {
                    if (
                        !task.timer_task_client ||
                        !task.timer_task_project ||
                        !task.timer_task_refMantis ||
                        !task.timer_task_subject ||
                        !task.timer_task_typeInter
                    ) {
                        task.valid = false;
                        return;
                    }
                    task.valid = true;
                }
            },
            findProject(id) {
                for (let i in this.select_projects) {
                    if (this.select_projects[i].id == id) return this.select_projects[i].datas;
                }
            },
            emittedChangeClient(value, id) {
                if (this.tmp[id]) { // Si un élément existe, c'est qu'une ligne a été supprimée auparavant -> return pour ne pas recharger la liste des projets
                    this.tmp = [];
                    return;
                }
                this.getListProject(value, id);
            },
            getListProject(val, id) {
                let vm = this;

                vm.tasks[id].disabled.client = true;
                vm.tasks[id].disabled.project = true;
                vm.tasks[id].loading = true;
                vm.tasks[id].project_list = '';
                vm.tasks[id].timer_task_project = '';

                let indexProject = vm.select_projects.findIndex(project => project.id == id);
                Vue.set(vm.select_projects, indexProject, {id: id, datas:[]});

                jq.ajax({
                    url: '/task/new-index',
                    type: 'post',
                    data: {'timer_task[client]': val},
                    success: function(html) {
                        let projects = [];
                        jq(html).find('#timer_task_project option').each((idx, el) => {
                            projects.push( { 'text': jq(el).text(), 'id': jq(el).attr('value') } );
                        });

                        Vue.set(vm.select_projects, indexProject, {id: id, datas:projects});
                        
                        for (let i in vm.tasks) {
                            if (vm.tasks[i].id == id) {
                                vm.tasks[i].project_list = JSON.stringify(projects);
                                break;
                            }
                        }

                        vm.tasks[id].disabled.client = false;
                        vm.tasks[id].disabled.project = false;
                        vm.tasks[id].loading = false;

                    }
                });
            },
            state_msg: function (state) {
                return state_msg[state];
            },
            state_class: function (state) {
                return state_class[state];
            }
        },
        computed: {
            empty: function () {
                return isEmpty(this.tasks);
            }
        }
    });

    setInterval(() => {
        update_time();
    }, 1000);

    function get_save() {
        let saved_tasks = localStorage.getItem("tasks");
        if (saved_tasks != null) {
            return JSON.parse(saved_tasks);
        }
        return {};
    }

    function create() {
        let new_id = 0;
        for (var task in tasks) {
            new_id = tasks[task].id;
        }
        new_id++;
        
        new_task(new_id, task_new);
        form.form_check(new_id);
        save();
        update_time();
    }

    function subtract_pause(task) {
        if (task.pause_time != 0) {
            task.start_time += calc_diff(task.pause_time);
            task.pause_time = 0;
        }
    }

    function update_time(force) {
        for (var id in tasks) {
            if (!tasks[id].is_playing && !force || tasks[id].state !== 0) {
                continue;
            }
            tasks[id].time = msToTime(calc_diff(tasks[id].start_time));
        }
    }

    function save() {
        localStorage.setItem("tasks", JSON.stringify(tasks));
    }

    function new_task(id, params) {
        
        task_new.timer_task_project = jq('#timer_task_project').val();
        task_new.project_list = jq('[data-ajax-success-projects]').attr('data-ajax-success-projects');
        select_projects.push({id: id , datas: JSON.parse(task_new.project_list)})

        let task = {};
        for (var prop in params) {
            task[prop] = params[prop];
        }

        task.id = id;
        task.start_time = time_now();
        if (params.timer_task_duration) {
            if (!isNaN(params.timer_task_duration)) {
                task.start_time -= Number(params.timer_task_duration) * 3600000;
            }
        }
        task.is_playing = true;
        task.pause_time = 0;
        task.time = 0;
        task.user = jq("#user_id").html();
        task.valid = false;
        task.state = 0;
        task.disabled = {'client': false, 'project': false};
        Vue.set(form.tasks, id, task);
    }

    function msToHourFloat(value, step, _ceil, divisor) {
        if (divisor) {
            value /= divisor;
        } else {
            value /= 3600000;
        }
        if (!step) {
            step = 1.0;
        }
        var inv = 1.0 / step;
        if (_ceil) {
            return Math.ceil(value * inv) / inv;
        } else {
            return Math.round(value * inv) / inv;
        }
    }

    function msToTime(duration) {
        var seconds = Math.floor((duration / 1000) % 60),
            minutes = Math.floor((duration / (1000 * 60)) % 60),
            hours = Math.floor((duration / (1000 * 60 * 60)));

        hours = hours < 10 ? "0" + hours : hours;
        minutes = minutes < 10 ? "0" + minutes : minutes;
        seconds = seconds < 10 ? "0" + seconds : seconds;

        return hours + ":" + minutes + ":" + seconds;
    }

    function calc_diff(from, to) {
        if (!to) {
            to = time_now();
        }
        let diff = to - from;
        return diff;
    }

    function ajax_send(task, time, id) {
        for (var i in task) {
            let key = i.replace("timer_task_", "");
            task[key] = task[i];
        }
        task.duration = time;
        let data = JSON.stringify(task);
        form.form_error_msg = "";
        jq.ajax({
            type: "POST",
            url: " /api/task-duration",
            data: data,
            beforeSend: function (x) {
                if (x && x.overrideMimeType) {
                    x.overrideMimeType("application/j-son;charset=UTF-8");
                }
            },
            success: function (result) {
                try {
                    result = JSON.parse(result);
                } catch (e) {
                    form.form_error_msg = "Erreur serveur, veuillez tenter de rafraichir la page";
                    ajax_fail(task);
                }
                if (result.jsonResponse === "successChronoTask") {
                    setTimeout(function () {
                        form.task_delete(id)
                    }, 600);
                    task.state = 2;
                    return;
                }
                ajax_fail(task)

            },
            error: function (error) {
                let error_msg = JSON.parse(error.responseText);
                if(error_msg.jsonResponse){
                    form.form_error_msg = error_msg.jsonResponse;
                }
                ajax_fail(task);
            }
        });
    }


    function ajax_fail(task) {
        task.state = 4;
        setTimeout(function () {
            task.state = 0
        }, 600);
    }

    function time_now() {
        return new Date().getTime();
    }

    window.addEventListener("beforeunload", event => {
        save();
    });

}
