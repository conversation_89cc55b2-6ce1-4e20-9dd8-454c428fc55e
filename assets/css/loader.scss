$baseLoaderSize: 40px;
$inputLoaderSize: 26px;
$backgroundLoader: #CCCCCC;
$colorLoader: #006dae;

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

@mixin size_loader($size) {
  .wrap_spinner_loader {
    &:before {
      height: $size;
      width: $size;
    }

    .spinner-loader {
      height: $size;
      width: $size;
    }
  }
}

.wrap_spinner_loader {
  position: relative;
  display: inline-block;

  &:before {
    content: '';
    position: absolute;
    height: $baseLoaderSize;
    width: $baseLoaderSize;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid $backgroundLoader;
    border-radius: 50%;
  }

  .spinner-loader {
    height: $baseLoaderSize;
    width: $baseLoaderSize;
    border-radius: 50%;
    border-top: 2px solid $colorLoader;
    border-right: 2px solid transparent;
    animation: spinner 1s linear infinite;
  }
}

.block_input_loader {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  padding-right: 5px;
  z-index: 5;

  .wrap_spinner_loader {
    display: block;
  }
}

.block_input_loader {
  @include size_loader($inputLoaderSize);
}

select.loading {
  & ~ .select2 {
    .select2-selection__arrow {
      display: none;
    }
  }
}

#results_filters {
  width: 100%;
  display: flex;
  flex-direction: column;

  tr.main-result{
    cursor: pointer;
  }

  tr.details {
    td.sub-table {
      padding: 0;
    }
  }

  #divLoader {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    position: static;
  }
}

