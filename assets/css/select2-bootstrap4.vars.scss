// Overrides of Select2 SASS variables to make it work with Bootstrap 4 and match its look.
// Import this BEFORE https://github.com/select2/select2-bootstrap-theme SASS files or SASS compilation will fail
// Since there is no official Bootstrap 4 theme for Select 2 this is a good fallback.

/**
 * We need a clone of bootstrap color-yiq mixin so we can get the same value for color
 */
@function bs4-color-yiq($color) {
	$r: red($color);
	$g: green($color);
	$b: blue($color);

	$yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;

	@if ($yiq >= 150) {
		@return "#111";
	} @else {
		@return "#fff";
	}
}

$s2bs-input-border: $input-border-color !default;
$s2bs-border-radius-base: $border-radius !default;
$s2bs-border-radius-large: $border-radius-lg !default;
$s2bs-border-radius-small: $border-radius-sm !default;
$s2bs-btn-default-bg: lighten(theme-color("secondary"), $theme-color-interval * 2) !default;
$s2bs-btn-default-border: theme-color("secondary") !default;
$s2bs-btn-default-color: bs4-color-yiq($s2bs-btn-default-bg) !default;
$s2bs-clear-selection-hover-color: $s2bs-btn-default-color !default;
$s2bs-remove-choice-hover-color: $s2bs-btn-default-color !default;
$s2bs-caret-width-base: .25rem !default; // 4px
$s2bs-caret-width-large: .3125rem !default; // 5px
$s2bs-font-size-base: $font-size-base !default;
$s2bs-font-size-large: $font-size-lg !default;
$s2bs-font-size-small: $font-size-sm !default;
$s2bs-padding-base-horizontal: $input-btn-padding-x !default;
$s2bs-padding-large-horizontal: $input-btn-padding-x-lg !default;
$s2bs-padding-small-horizontal: $input-btn-padding-x-sm !default;
$s2bs-padding-base-vertical: $input-btn-padding-y  !default;
$s2bs-padding-large-vertical: $input-btn-padding-y-lg !default;
$s2bs-padding-small-vertical: $input-btn-padding-y-sm !default;
$s2bs-line-height-base: $input-btn-line-height !default;
$s2bs-line-height-large: $input-btn-line-height !default;
$s2bs-line-height-small: $input-btn-line-height !default;
$s2bs-input-height-base: calc(#{$input-btn-padding-y * 2 + $input-btn-line-height} + #{$border-width * 2}) !default;
$s2bs-input-height-large: calc(#{$input-btn-padding-y-lg * 2 + $input-btn-line-height} + #{$border-width * 2}) !default;
$s2bs-input-height-small: calc(#{$input-btn-padding-y-sm * 2 + $input-btn-line-height} + #{$border-width * 2}) !default;
$s2bs-input-bg-disabled: $input-disabled-bg !default;
$s2bs-input-color-placeholder: $input-placeholder-color !default;
$s2bs-input-border-focus: $input-focus-border-color !default;

$s2bs-selection-choice-border-radius: $border-radius !default;
$s2bs-cursor-disabled: not-allowed !default; // Missing in bootstrap 4

// Required Bootstrap 3 vars used in mixins
$state-warning-text: theme-color("warning") !default;
$state-danger-text: theme-color("danger") !default;
$state-success-text: theme-color("secondary") !default;
$screen-sm-min: breakpoint-min("md") !default;