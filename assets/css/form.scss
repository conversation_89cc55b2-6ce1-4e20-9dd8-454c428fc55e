textarea {
  min-height: 100px;

  &.select2-search__field{
    min-height: 0;
  }
}
button.select2-selection__choice__remove {
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
  cursor: pointer;
  overflow: hidden;
  outline: none;
}

input, select{
  max-width: 500px !important;
}
.select2-selection__choice {
  background: $light !important;
}

.table-form .select2-container--open {
  z-index: 10;
}

#task_new .select2 {
  font-size: 10px;
}

.select2 {
  font-weight: normal;
}

.select2-dropdown {
  min-width: min-content;
}

.select2-container {
  display: table-cell;
}

.max-w-md {
  max-width: 800px;
}

.max-w-xs {
  @media screen and (min-width: 992px) {
    max-width: 300px;
  }
}

.input-number-styled input::-webkit-outer-spin-button,
.input-number-styled input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

.input-number-styled input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}
