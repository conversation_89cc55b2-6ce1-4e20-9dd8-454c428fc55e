@import "assets/css/_variables";
@import "~bootstrap/scss/bootstrap";
@import "~select2/src/scss/core";
@import "assets/css/select2-bootstrap4.vars";
@import "assets/css/select2-bootstrap";
@import "assets/css/select2-bootstrap4.after";
@import "~simple-line-icons/scss/simple-line-icons";
@import "assets/css/form";
@import "~fullcalendar/dist/fullcalendar.css";
@import "~datatables.net-dt";
@import "loader";


body {
  font-size: 15px;
}

.container {
  margin-top: 50px;
  max-width: 90%;
}

p {
  margin-bottom: 0.5rem;
}

.header-title {
  font-size: 1.5rem;
  color: $gray-600;
}

header img {
  max-width: 75px;
}

nav ul li {
  width: auto;
}

nav {
  background-image: linear-gradient(to top, $light, $white);
}

.icon {
  margin: 0 5px;
  color: $gray-700;
  font-size: 1.2rem;
}

.icon:hover {
  color: $primary;
  text-decoration: none;
}

.icon-arrow-left-circle {
  font-size: 1.5rem;
  margin-left: 0;
  margin-top: 1rem;
}

.icon-send {
  width: 15px;
  height: 15px;
  color: $success;
}

.btn-send {
  .icon {
    position: relative;
    top: 2px;
  }
}

.btn-send:hover {
  .icon-send {
    color: white;
  }
}

.btn-send:disabled {
  border-color: $warning;

  .icon-send {
    color: $warning;
  }
}

h1 {
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 400;
  color: $gray-800;
}

h2 {
  margin-bottom: 2rem;
}

.nav-item {
  position: relative;
}

// @media screen and (max-width: 1200px) {
//   .nav-item {
//     width: min-content;
//   }
// }

@media screen and (max-width: 768px) {
  .nav-item {
    width: auto;
  }
  .blog-header {
    padding: 15px;
  }
}

@media (min-width: 768px) {
  .col-form-label {
    text-align: right;
  }
}

nav .logo {
  width: 100px;
  margin-right: 5px;
  background-color: #fff;
  padding: 5px;
}

table {
  overflow-x: scroll;
}

.table-large tr td.resum_inter {
  width: 30%;
}

.table-large td {
  width: 100px;

}

.td-subject {
  width: 330px;
}

.col-content {
  margin-bottom: 2rem;

  @media all and (min-width: 768px) {
    // background-color: $white;
    // padding: 1.4rem 1.2rem;
  }
}

form {
  margin-bottom: 10px;
}

button a {
  color: white;
  text-decoration: none;
}

button a:hover {
  text-decoration: none;
  color: white;
}

table a:hover {
  text-decoration: none;
}

.table-form {
  padding: 0.75rem;
}

.navbar-light .navbar-nav .nav-link {
  color: $primary;
}

.navbar-nav .active > .nav-link {
  color: darken($primary, 30);
}

.btn-outline-secondary a {
  color: $secondary;
}

.btn-outline-success a {
  color: $success;
  margin-top: -20px;
}

/* timer */

#chrono_nav {
  display: inline-block;
  vertical-align: middle;
}

.switch {
  position: relative;
  display: inline-block;
  width: 45px;
  height: 25.5px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 19.5px;
  width: 19.5px;
  left: 2.5px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(19.5px);
  -ms-transform: translateX(19.5px);
  transform: translateX(19.5px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}


.bg-cool {
  background-color: lighten($primary, $amount: 40%);
  padding: 1.5rem 1rem;
}

.mb-big {
  margin-bottom: 4rem;
}

.mb-low {
  margin-bottom: 0.5rem;
}

.blog-header {
  background: url("../img/banner_tetris.png");
  -webkit-background-size: 90% 100%;
  background-size: 90% 100%;
  background-repeat: no-repeat;
  @media screen and (max-width: 768px) {
    background: url("../img/banner_tetris.png");
    background-size: auto 100%;
  }
}

header > div {
  @media screen and (min-width: 992px) {
    padding: 0 2rem;
  }
}

.button-link:hover {
  a {
    color: white;
  }
}

.bg-light-alpha {
  background-color: rgba(250, 250, 250, 0.5);
}

.ajax-success {
  background-color: transparent;
  animation-name: ajax-success;
  animation-duration: 0.3s;
  animation-iteration-count: infinite;
}

@keyframes ajax-success {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: $success;
  }
  100% {
    background-color: transparent;
  }
}

.ajax-error {
  background-color: transparent;
  animation-name: ajax-error;
  animation-duration: 0.3s;
  animation-iteration-count: infinite;
}

.clickable {
  cursor: pointer;
}

@keyframes ajax-error {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: $danger;
  }
  100% {
    background-color: transparent;
  }
}


#calendar_page #calendar .fc-event-container {
  cursor: pointer;
}

.fc-event-container:hover {
  opacity: 0.7;
}

.fc-title {
  color: white;
  font-size: 1.2em;
}

.calendar-conge {
  .fc-event {
    padding: 1px 4px !important;
    font-weight: bold;
  }

  .state-valide {
    background: $primary;
    border-color: $primary;
  }

  .state-refuse {
    background: $gray;
    border-color: $gray;
  }

  .state-en-attente {
    background: $warning;
    border-color: $warning;
  }
}

h2 {
  font-size: 1.3rem;
}

.conge-dashboard-list {
  margin: 1.5rem 0;

  p {
    margin-bottom: 0;
  }

  ul {
    max-height: 50vh;
    overflow-y: auto;
  }

  li {
    display: flex;
    justify-content: space-between;
  }
}

.conge-add-container {
  .icon-info {
    vertical-align: middle;
    font-size: 1.2rem;
  }
}

.icon-action {
  &:hover {
    cursor: pointer;
    color: black;
  }
}

.date-day-duree {
  font-style: italic;
}

.conge-etat-refuse {
  color: $danger;
}

.conge-etat-en-attente {
  color: $warning;
}

.conge-etat-valide {
  color: $success;
}

#demande-conge {
  display: block;
}

#demande-conge > #form-conge {
  display: grid;
  flex: none;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
  max-width: none;
}

#form-conge .form-group:last-child {
  width: 50%;
  grid-column: span 2;
}

.select2-container {
  width: 100%;
  display: block !important;
}

body > .select2-container {
  width: auto;
}

.form-select2-adaptative {
  .select2-container {
    display: table-cell !important;
  }
}

.chrono-task {
  .form-select2-adaptative .select2-container {
    max-width: 100px;
  }
}

.select2-container--bootstrap .select2-results__option {
  white-space: nowrap;
}

.title-index {
  margin-top: 15px;
  padding-bottom: 5px;
  font-size: 2em;
}

.subtitle-index {
  padding-bottom: 5px;
  font-size: 0.8em;
}

.home-title {
  display: block;
  font-size: 1.3rem;
  margin-bottom: 2rem;
}

.chrono-index {
  margin-top: 30px;
}

.skill-shaker-index {
  margin-top: 30px;
}

.skill-chrono-index {
}

.btn-skill-shaker {
  font-size: 1.6em;
  display: flex;
  justify-content: center;
}

.title-form {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.bloc-list {
  width: 100%;
}

.page-user {
  a.btn-filter, a.btn-filterType {
    text-decoration: none;
    padding: 5px 10px;
    margin-right: 10px;
    color: black;
    display: inline-block;

    &.choice {
      text-decoration: underline;
    }
  }

  .black {
    color: black;
  }

  .first-bloc {
    text-align: center;
    font-size: 18px;
  }
}

.page-projects {
  a.btn-filter, a.btn-filterType {
    text-decoration: none;
    padding: 5px 10px;
    margin-right: 10px;
    color: black;
    display: inline-block;

    &.choice, &:hover, &.type {
      text-decoration: underline;
    }
  }

  .tdNull {
    background-color: #D14141;
  }

  .bloc-menu {
    position: sticky;
    top: 0;
    z-index: 10;
    background: $light;
    padding-bottom: 10px;
    padding-top: 10px;
  }

  .black {
    color: black;
  }

  .margintop10 {
    margin-top: 10px;
  }

  .firstbloc {
    text-align: center;
    font-size: 18px;
  }

  .inlineblock {
    display: inline-block;
  }

  .table-little {
    max-width: 900px;
  }

  .btn-back-top {
    padding-bottom: 10px;
    display: block;
  }

  .project-title-container {
    margin-top: 0.5rem;
    margin-bottom: 1.5rem;

    h1 {
      margin-bottom: 0.75rem;
    }
  }
}

.form-search form {
  legend{
    display: none;
  }

  .form-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin: 0 -15px;

    .form-group {
      padding: 0 15px 10px;
      flex: 0 0 auto;
    }
    .form-submit{
      flex-grow: 0;
    }
    > div:nth-of-type(8){
      flex: 1 0 100%;

    }
  }

  .col-md-6 {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .select2 {
    width: 100% !important;
  }

  #recherche {
    display: flex;
    justify-content: right;
  }
}

@include media-breakpoint-down(md){
  .form-search form {
    .form-container {
      .form-group {
        flex-grow: 1;
      }
    }
  }
}
.btn-back {
  &:hover {
    text-decoration: none;
  }
}

.card-deck {
  .card {
    max-width: 300px;
  }
}

#task_new {
  .input-number-styled {
    flex: 0 0 100%;
    .form-group {
      display: inline-block;
      vertical-align:top;
      width: 33%;
      padding: 0 15px;
      .select2-container {
        width: 100% !important;
        display: block;
      }
    }
    .btn {
      float: right;
      margin-right: 15px;
    }
    & + .col-lg-8 {
      max-width: 100%;
      flex: 0 0 100%;
    }
  }
  #chrono_table {
    width: 100% !important;
    .select2-container {
      width: 170px !important;
      display: block;
      max-width: 170px !important;
    }
    tbody {
      th:nth-child(5),
      th:nth-child(6) {
        .select2-container {
          width: 120px !important;
        }
      }
      th:nth-child(2) {
        input {
          width: 70px;
          height: 38px;
          border: 1px solid #ced4da;
          padding: 0 10px;
          color:#495057;
        }
      }
      textarea {
        border: 1px solid #ced4da;
        padding: 0 10px;
        color:#495057;
        width: 200px;
      }
    }
  }
}

.itr-calendar-state-am {
  margin-left: 150px !important;
}
.itr-calendar-state-pm {
  margin-right: 150px !important;
}

.allow {
  color: $success;

  &-not {
    color: $danger
  }
}

#suivi-tma{
  table th{
    text-align: center;
  }
  table, th, td {
    border: 1px solid;
  }
  table tbody td{
    text-align: center;

    &.project-name{
        text-align: left;
        font-weight: bold;
    }
  }

}