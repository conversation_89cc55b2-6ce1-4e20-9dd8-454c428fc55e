{"api-platform/core": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.1", "ref": "a8f6478aa07be998567b4db6fe5e89e556e1e9f6"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/doctrine-bundle": {"version": "1.11", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.6", "ref": "b618e23cce0c0e9c1bca00082dd6db0536acd07a"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-cache-bundle": {"version": "1.4.0"}, "doctrine/doctrine-fixtures-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.2", "ref": "baaa439e3e3179e69e3da84b671f0a3e4a2f56ad"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "google/apiclient": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.10", "ref": "07a97ec434d43b7903c78069a04c92adb6442e52"}}, "knpuniversity/oauth2-client-bundle": {"version": "1.34", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.20", "ref": "1ff300d8c030f55c99219cc55050b97a695af3f6"}, "files": ["config/packages/knpu_oauth2_client.yaml"]}, "lexik/jwt-authentication-bundle": {"version": "2.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "liip/test-fixtures-bundle": {"version": "1.10.1"}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "phpunit/phpunit": {"version": "8.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.7", "ref": "db276258424d15e572d35a4eb834b8f815662b25"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "sensio/framework-extra-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "symfony/console": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.2", "ref": "0a4841612ca4dd3464cbbe1e06b073c79ce7a7f2"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/debug-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.1", "ref": "0ce7a032d344fb7b661cd25d31914cd703ad445b"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/flex": {"version": "1.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.2", "ref": "95b4ddc6f93f433727cf62c0549b0f987d8c3bc2"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/maker-bundle": {"version": "1.35", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bundle": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "2120e71a370db3a494b8afcc42d7cfb2cff6f910"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "170ab6f9abd4e1dab87462847116659ae138c34e"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "38d658ce68375d66d2cddc88892af8f08701e6e7"}, "files": ["config/packages/security.yaml"]}, "symfony/swiftmailer-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "f0b2fccdca2dfd97dc2fd5ad216d5e27c4f895ac"}, "files": ["config/packages/dev/swiftmailer.yaml", "config/packages/swiftmailer.yaml", "config/packages/test/swiftmailer.yaml"]}, "symfony/translation": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "7e5381cd17e0e27d1b432af9230b65060b75ac4c"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "config/routes/dev/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.1", "ref": "21ab70175caf1e4b273791364fb30d27f1153546"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/web-server-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "dae9b39fd6717970be7601101ce5aa960bf53d9a"}}, "symfony/webpack-encore-bundle": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.6", "ref": "5919ae925ac2c06c860c6260fe12d1dc8ae2a419"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}}