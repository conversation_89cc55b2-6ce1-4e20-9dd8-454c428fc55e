{"devDependencies": {"cypress": "^3.2.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"@babel/core": "^7.25.2", "@symfony/webpack-encore": "^4.6.1", "bootstrap": "^4.3.1", "datatables.net": "^1.10.20", "datatables.net-bs4": "^1.10.20", "datatables.net-dt": "^1.10.20", "fullcalendar": "^3.10.0", "jquery": "^3.3.1", "material-design-icons": "^3.0.1", "moment": "^2.24.0", "popper": "^1.0.1", "popper.js": "^1.14.7", "sass": "^1.77.8", "sass-loader": "7.0.1", "select2": "^4.0.6-rc.1", "select2-bootstrap-theme": "^0.1.0-beta.10", "simple-line-icons": "^2.4.1", "vue": "^2.6.10", "vue-loader": "15.0.11", "vue-template-compiler": "^2.6.10"}}