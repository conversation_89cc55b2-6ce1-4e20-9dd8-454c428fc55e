ARG PHP_VERSION=7.2.14-fpm-alpine3.9
ARG NODE_VERSION=16.17.1-alpine3.16
ARG NGINX_VERSION=1.25.4-alpine3.18

# PHP CONTAINER
FROM surnet/alpine-wkhtmltopdf:3.10-0.12.5-full AS wkhtmltopdf
FROM php:${PHP_VERSION} AS builder
ARG BUILD_PATH=.
ARG TIMEZONE=Europe/Paris
ARG UID=1002

WORKDIR /app

# Create a non-root user to run the application.
RUN addgroup -S -g $UID appuser \
 && adduser -S -D -u $UID -h /home/<USER>
 && addgroup appuser www-data \
 && mkdir -p /home/<USER>/.composer \
 && chown -R appuser:appuser /home/<USER>
 && chown www-data:www-data /app \
 && chmod g+w /app

# Install healthcheck
RUN wget -O /usr/local/bin/php-fpm-healthcheck \
https://raw.githubusercontent.com/renatomefi/php-fpm-healthcheck/master/php-fpm-healthcheck \
&& chmod +x /usr/local/bin/php-fpm-healthcheck
RUN set -xe && echo "pm.status_path = /status" >> /usr/local/etc/php-fpm.d/zz-docker.conf
HEALTHCHECK --interval=2s CMD php-fpm-healthcheck

COPY --from=composer /usr/bin/composer /usr/bin/composer

RUN apk add --no-cache --virtual .build-deps libzip-dev libxml2-dev icu-dev $PHPIZE_DEPS  \
    && apk add --no-cache curl icu-libs libintl libzip fcgi \
    && pecl install apcu \
    && docker-php-ext-configure intl  \
    && docker-php-ext-install opcache intl pdo_mysql zip intl exif ctype xml \
    && docker-php-ext-enable apcu intl ctype xml \
    && pecl clear-cache \
    && apk del .build-deps \
    && rm -rf /tmp/*

COPY --link ${BUILD_PATH}/infra/docker/php/conf/symfony.ini /usr/local/etc/php/conf.d/symfony.ini
COPY --link ${BUILD_PATH}/infra/docker/php/conf/preload.php /usr/local/etc/php/preload.php

# GD INSTALLATION
RUN apk add --no-cache findutils gd freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev perl && \
  docker-php-ext-configure gd --with-freetype --with-jpeg && \
  NPROC=$(grep -c ^processor /proc/cpuinfo 2>/dev/null || 1) && \
  docker-php-ext-install -j${NPROC} gd && \
  apk del --no-cache freetype-dev libpng-dev libjpeg-turbo-dev

RUN apk add --no-cache tzdata supervisor libxrender fontconfig && mkdir /etc/supervisor.d/
COPY ${BUILD_PATH}/infra/docker/php/conf/supervisor.ini /etc/supervisor.d/supervisor.ini
CMD /usr/bin/supervisord --pidfile=/run/supervisord.pid -n -c /etc/supervisord.conf
COPY --from=wkhtmltopdf /bin/wkhtmltopdf /bin/wkhtmltopdf

## Dev application
FROM builder AS dev
ARG BUILD_PATH=.

RUN apk add --no-cache --virtual .build-deps  $PHPIZE_DEPS  \
    && apk add --no-cache git \
    && pecl install pcov \
    && docker-php-ext-enable pcov \
    && pecl clear-cache \
    && apk del .build-deps \
    && rm -rf /tmp/*

COPY ${BUILD_PATH}/infra/docker/php/scripts/install-symfony.sh /usr/local/bin/
RUN git config --global --add safe.directory /app

# BUILD ASSETS
FROM node:${NODE_VERSION} AS node-builder
ARG APP_PATH=.
WORKDIR /app
COPY ${APP_PATH}/package.* ${APP_PATH}/yarn.* ${APP_PATH}/webpack.config.js /app/
RUN yarn install
COPY ${APP_PATH}/assets /app/assets
RUN yarn encore production

## Production
FROM builder AS prod
ENV APP_ENV prod
RUN  sed -i 's/opcache.validate_timestamps.*/opcache.validate_timestamps=0/' /usr/local/etc/php/conf.d/symfony.ini
COPY --from=node-builder /app/public /app/public
COPY --chown=www-data . /app
RUN composer install -o -a --no-dev && chown -R www-data .
RUN rm -rf infra/ /usr/bin/composer
USER appuser

## NGINX  CONTAINER
FROM nginx:${NGINX_VERSION} AS nginx
ARG BUILD_PATH=.
COPY ${BUILD_PATH}/infra/docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY --from=node-builder /app/public /app/public

