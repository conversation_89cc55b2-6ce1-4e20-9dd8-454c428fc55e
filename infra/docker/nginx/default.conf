log_format apm '"$time_local" client=$remote_addr '
               'method=$request_method request="$request" '
               'request_length=$request_length '
               'status=$status bytes_sent=$bytes_sent '
               'body_bytes_sent=$body_bytes_sent '
               'referer=$http_referer '
               'user_agent="$http_user_agent" '
               'upstream_addr=$upstream_addr '
               'upstream_status=$upstream_status '
               'request_time=$request_time '
               'upstream_response_time=$upstream_response_time '
               'upstream_connect_time=$upstream_connect_time '
               'upstream_header_time=$upstream_header_time';

server {
    server_name ~.*;
    server_tokens off;

    location ~ \.(sql|tar|gz|tar.gz|tgz)$ { deny all; }

    location / {
        root /app/public;
     
        add_header Content-Security-Policy "script-src 'unsafe-inline' default-src https:" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Referrer-policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "accelerometer=('self'), geolocation=('self'), fullscreen=('self'), ambient-light-sensor=('self'), autoplay=('self'), battery=('self'), camera=('self'), display-capture=('self')" always;

        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        client_max_body_size 50m;

        fastcgi_pass php:9000;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /app/public/index.php;
    }


    error_log /dev/stderr debug;
    access_log /dev/stdout apm;
}
