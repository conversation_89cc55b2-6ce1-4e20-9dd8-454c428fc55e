networks:
    traefik:
        name: traefik-network
        external: true

services:
    php:
        networks:
            - traefik

    nginx:
        networks:
            traefik:
                aliases:
                    - $SERVER_NAME
        depends_on:
            - php
            - mysql
            - mailpit
        labels:
            - traefik.enable=true
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}.rule=Host(`${SERVER_NAME}`)
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}.entrypoints=http
            - traefik.http.middlewares.${COMPOSE_PROJECT_NAME}-redirect.redirectscheme.scheme=https
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}.middlewares=${COMPOSE_PROJECT_NAME}-redirect

            # HTTPS
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-secure.rule=Host(`${SERVER_NAME}`)
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-secure.entrypoints=https
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-secure.tls=true
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-secure.tls.certresolver=le

    mailpit:
        container_name: $COMPOSE_PROJECT_NAME-mailpit
        networks:
            traefik:
                aliases:
                    - mailpit.$SERVER_NAME
        image: axllent/mailpit
        labels:
            - traefik.enable=true
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit.rule=Host(`mailpit.${SERVER_NAME}`)
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit.entrypoints=http
            - traefik.http.middlewares.${COMPOSE_PROJECT_NAME}-mailpit-redirect.redirectscheme.scheme=https
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit.middlewares=${COMPOSE_PROJECT_NAME}-mailpit-redirect

            - traefik.http.services.${COMPOSE_PROJECT_NAME}-mailpit.loadbalancer.server.port=8025

            # HTTPS
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit-secure.rule=Host(`mailpit.${SERVER_NAME}`)
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit-secure.entrypoints=https
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit-secure.tls=true
            - traefik.http.routers.${COMPOSE_PROJECT_NAME}-mailpit-secure.tls.certresolver=le
