services:
    mysql:
        networks:
            default:
                aliases:
                    - $COMPOSE_PROJECT_NAME-mysql
        container_name: $COMPOSE_PROJECT_NAME-mysql
        image: mysql:8.0.0
        command: --default-authentication-plugin=mysql_native_password
        volumes:
            - dbdata:/var/lib/mysql
        environment:
            MYSQL_DATABASE: ${MYSQL_DATABASE}
            MYSQL_USER: ${MYSQL_USER}
            MYSQL_PASSWORD: ${MYSQL_PASSWORD}
            MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}

    php:
        networks:
            default:
                aliases:
                    - $COMPOSE_PROJECT_NAME-php
        container_name: $COMPOSE_PROJECT_NAME-php
        build:
            target: ${BUILD_TARGET:-dev}
            context: ../..
            dockerfile: infra/docker/php/Dockerfile
            args:
                TIMEZONE: ${TIMEZONE}
        volumes:
            - ../..:/app
        depends_on:
            - mysql

    nginx:
        networks:
            default:
                aliases:
                    - $COMPOSE_PROJECT_NAME-nginx
        container_name: $COMPOSE_PROJECT_NAME-nginx
        depends_on:
            - php
            - mysql
        build:
            target: nginx
            dockerfile: infra/docker/php/Dockerfile
            context: ../..
        environment:
            DOMAIN_NAME: $SERVER_NAME
        volumes:
            - ../../public:/app/public

    node:
        build:
            context: ../..
            dockerfile: infra/docker/php/Dockerfile
            target: node-builder
        volumes:
            - ../..:/app

volumes:
    dbdata:

networks:
    default:
        name: $COMPOSE_PROJECT_NAME
