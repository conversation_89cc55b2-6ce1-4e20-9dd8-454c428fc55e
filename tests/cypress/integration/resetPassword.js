describe('Reset password', function () {
    // we can use these values to log in
    const email = '<EMAIL>'

    context('Reset password page', function () {
        it('is accessible from the homepage', function () {
            cy.visit('/')
            cy.get('#resetPassword a').should(
                'contain',
                'Réinitialiser mon mot de passe'
            ).click()
            cy.url().should('include', 'reset-password')
        })

        it('sent mail to know user', function () {
            cy.visit('/reset-password')
            cy.get('#form_email').type(email)
            cy.get('#form_submit').click()

            cy.get('div.alert-primary').should(
                'contain',
                'Votre nouveau mot de passe est envoyé par mail.'
            )
        })

        it('display error to unknow user', function () {
            cy.visit('/reset-password')
            cy.get('#form_email').type('<EMAIL>')
            cy.get('#form_submit').click()

            cy.get('div.alert-primary').should(
                'contain',
                'Utilisateur non trouvé.'
            )
        })
    })



})
