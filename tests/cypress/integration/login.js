describe('Connexion', function () {
    // we can use these values to log in
    const username = 'Admin'
    const password = 'admin'

    context('Unauthorized', function () {
        it('is redirected on visit to /connexion when no session', function () {
            cy.visit('/task')
            cy.get('h1').should(
                'contain',
                'Formulaire de connexion !'
            )
            cy.url().should('include', 'connexion')
        })

        it('is redirected using cy.request', function () {
            cy.request({
                url: '/task',
                followRedirect: false, // turn off following redirects automatically
            }).then((resp) => {
                // should have status code 302
                expect(resp.status).to.eq(302)
                expect(resp.redirectedToUrl).to.contain('/connexion')
            })
        })
    })

    context('HTML form submission', function () {
        beforeEach(function () {
            cy.visit('/connexion')
        })

        it('displays errors on login', function () {
            // incorrect username on purpose
            cy.get('input[name=_username]').type(username)
            cy.get('input[name=_password]').type('incorrect password')
            cy.get('form').submit()

            // we should have visible errors now
            cy.get('div')
                .and('contain', 'Identifiants invalides')

            // and still be on the same URL
            cy.url().should('include', '/connexion')
        })

        it('redirects to / on success', function () {
            cy.get('input[name=_username]').type(username)
            cy.get('input[name=_password]').type(password)
            cy.get('form').submit()

            // we should be redirected to /dashboard
            cy.get('a').should('contain', 'Déconnexion')

            // and our cookie should be set to 'cypress-session-cookie'
            cy.getCookie('PHPSESSID').should('exist')
        })
    })

})
