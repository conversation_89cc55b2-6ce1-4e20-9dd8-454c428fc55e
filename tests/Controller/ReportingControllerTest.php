<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\DataFixtures\ReportingFixtures;
use App\Entity\User;
use App\Tests\LoginTrait;
use Doctrine\ORM\EntityManagerInterface;
use Liip\TestFixturesBundle\Test\FixturesTrait;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ReportingControllerTest extends WebTestCase
{
    use FixturesTrait;
    use LoginTrait;

    private $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->loadFixtures([ReportingFixtures::class]);
    }

    public function testReportingPageRecipientsListWithObservers(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->client->getContainer()->get('doctrine');
        $user = $entityManager->getRepository(User::class)->findOneBy(['mail' => '<EMAIL>']);

        $this->login($this->client, $user);
        $crawler = $this->client->request(Request::METHOD_GET, '/reporting/');

        $this->assertSame(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $crawler = $crawler->filter('p#reporting_recipients');
        $this->assertStringContainsString('<EMAIL>', $crawler->text());
        $this->assertStringContainsString('<EMAIL>', $crawler->text());
    }

    public function testReportingPageEmptyRecipientsList(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->client->getContainer()->get('doctrine');
        $user = $entityManager->getRepository(User::class)->findOneBy(['mail' => '<EMAIL>']);

        $this->login($this->client, $user);
        $crawler = $this->client->request(Request::METHOD_GET, '/reporting/');

        $this->assertSame(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $crawler = $crawler->filter('p#reporting_recipients');
        $this->assertSame('Ce reporting sera envoyé à : -', $crawler->text());
    }
}
