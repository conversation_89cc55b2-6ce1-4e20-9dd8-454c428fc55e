<?php

declare(strict_types=1);

namespace App\Tests\Service;

use App\Entity\Task;
use App\Service\TaskManagerService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class TaskManagerServiceTest extends KernelTestCase
{
    /**
     * @var TaskManagerService
     */
    private $taskManagerService;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->taskManagerService = self::$container->get(TaskManagerService::class);
    }

    public function testDuplicateTaskDuringWeekWithoutPublicHoliday(): void
    {
        $task = new Task();
        $task->setDate(new \DateTimeImmutable('2024-03-04')); // Lundi 04 mars 2024

        $duplicated = \array_values($this->taskManagerService->duplicateTask($task, 4));

        $this->assertCount(3, $duplicated);

        $duplicatedDates = \array_map(static function (Task $task) {
            return $task->getDate()->format('Y-m-d');
        }, $duplicated);

        $expectedDates = [
            '2024-03-05', // Mardi 05 mars 2024
            '2024-03-06', // Mercredi 06 mars 2024
            '2024-03-07', // Jeudi 07 mars 2024
        ];

        $diff = \array_diff($duplicatedDates, $expectedDates);

        $this->assertEmpty($diff);
    }

    public function testDuplicateTaskDuringWeekWithPublicHoliday(): void
    {
        $task = new Task();
        $task->setDate(new \DateTimeImmutable('2024-04-29')); // Lundi 29 avril 2024

        $duplicated = \array_values($this->taskManagerService->duplicateTask($task, 4));

        $this->assertCount(2, $duplicated); // 3 dupliquées - 1 jour férié

        $duplicatedDates = \array_map(static function (Task $task) {
            return $task->getDate()->format('Y-m-d');
        }, $duplicated);

        $expectedDates = [
            '2024-04-30', // Lundi 30 avril 2024
            '2024-05-02', // Jeudi 02 mai 2024
        ];

        $diff = \array_diff($duplicatedDates, $expectedDates);

        $this->assertEmpty($diff);
    }

    public function testDuplicateTaskWithWeekendWithoutPublicHoliday(): void
    {
        $task = new Task();
        $task->setDate(new \DateTimeImmutable('2024-04-10')); // Mercredi 10 avril 2024

        $duplicated = \array_values($this->taskManagerService->duplicateTask($task, 4));

        $this->assertCount(3, $duplicated);

        $duplicatedDates = \array_map(static function (Task $task) {
            return $task->getDate()->format('Y-m-d');
        }, $duplicated);

        $expectedDates = [
            '2024-04-11', // Jeudi 11 avril 2024
            '2024-04-12', // Vendredi 12 mai 2024
            '2024-04-15', // Lundi 16 avril 2024
        ];

        $diff = \array_diff($duplicatedDates, $expectedDates);

        $this->assertEmpty($diff);
    }

    public function testDuplicateTaskWithWeekendAndPublicHoliday(): void
    {
        $task = new Task();
        $task->setDate(new \DateTimeImmutable('2024-05-07')); // Mardi 7 mai 2024

        $duplicated = \array_values($this->taskManagerService->duplicateTask($task, 4));

        $this->assertCount(1, $duplicated); // 3 dupliquées - 2 jours fériés

        $duplicatedDates = \array_map(static function (Task $task) {
            return $task->getDate()->format('Y-m-d');
        }, $duplicated);

        $expectedDates = [
            '2024-05-10', // Vendredi 10 mai 2024
        ];

        $diff = \array_diff($duplicatedDates, $expectedDates);

        $this->assertEmpty($diff);
    }
}
