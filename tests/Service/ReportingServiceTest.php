<?php

declare(strict_types=1);

namespace App\Tests\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Exception\ReportingNotFoundException;
use App\Repository\TaskRepository;
use App\Service\ReportingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Twig\Environment;

class ReportingServiceTest extends KernelTestCase
{
    public function testGetReportingRecipientsWithTasks(): void
    {
        $manager = new User();
        $manager->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project one');
        $project->setManager($manager);

        $taskOne = new Task();
        $taskOne->setProject($project);

        $manager = new User();
        $manager->setMail('<EMAIL>');
        $observer = new User();
        $observer->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project two');
        $project->setManager($manager);
        $project->addObserver($observer);

        $taskTwo = new Task();
        $taskTwo->setProject($project);

        $reportingService = $this->getReportingServiceMock([$taskOne, $taskTwo]);

        $testUser = new User();
        $testUser->setMail('<EMAIL>');

        $result = $reportingService->getReportingEmailRecipientsForView($testUser, new \DateTime());
        $this->assertEmpty(\array_diff(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'], $result));
    }

    public function testGetReportingRecipientsWithNoTask(): void
    {
        $reportingService = $this->getReportingServiceMock([]);

        $testUser = new User();
        $testUser->setMail('<EMAIL>');

        $this->expectException(ReportingNotFoundException::class);

        $reportingService->getReportingEmailRecipientsForView($testUser, new \DateTimeImmutable());
    }

    public function testGetReportingRecipientsWithExcludedEmails(): void
    {
        $manager = new User();
        $manager->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project one');
        $project->setManager($manager);

        $taskOne = new Task();
        $taskOne->setProject($project);

        $manager = new User();
        $manager->setMail('<EMAIL>');
        $observer = new User();
        $observer->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project two');
        $project->setManager($manager);
        $project->addObserver($observer);

        $taskTwo = new Task();
        $taskTwo->setProject($project);

        $reportingService = $this->getReportingServiceMock([$taskOne, $taskTwo]);

        $testUser = new User();
        $testUser->setMail('<EMAIL>');

        $result = $reportingService->getReportingEmailRecipientsForView($testUser, new \DateTime());
        $this->assertEmpty(\array_diff(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'], $result));
    }

    public function testSendReportingEmailWithObservers(): void
    {
        $manager = new User();
        $manager->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project one');
        $project->setManager($manager);

        $taskOne = new Task();
        $taskOne->setProject($project);

        $manager = new User();
        $manager->setMail('<EMAIL>');
        $observer = new User();
        $observer->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project two');
        $project->setManager($manager);
        $project->addObserver($observer);

        $taskTwo = new Task();
        $taskTwo->setProject($project);

        $taskRepository = $this->createMock(TaskRepository::class);

        $taskRepository->expects($this->once())
            ->method('findBy')
            ->willReturn([$taskOne, $taskTwo]);

        $entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $entityManagerMock->expects($this->once())
            ->method('getRepository')
            ->with(Task::class)
            ->willReturn($taskRepository);

        $httpClientMock = $this->createMock(HttpClientInterface::class);
        $twigMock = $this->createMock(Environment::class);
        $mailerMock = $this->createMock(\Swift_Mailer::class);

        $reportingService = new ReportingService($httpClientMock, $entityManagerMock, $twigMock, '<EMAIL>', $mailerMock);

        $email = $reportingService->sendReportingEmail([], 'reporting text', new \DateTimeImmutable(), (new User())->setMail('<EMAIL>'));

        $this->assertArrayHasKey('<EMAIL>', $email->getTo());
    }

    public function testSendReportingEmailWithoutObservers(): void
    {
        $manager = new User();
        $manager->setMail('<EMAIL>');

        $project = new Project();
        $project->setName('project one');
        $project->setManager($manager);

        $taskOne = new Task();
        $taskOne->setProject($project);

        $taskRepository = $this->createMock(TaskRepository::class);

        $taskRepository->expects($this->once())
            ->method('findBy')
            ->willReturn([$taskOne]);

        $entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $entityManagerMock->expects($this->once())
            ->method('getRepository')
            ->with(Task::class)
            ->willReturn($taskRepository);

        $httpClientMock = $this->createMock(HttpClientInterface::class);
        $twigMock = $this->createMock(Environment::class);
        $mailerMock = $this->createMock(\Swift_Mailer::class);

        $reportingService = new ReportingService($httpClientMock, $entityManagerMock, $twigMock, '<EMAIL>', $mailerMock);

        $email = $reportingService->sendReportingEmail([], 'reporting text', new \DateTimeImmutable(), (new User())->setMail('<EMAIL>'));

        $this->assertArrayNotHasKey('<EMAIL>', $email->getTo());
    }

    private function getReportingServiceMock(array $tasks = []): ReportingService
    {
        $taskRepository = $this->createMock(TaskRepository::class);

        $taskRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($tasks);

        $entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $entityManagerMock->expects($this->once())
            ->method('getRepository')
            ->with(Task::class)
            ->willReturn($taskRepository);

        $httpClientMock = $this->createMock(HttpClientInterface::class);
        $twigMock = $this->createMock(Environment::class);
        $mailerMock = $this->createMock(\Swift_Mailer::class);

        return new ReportingService($httpClientMock, $entityManagerMock, $twigMock, '<EMAIL>', $mailerMock);
    }
}
