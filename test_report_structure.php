<?php

// Test simple pour vérifier la structure du rapport
echo "Test de la structure du rapport hebdomadaire\n";
echo "============================================\n\n";

// Simuler des données de test
$mockReport = [
    [
        'nom' => 'Total',
        'prenom' => 'Déclaré',
        'client' => '',
        'projet' => '',
        'temps_passe' => '150h',
        'pourcentage_temps' => '',
        'temps_itroom' => '25h',
    ],
    [
        'nom' => 'Total',
        'prenom' => 'Attendu',
        'client' => '',
        'projet' => '',
        'temps_passe' => '160h',
        'pourcentage_temps' => '',
        'temps_itroom' => '',
    ],
    [
        'nom' => 'Pourcentage',
        'prenom' => 'Temps déclaré vs attendu',
        'client' => '',
        'projet' => '',
        'temps_passe' => '93.8%',
        'pourcentage_temps' => '',
        'temps_itroom' => '',
    ],
    [
        'nom' => 'Dupont',
        'prenom' => 'Jean',
        'client' => 'IT Room',
        'projet' => 'Maintenance',
        'temps_passe' => '8h',
        'pourcentage_temps' => '',
        'temps_itroom' => '8h',
    ],
    [
        'nom' => 'Dupont',
        'prenom' => 'Jean',
        'client' => 'Temps déclaré (hors absence justifiée)',
        'projet' => '',
        'temps_passe' => '35h',
        'pourcentage_temps' => '',
        'temps_itroom' => '8h',
    ],
    [
        'nom' => 'Dupont',
        'prenom' => 'Jean',
        'client' => 'Temps sur site',
        'projet' => '',
        'temps_passe' => '37.5h',
        'pourcentage_temps' => '',
        'temps_itroom' => '',
    ],
];

echo "Structure du rapport :\n";
echo "Nom | Prénom | Client | Projet | Temps passé | Temps IT Room\n";
echo str_repeat("-", 80) . "\n";

foreach ($mockReport as $i => $line) {
    printf("%-12s | %-25s | %-20s | %-10s | %-11s | %-12s\n",
        $line['nom'],
        $line['prenom'],
        substr($line['client'], 0, 20),
        $line['projet'],
        $line['temps_passe'],
        $line['temps_itroom']
    );
    
    // Séparer les lignes globales des lignes utilisateurs
    if ($i === 2) {
        echo str_repeat("-", 80) . "\n";
    }
}

echo "\nTest terminé avec succès !\n";
echo "Les 3 premières lignes sont les totaux globaux.\n";
echo "La ligne 'Pourcentage' affiche le pourcentage global dans la colonne 'Temps passé'.\n";
echo "La colonne 'Temps IT Room' affiche les temps imputés au client IT Room.\n";
