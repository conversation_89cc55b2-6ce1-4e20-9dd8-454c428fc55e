image: docker:latest

services:
  - docker:19.03.5-dind

before_script:
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN registry.gitlab.com
  - apk add --no-cache py-pip
  - apk add --no-cache gcc
  - cp .env.test .env.local
  - pip install docker-compose~=1.23.0
  - cd ./docker
  - cp .env.test .env
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
  - docker-compose pull
  - docker-compose up -d nginx
  - docker-compose exec -T php composer install
  - docker-compose exec -T php npm install --production
  - docker-compose exec -T php yarn encore production
  - docker-compose exec -T php php bin/console doctrine:database:create
  - docker-compose exec -T php php bin/console doctrine:migration:migrate -n
  - docker-compose exec -T php php bin/console doctrine:fixture:load --group=tests -n -e dev
  - docker-compose exec -T php chown -R www-data:www-data var

stages:
  - build
  - test

### BUILD
build:
  stage: build
  image: docker:latest
  services:
    - docker:19.03.5-dind
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - apk add --no-cache py-pip
    - pip install docker-compose~=1.23.0
  script:
    - cd docker
    - cp .env.test .env
    - docker-compose pull || true
    - docker-compose build
    - docker-compose push
  only:
    refs:
      - master
      - develop

## TESTS
phpunit:
  stage: test
  script:
    - docker-compose exec -T php php bin/phpunit
  only:
    refs:
      - master
      - develop

cypress:
  stage: test
  script:
    - docker-compose run --no-deps --use-aliases -T cypress_base npx cypress run
  artifacts:
    when: always
    paths:
      - tests/cypress
      - var/log
    expire_in: 1 day
  only:
    refs:
      - master
      - develop