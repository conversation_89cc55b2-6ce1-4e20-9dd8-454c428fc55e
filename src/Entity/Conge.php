<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;


/**
 * @ORM\Entity(repositoryClass="App\Repository\CongeRepository")
 */
class Conge
{
    const ETATS = ['En Attente' => 'En Attente','Validé' => 'Validé', 'Refusé' => 'Refusé'];

    public const REASON = [
        'Congé' => 'holiday',
        'Maladie' => 'sickness'
    ];

    public function __construct(User $user)
    {
        $this->setDateCreation(new \DateTime());
        $this->setEtat(self::ETATS['En Attente']);
        $this->setUser($user);
    }

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime")
     */
    private $dateDebut;

    /**
     * @ORM\Column(type="datetime")
     */
    private $dateFin;

    /**
     * @ORM\Column(type="datetime")
     */
    private $dateCreation;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $etat;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\User", inversedBy="conges")
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $dureeDebut;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $dureeFin;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $messagerefus;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $reason;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->dateDebut;
    }

    public function setDateDebut(\DateTimeInterface $dateDebut): self
    {
        $this->dateDebut = $dateDebut;

        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->dateFin;
    }

    public function setDateFin(\DateTimeInterface $dateFin): self
    {
        $this->dateFin = $dateFin;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): self
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getEtat(): ?string
    {
        return $this->etat;
    }

    public function setEtat(string $etat): self
    {
        $this->etat = $etat;

        return $this;
    }

    public function getDureeDebut(): ?string
    {
        return $this->dureeDebut;
    }

    public function setDureeDebut(string $dureeDebut): self
    {
        $this->dureeDebut = $dureeDebut;

        return $this;
    }

    public function getDureeFin(): ?string
    {
        return $this->dureeFin;
    }

    public function setDureeFin(string $dureeFin): self
    {
        $this->dureeFin = $dureeFin;

        return $this;
    }

    public function getUser() : ?User
    {
        return $this->user;
    }

    public function setUser(?User $user) : self
    {
        $this->user = $user;
        return $this;
    }

    public function getMessagerefus(): ?string
    {
        return $this->messagerefus;
    }

    public function setMessagerefus(?string $messagerefus): self
    {
        $this->messagerefus = $messagerefus;

        return $this;
    }

    /**
     * @Assert\Callback
     */
    public function validate(ExecutionContextInterface $context, $payload)
    {
        if($this->getDateDebut() > $this->getDateFin() ){
            $context->buildViolation('Vous avez saisi une date de fin antérieure à la date de début')
            ->atPath('dateDebut')
            ->addViolation();
        }
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function setReason(string $reason): self
    {
        $this->reason = $reason;

        return $this;
    }
}
