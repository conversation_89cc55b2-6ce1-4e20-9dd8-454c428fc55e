<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\CompanyRepository")
 */
class Company
{
    const ALL = 0;
    const IT_ROOM = 1;
    const IT_REF = 2;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Project", mappedBy="company")
     */
    private $projects;

    /**
     * @ORM\Column(type="boolean")
     */
    private $projectEnabled;

    /**
     * @ORM\Column(type="boolean")
     */
    private $userEnabled;


    /**
     * @ORM\ManyToMany(targetEntity=TypeInter::class, mappedBy="allowCompany")
     */
    private $typeInters;

    public function __construct()
    {
        $this->projects = new ArrayCollection();
        $this->typeInters = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function __toString(): ?string
    {
        return $this->name;
    }


    /**
     * @return Collection|Project[]
     */
    public function getProjects(): Collection
    {
        return $this->projects;
    }

    public function addProject(Project $project): self
    {
        if (!$this->projects->contains($project)) {
            $this->projects[] = $project;
            $project->setCompany($this);
        }

        return $this;
    }

    public function removeProject(Project $project): self
    {
        if ($this->projects->contains($project)) {
            $this->projects->removeElement($project);
            // set the owning side to null (unless already changed)
            if ($project->getCompany() === $this) {
                $project->setCompany(null);
            }
        }

        return $this;
    }

    public function getProjectEnabled(): ?bool
    {
        return $this->projectEnabled;
    }

    public function setProjectEnabled(bool $projectEnabled): self
    {
        $this->projectEnabled = $projectEnabled;

        return $this;
    }

    public function getUserEnabled(): ?bool
    {
        return $this->userEnabled;
    }

    public function setUserEnabled(bool $userEnabled): self
    {
        $this->userEnabled = $userEnabled;

        return $this;
    }


    /**
     * @return Collection|TypeInter[]
     */
    public function getTypeInters(): Collection
    {
        return $this->typeInters;
    }

    public function addTypeInter(TypeInter $typeInter): self
    {
        if (!$this->typeInters->contains($typeInter)) {
            $this->typeInters[] = $typeInter;
            $typeInter->addAllowCompany($this);
        }

        return $this;
    }

    public function removeTypeInter(TypeInter $typeInter): self
    {
        if ($this->typeInters->removeElement($typeInter)) {
            $typeInter->removeAllowCompany($this);
        }

        return $this;
    }
}
