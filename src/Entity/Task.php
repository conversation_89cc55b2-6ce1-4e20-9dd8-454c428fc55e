<?php

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

/**
 * @ORM\Entity(repositoryClass="App\Repository\TaskRepository")
 * @ORM\Table(indexes={
 *     @ORM\Index(name="INDEX_DATE", columns={"date"}),
 *     @ORM\Index(name="INDEX_USER", columns={"user_id"}),
 *     @ORM\Index(name="INDEX_CLIENT", columns={"client_id"}),
 *     @ORM\Index(name="INDEX_TYPEINTER", columns={"type_inter_id"}),
 *     @ORM\Index(name="INDEX_PROJECT", columns={"project_id"})},
 *     )
 */
class Task
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $subject;

    /**
     * @ORM\Column(type="datetime")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    public $dateD;
    public function getDateD(): ?DateTimeInterface
    {
        return $this->dateD;
    }
    public $dateF;
    public function getDateF(): ?DateTimeInterface
    {
        return $this->dateF;
    }

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $content;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $refMantis;

    /**
     * @ORM\Column(type="decimal", scale=2)
     */
    private $duration;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\User", inversedBy="tasks")
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Client", inversedBy="tasks")
     * @ORM\JoinColumn(nullable=false)
     */
    private $client;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Project", inversedBy="tasks")
     * @ORM\JoinColumn(nullable=false)
     */
    private $project;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\TypeInter", inversedBy="tasks")
     * @ORM\JoinColumn(nullable=false)
     */
    private $typeInter;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getDate(): ?DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(?string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getRefMantis(): ?string
    {
        return $this->refMantis;
    }

    public function setRefMantis(?string $refMantis): self
    {
        $this->refMantis = ltrim($refMantis, "0");

        return $this;
    }

    public function getDuration(): ?float
    {
        return $this->duration;
    }

    public function setDuration(float $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getTypeInter(): ?TypeInter
    {
        return $this->typeInter;
    }

    public function setTypeInter(?TypeInter $typeInter): self
    {
        $this->typeInter = $typeInter;

        return $this;
    }

    /**
     * @Assert\Callback
     */
    public function validate(ExecutionContextInterface $context, $payload)
    {
        if ($this->getTypeInter() !== null && $this->getTypeInter()->getName() !== "Régie" ) {
            
            if($this->getRefMantis() === null){
                $context->buildViolation('Veuillez saisir une valeur')
                ->atPath('refMantis')
                ->addViolation();
            }

            if($this->getSubject() === null){
                $context->buildViolation('Veuillez saisir une description')
                ->atPath('subject')
                ->addViolation();
            }

            if($this->getProject() === null){
                $context->buildViolation('Veuillez choisir un projet')
                ->atPath('project')
                ->addViolation();
            }
        }
    }

}
