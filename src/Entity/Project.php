<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ProjectRepository")
 */
class Project
{

    const SEUIL_POURCENTAGE_CONSOMME = 75;

    const STATE_INACTIVE = 0;
    const STATE_ACTIVE = 1;
    const STATE_STANDBY = 2;
    const STATE_ALL = 3;

    const TYPE_ENGAGEMENT = 1;
    const TYPE_FORFAIT = 2;
    const TYPE_OTHER = 3;

    const NONE = 'aucun';

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Task", mappedBy="project")
     */
    private $tasks;

    /**
     * @ORM\Column(type="smallint")
     */
    private $actif = self::STATE_INACTIVE;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Client", inversedBy="projects")
     * @ORM\JoinColumn(nullable=true)
     */
    private $client;

    /**
     * @ORM\Column(type="decimal", scale=2, nullable=true)
     */
    private $chargeVendue;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $avancement;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Assert\NotBlank
     */
    private $reference;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\User", inversedBy="projects")
     */
    private $manager;

    /**
     * @ORM\Column(type="smallint", nullable=true)
     */
    private $type = self::TYPE_ENGAGEMENT;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $dateRecette;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $dateProduction;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Company", inversedBy="projects")
     * @ORM\JoinColumn(nullable=true)
     */
    private $company;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, inversedBy="userProjects")
     */
    private $userProject;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $description;
  
    /**
     * @ORM\Column(type="boolean", options={"default": "0"})
     * @Assert\Type("bool")
     */
    private $defaultProject = false;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, inversedBy="observerProjects")
     * @ORM\JoinTable(name="projects_observers")
     */
    private $observers;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $chargeTech;

    /**
     * @ORM\Column(type="decimal", precision=5, scale=2, nullable=true)
     */
    private $pilotage;

    /**
     * @ORM\Column(type="decimal", precision=5, scale=2, nullable=true)
     */
    private $recette;

    /**
     * @ORM\Column(type="decimal", precision=5, scale=2, nullable=true)
     */
    private $conception;

    /**
     * @ORM\Column(type="decimal", precision=5, scale=2, nullable=true)
     */
    private $mep;

    public function __construct()
    {
        $this->tasks = new ArrayCollection();
        $this->userProject = new ArrayCollection();
        $this->observers = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }


    public function getActif(): ?int
    {
        return $this->actif;
    }

    public function setActif(int $actif): self
    {
        $this->actif = $actif;

        return $this;
    }

    /**
     * @return Collection|Task[]
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(Task $task): self
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks[] = $task;
            $task->setProject($this);
        }

        return $this;
    }

    public function removeTask(Task $task): self
    {
        if ($this->tasks->contains($task)) {
            $this->tasks->removeElement($task);
            // set the owning side to null (unless already changed)
            if ($task->getProject() === $this) {
                $task->setProject(null);
            }
        }

        return $this;
    }

    public function __toString(): ?string
    {
        return $this->name;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client = null): self
    {
        $this->client = $client;

        return $this;
    }

    public function getChargeVendue(): ?float
    {
        return $this->chargeVendue;
    }

    public function setChargeVendue(?float $chargeVendue): self
    {
        $this->chargeVendue = $chargeVendue;

        return $this;
    }


    public function getAvancement()
    {
        return $this->avancement;
    }

    public function setAvancement($avancement): self
    {
        $this->avancement = $avancement;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(?string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getManager(): ?User
    {
        return $this->manager;
    }

    public function setManager(?User $manager): self
    {
        $this->manager = $manager;

        return $this;
    }

    public function getType(): ?int
    {
        return $this->type;
    }

    public function setType(?int $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getDateRecette(): ?\DateTimeInterface
    {
        return $this->dateRecette;
    }

    public function setDateRecette(?\DateTimeInterface $dateRecette): self
    {
        $this->dateRecette = $dateRecette;

        return $this;
    }

    public function getDateProduction(): ?\DateTimeInterface
    {
        return $this->dateProduction;
    }

    public function setDateProduction(?\DateTimeInterface $dateProduction): self
    {
        $this->dateProduction = $dateProduction;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @return Collection|User[]
     */
    public function getUserProject(): Collection
    {
        return $this->userProject;
    }

    public function addUserProject(User $userProject): self
    {
        if (!$this->userProject->contains($userProject)) {
            $this->userProject[] = $userProject;
        }

        return $this;
    }

    public function removeUserProject(User $userProject): self
    {
        $this->userProject->removeElement($userProject);

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description; 
        
        return $this;
    }
  
    public function getDefaultProject(): ?bool
    {
        return $this->defaultProject;
    }

    public function setDefaultProject(bool $defaultProject): self
    {
        $this->defaultProject = $defaultProject;
      
        return $this;
    }

    /**
     * @return Collection|User[]
     */
    public function getObservers(): Collection
    {
        return $this->observers;
    }

    public function addObserver(User $observer): void
    {
        if (!$this->observers->contains($observer)) {
            $this->observers[] = $observer;
        }
    }

    public function removeObserver(User $observer): void
    {
        $this->observers->removeElement($observer);
    }

    /**
     * @return string[]
     */
    public function getReportingRecipientEmails(): array
    {
        return \array_merge([$this->manager->getMail()], \array_map(static function (User $user) {
            return $user->getMail();
        }, $this->observers->toArray()));
    }

    public function getChargeTech(): ?string
    {
        return $this->chargeTech;
    }

    public function setChargeTech(?string $chargeTech): self
    {
        $this->chargeTech = $chargeTech;

        return $this;
    }

    public function getPilotage(): ?string
    {
        return $this->pilotage;
    }

    public function setPilotage(?string $pilotage): self
    {
        $this->pilotage = $pilotage;

        return $this;
    }

    public function getRecette(): ?string
    {
        return $this->recette;
    }

    public function setRecette(?string $recette): self
    {
        $this->recette = $recette;

        return $this;
    }

    public function getConception(): ?string
    {
        return $this->conception;
    }

    public function setConception(?string $conception): self
    {
        $this->conception = $conception;

        return $this;
    }

    public function getMep(): ?string
    {
        return $this->mep;
    }

    public function setMep(?string $mep): self
    {
        $this->mep = $mep;

        return $this;
    }
}
