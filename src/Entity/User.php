<?php

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * 
 * @ORM\Entity(repositoryClass="App\Repository\UserRepository")
 * @UniqueEntity(
 *     fields={"mail"},
 *     message="Cet email est déjà utilisé."
 * )
 */
class User implements UserInterface
{
    const FILTER_ACTIVE_ALL = 0;
    const FILTER_ACTIVE = 1;
    const FILTER_INACTIVE = 2;
    const FILTERS_SELECTOR = [
        User::FILTER_ACTIVE_ALL => ['oui', 'non', 1, 0],
        User::FILTER_ACTIVE => ['oui', 1],
        User::FILTER_INACTIVE => ['non', 0]
    ];

    const COMPANY_ALL = 'Tout';
    const COMPANY_IT_ROOM = 'IT-Room';
    const COMPANY_IT_REF = 'IT-Ref';
    const COMPANY_IT_BLM = 'IT-BLM';

    const TAG_BACK = 'Back';
    const TAG_FRONT = 'Front';
    const TAG_DESIGN = 'Design';
    const TAG_MANAGER = 'Pilotage';
    const IT_ROOM_TAGS = [
        USER::TAG_BACK => USER::TAG_BACK,
        USER::TAG_FRONT => USER::TAG_FRONT,
        USER::TAG_DESIGN => USER::TAG_DESIGN,
        USER::TAG_MANAGER => USER::TAG_MANAGER,
    ];

    const COMPANIES = [
        User::COMPANY_IT_ROOM => User::COMPANY_IT_ROOM,
        User::COMPANY_IT_REF => User::COMPANY_IT_REF,
        User::COMPANY_IT_BLM => User::COMPANY_IT_BLM,
    ];
  
    const ROLE_USER = 'ROLE_USER';
    const ROLE_GESTION_CONGES = 'ROLE_GESTION_CONGES';
    const ROLE_PROJECT_MANAGER = 'ROLE_PROJECT_MANAGER';
    const ROLE_ADMIN = 'ROLE_ADMIN';
    const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';

    const ROLES = [
        User::ROLE_SUPER_ADMIN => User::ROLE_SUPER_ADMIN,
        User::ROLE_ADMIN => User::ROLE_ADMIN,
        User::ROLE_PROJECT_MANAGER => User::ROLE_PROJECT_MANAGER,
        User::ROLE_GESTION_CONGES => User::ROLE_GESTION_CONGES,
        User::ROLE_USER => User::ROLE_USER,
    ];

    const ROLES_ORDER_HIERARCHY = [
        User::ROLE_SUPER_ADMIN,
        User::ROLE_ADMIN,
        User::ROLE_PROJECT_MANAGER,
        User::ROLE_GESTION_CONGES,
        User::ROLE_USER,
    ];

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $mail;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $username;

    private $passwd;

     /**
     * @ORM\Column(type="string", length=255)
     */
    private $actif;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $firstName;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $password;

    /**
     * @var null|string
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $googleId;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Task", mappedBy="user")
     */
    private $tasks;

    /**
     * @ORM\Column(type="boolean")
     */
    private $onSite = false;

    /**
     * @var string|null
     *
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $mantisApiKey;

    /**
     * @ORM\Column(type="decimal", scale=2)
     */
    private $duration;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Project", mappedBy="manager")
     */
    private $projects;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Conge", mappedBy="user")
     */
    private $conges;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $registrationNumber;

    /**
     * @ORM\ManyToMany(targetEntity=Project::class, mappedBy="userProject")
     */
    private $userProjects;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Company", inversedBy="projects")
     * @Assert\NotBlank
     * @ORM\JoinColumn(nullable=true)
     */
    private $company;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $tag;

    /**
     * @ORM\ManyToMany(targetEntity=Project::class, mappedBy="observers")
     */
    private $observerProjects;

    /**
     * @ORM\Column(type="array")
     */
    private $roles = [];

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Client")
     * @ORM\JoinColumn(nullable=true)
     */
    private $client;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Project")
     * @ORM\JoinColumn(nullable=true)
     */
    private $project;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\TypeInter")
     * @ORM\JoinColumn(nullable=true)
     */
    private $typeInter;

    /**
     * @ORM\Column(type="float", options={"default": 1})
     * @Assert\Range(min=0,max=1)
     */
    private $weekly_work_time = 1.0;

    public function __construct()
    {
        $this->tasks = new ArrayCollection();
        $this->projects = new ArrayCollection();
        $this->conges = new ArrayCollection();
        $this->userProjects = new ArrayCollection();
        $this->observerProjects = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMail(): ?string
    {
        return $this->mail;
    }

    public function setMail(string $mail): self
    {
        $this->mail = $mail;

        return $this;
    }

    public function getPasswd(): ?string
    {
        return $this->passwd;
    }

    public function setPasswd(string $passwd): self
    {
        $this->passwd = $passwd;

        return $this;
    }
    
    public function getActif(): ?string
    {
        return $this->actif;
    }

    public function setActif(string $actif): self
    {
        $this->actif = $actif;

        return $this;
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        if ( $roles == null ) $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    public function getHighestRole(): string
    {
        foreach (User::ROLES_ORDER_HIERARCHY as $role) {
            if (in_array($role, $this->getRoles())) {
                return $role;
            }
        }
        return User::ROLE_USER;
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function addRole(string $role) {
        $this->roles[] = $role;
    }

    public function resetRoles()
    {
        $this->roles = [];
    }


    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(string $username): self
    {
        $this->username = $username;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @return Collection|Task[]
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(Task $task): self
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks[] = $task;
            $task->setUser($this);
        }

        return $this;
    }

    public function removeTask(Task $task): self
    {
        if ($this->tasks->contains($task)) {
            $this->tasks->removeElement($task);
            // set the owning side to null (unless already changed)
            if ($task->getUser() === $this) {
                $task->setUser(null);
            }
        }

        return $this;
    }

    public function eraseCredentials() {}

    public function getSalt() {}

    public function __toString() : ?string
    {
        return $this->firstName . ' '. $this->name;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getTypeInter(): ?TypeInter
    {
        return $this->typeInter;
    }

    public function setTypeInter(?TypeInter $typeInter): self
    {
        $this->typeInter = $typeInter;

        return $this;
    }

    public function getTag(): ?string
    {
        return $this->tag;
    }

    public function setTag($tag): self
    {
        if (!in_array($tag, User::IT_ROOM_TAGS)) {
            throw new \InvalidArgumentException('tag must be in ['.implode(User::IT_ROOM_TAGS, ', ').']');
        }
        $this->tag = $tag;

        return $this;
    }

    public function getDuration(): ?float
    {
        return $this->duration;
    }

    public function setDuration(float $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getOnSite(): ?bool
    {
        return $this->onSite;
    }

    public function setOnSite(bool $onSite): self
    {
        $this->onSite = $onSite;

        return $this;
    }

    /**
     * @return Collection|Project[]
     */
    public function getProjects(): Collection
    {
        return $this->projects;
    }

    public function addProject(Project $project): self
    {
        if (!$this->projects->contains($project)) {
            $this->projects[] = $project;
            $project->setManager($this);
        }

        return $this;
    }

    public function removeProject(Project $project): self
    {
        if ($this->projects->contains($project)) {
            $this->projects->removeElement($project);
            // set the owning side to null (unless already changed)
            if ($project->getManager() === $this) {
                $project->setManager(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Conge[]
     */
    public function getConges(): Collection
    {
        return $this->conges;
    }

    public function addConge(Conge $conge): self
    {
        if (!$this->conges->contains($conge)) {
            $this->conges[] = $conge;
            $conge->setUser($this);
        }

        return $this;
    }

    public function removeConge(Conge $conge): self
    {
        if ($this->conges->contains($conge)) {
            $this->conges->removeElement($conge);
            // set the owning side to null (unless already changed)
            if ($conge->getUser() === $this) {
                $conge->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return string|null
     */
    public function getGoogleId(): ?string
    {
        return $this->googleId;
    }

    /**
     * @param string|null $googleId
     */
    public function setGoogleId(?string $googleId): void
    {
        $this->googleId = $googleId;
      
    }

    /**
     * @return string|null
     */
    public function getMantisApiKey(): ?string
    {
        return $this->mantisApiKey;
    }

    /**
     * @param string|null $mantisApiKey
     *
     * @return $this
     */
    public function setMantisApiKey(?string $mantisApiKey): User
    {
        $this->mantisApiKey = $mantisApiKey;

        return $this;
    }

    public function getRegistrationNumber(): ?string
    {
        return $this->registrationNumber;
    }

    public function setRegistrationNumber(?string $registrationNumber): ?string
    {
        $this->registrationNumber = $registrationNumber;

        return $this;
    }

    /**
     * @return Collection|Project[]
     */
    public function getUserProjects(): Collection
    {
        return $this->userProjects;
    }

    public function addUserProject(Project $userProject): self
    {
        if (!$this->userProjects->contains($userProject)) {
            $this->userProjects[] = $userProject;
            $userProject->addUserProject($this);
        }

        return $this;
    }

    public function removeUserProject(Project $userProject): self
    {
        if ($this->userProjects->removeElement($userProject)) {
            $userProject->removeUserProject($this);
        }

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @return Collection|Project[]
     */
    public function getObserverProjects(): Collection
    {
        return $this->observerProjects;
    }

    public function addObserverProject(Project $observerProject): void
    {
        if (!$this->observerProjects->contains($observerProject)) {
            $this->observerProjects[] = $observerProject;
            $observerProject->addObserver($this);
        }
    }

    public function removeObserverProject(Project $observerProject): void
    {
        if ($this->observerProjects->removeElement($observerProject)) {
            $observerProject->removeObserver($this);
        }
    }

    public function getWeeklyWorkTime(): ?float
    {
        return $this->weekly_work_time;
    }

    public function setWeeklyWorkTime(float $weekly_work_time): self
    {
        $this->weekly_work_time = $weekly_work_time;

        return $this;
    }
}
