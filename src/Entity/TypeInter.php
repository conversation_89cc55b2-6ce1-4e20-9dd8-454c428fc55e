<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\TypeInterRepository")
 */
class TypeInter
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $actif;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Task", mappedBy="typeInter")
     */
    private $tasks;

    /**
     * @ORM\ManyToMany(targetEntity=Company::class, inversedBy="typeInters")
     */
    private $allowCompany;

    public function __construct()
    {
        $this->tasks = new ArrayCollection();
        $this->allowCompany = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getActif(): ?string
    {
        return $this->actif;
    }

    public function setActif(string $actif): self
    {
        $this->actif = $actif;

        return $this;
    }

    /**
     * @return Collection|Task[]
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(Task $task): self
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks[] = $task;
            $task->setTypeInter($this);
        }

        return $this;
    }

    public function removeTask(Task $task): self
    {
        if ($this->tasks->contains($task)) {
            $this->tasks->removeElement($task);
            // set the owning side to null (unless already changed)
            if ($task->getTypeInter() === $this) {
                $task->setTypeInter(null);
            }
        }

        return $this;
    }

    public function __toString(): ?string
    {
        return $this->name;
    }

    /**
     * @return Collection|Company[]
     */
    public function getAllowCompany(): Collection
    {
        return $this->allowCompany;
    }


    public function addAllowCompany(Company $allowCompany): self
    {
        if (!$this->allowCompany->contains($allowCompany)) {
            $this->allowCompany[] = $allowCompany;
        }

        return $this;
    }

    public function removeAllowCompany(Company $allowCompany): self
    {
        $this->allowCompany->removeElement($allowCompany);

        return $this;
    }
}
