<?php

namespace App\Service;

use App\Entity\Conge;
use App\Entity\Task;
use App\Entity\Project;
use App\Entity\Client;
use App\Entity\TypeInter;
use App\Entity\User;
use Doctrine\ORM\EntityManager;
use Twig\Environment;
use Doctrine\ORM\EntityManagerInterface;

class CongeManagerService
{
    /**
     * @var TaskManagerService
     */
    private $taskManagerService;
    private $twig;
    
    /**
     * @var \Swift_Mailer
     */
    private $mailer;
    /** 
     * @var EntityManager
     */
    private $entityManager;

    public function __construct(
        Environment $environment,
        \Swift_Mailer $mailer,
        TaskManagerService $taskManagerService,
        EntityManagerInterface $entityManager
    )
    {
        $this->twig = $environment;
        $this->mailer = $mailer;
        $this->taskManagerService = $taskManagerService;
        $this->entityManager = $entityManager;
    }

    /**
     * La methode createTasksFromConge sert à créer les taches
     * dans le suivi des temps à partir d'un congé
     *
     * @param Conge $conge La variable contient le congé à partir duquel seront créées les taches
     * @return Task[]           La variable contient les taches qui ont été créées à partir du congé
     * @throws \Exception
     */
    public function createTasksFromConge(Conge $conge) : array
    {
        $nbJour = $this->getNbJourPourTache($conge->getDateDebut(),$conge->getDateFin());

        $project = $this->entityManager->getRepository(Project::class)->findOneBy(['name' => 'aucun']); 
        $type = $this->entityManager->getRepository(TypeInter::class)->findOneBy(['name' => 'Forfait']);
        $client = $this->entityManager->getRepository(Client::class)->findOneBy(['name' => 'Congés (CP/RTT/Abs/Mal)']);

        //creation task
        $task = new Task;
        $task->setSubject(' CONGE ')
            ->setCreatedAt(new \DateTime())
            ->setDate($conge->getDateDebut())
            ->setContent(' CONGE ')
            ->setRefMantis(0000)
            ->setDuration(7)
            ->setUser($conge->getUser())
            ->setProject($project)
            ->setClient($client)
            ->setTypeInter($type);

        if ($conge->getDureeDebut() === 'matin' || $conge->getDureeDebut() === 'apres-midi') {
            $task->setSubject('Conge '.$conge->getDureeDebut());
            $task->setDuration(3.5);
        }
        $this->entityManager->persist($task);

        $tasks = [$task];


        // duplicated tasks
        if ($nbJour !== 0) {
            $duplicatedTasks = $this->taskManagerService->duplicateTask($task, $nbJour );
            if (!empty($duplicatedTasks) && ($conge->getDureeFin() === 'matin' || $conge->getDureeFin() === 'apres-midi')) {
                $lastTask = end($duplicatedTasks);
                $lastTask->setSubject('Conge '.$conge->getDureeFin());
                $lastTask->setDuration(3.5);
                $duplicatedTasks[count($duplicatedTasks)-1] = $lastTask;
            }
            foreach ($duplicatedTasks as $duplicatedTask) {
                $this->entityManager->persist($duplicatedTask);
            }

            $tasks = array_merge($tasks, $duplicatedTasks);
        }
        
        $this->entityManager->flush();

        return $tasks;
    }

    public function envoieMailEnAttente()
    {
        $congeEnAttente = $this->entityManager->getRepository(Conge::class)->getCongeEnAttente();
        
        $mails = $this->entityManager->getRepository(User::class)->getMailRoleGestionConge();
        $to = [];
        foreach ( $mails as $mail ){
            $to[] = $mail['mail'];
        }

        $message = (new \Swift_Message('Hello Email'))
            ->setFrom('<EMAIL>')
            ->setTo($to)
            ->setSubject('Demandes de congés')
            ->setBody(
                $this->twig->render(
                    'emails/mail-en-attente.html.twig', [
                    'congeEnAttente' => $congeEnAttente,
                ])
            );
        
        $this->mailer->send($message);
        
        return(count($congeEnAttente));
    }

    public function objetsEnTableau($desConges)
    {
        $retour = [];
        foreach( $desConges as $unConge){
            $unConge = [ "id" => $unConge->getId(),
                         "dateDebut" => $unConge->getDateDebut(), 
                         "dateFin" => $unConge->getDateFin(),
                         "dateCreation" => $unConge->getDateCreation(),
                         "etat" => $unConge->getEtat(),
                         "dureeDebut" => $unConge->getDureeDebut(),
                         "dureeFin" => $unConge->getDureeFin(),
                         "name" => $unConge->getUser()->getName(),
                         "firstName" => $unConge->getUser()->getFirstName()];
            $retour[] = $unConge;
        }

        return $retour;
    }

    function getNbJourPourTache($dateDebut, $dateFin)
    {
        $dateD = clone $dateDebut;
        $dateF = clone $dateFin;

        $nbJour=0;

        while($dateD <= $dateF){

           if ( $dateD->format('N') !== "6" && $dateD->format('N') !== "7" ) {
               $nbJour++;
           }
            $dateD = $dateD->modify('+1 day');
        }

        return $nbJour;
    }
}