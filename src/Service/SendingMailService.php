<?php

namespace App\Service;

use Twig\Environment;

class SendingMailService
{
    const SENDER_ADDRESS = '<EMAIL>';
    private $mailer;
    private $mailBody;
    private $twig;

    public function __construct(\Swift_Mailer $mailer, Environment $twig)
    {
        $this->mailer = $mailer;
        $this->twig = $twig;
    }

    public function generateMailBody(string $templatePath, array $context): string
    {
        return $this->mailBody = $this->twig->render($templatePath, $context);
    }

    public function sendingMail(string $subject, array $to,?string $body = null, $from = null): void
    {
        $message = (new \Swift_Message($subject))
            ->setFrom($from ?? self::SENDER_ADDRESS)
            ->setTo($to)
            ->setBody($body ?? $this->mailBody, 'text/html')
        ;
        $this->mailer->send($message);
    }
}