<?php

namespace App\Service;

use App\Entity\Client;
use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TypeInter;
use App\Entity\User;
use App\Validator\Constraints\JourFerie;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class TaskManagerService
{
    private $entityManager;
    private $validator;

    public function __construct(EntityManagerInterface $entityManager, ValidatorInterface $validator)
    {
        $this->entityManager = $entityManager;
        $this->validator = $validator;
    }

    /**
     * @return Task[]
     */
    public function duplicateTask(Task $task, int $nbRepetitions): array
    {
        $newTasks = [];
        for ($i = 0; $i < $nbRepetitions - 1; ++$i) {
            $newTask = clone $task;
            $newTask->setDate((clone $task->getDate())->modify('+1 weekday'));
            $task = $newTask;

            $violations = $this->validator->validate($newTask->getDate(), new JourFerie);

            if (0 !== $violations->count()) {
                continue;
            }

            $newTasks[] = $newTask;
        }

        return $newTasks;
    }

    public function createTaskFromAjaxRequest(array $request){
        // Instancier un nouvel objet Task
        $task = new Task();

        // Récupération de l'id client dans la requête
        $client = $this->entityManager->getReference(Client::class, $request['client']);

        // // Récupération de l'id user dans la requête
        $user = $this->entityManager->getReference(User::class, $request['user']);

        // // Récupération de l'id project dans la requête
        $project = $this->entityManager->getReference(Project::class, $request['project']);

        // // Récupération de l'id typeInter dans la requête
        $typeInter = $this->entityManager->getReference(TypeInter::class,$request['typeInter']);

        $date = new \DateTime();
        $date->setTime(0, 0);

        $task->setUser($user);
        $task->setClient($client);
        $task->setProject($project);
        $task->setTypeInter($typeInter);
        $task->setSubject($request['subject']);
        $task->setRefMantis($request['refMantis']);
        $task->setCreatedAt(new \DateTime());
        $task->setDate($date);
        $task->setDuration($request['duration']);

        $this->entityManager->persist($task);
        $this->entityManager->flush();
    }

    public function checkAjaxRequestIsAllowed(array $request)
    {
        $client = $this->entityManager->getRepository(Client::class)->find($request['client']);
        $project = $this->entityManager->getRepository(Project::class)->find($request['project']);
        $projects = $this->entityManager->getRepository(Project::class)->getProjectsByClient($client);
        if (empty($projects[$project->getName()])) {
            return 'Veuillez choisir un projet associé à ce client';
        }
        return true;
    }

    /**
     * Return the time passed for all the tickets in the $tickets parameter,
     * the average time fol all the tickets,
     * the time for each ticket.
     *
     * @param array $tickets List of tickets that we want to retrieve
     * @param string $startDate The date after which the tasks must have been entered
     * @param string $endDate The date before which the tasks must have been entered
     * @return array|null
     */
    public function getAverageTime(array $tickets, string $startDate = null, string $endDate = null): ?array
    {
        $tasks = $this->entityManager->getRepository(Task::class)->getTicketsByRefMantisAndDate($tickets, $startDate, $endDate);
        if (empty($tasks)) {
            return null;
        }

        $totalTime = 0;
        $times = array_fill_keys($tickets, 0);

        foreach ($tasks as $task) {
            $totalTime += $task['duration'];
            $times[$task['refMantis']] += $task['duration'];
        }

        $average = $totalTime / count(array_filter($times, static function ($time) { return $time !== 0; }));

        return [
            'total_time' => $totalTime,
            'average' => $average,
            'times' => $times
        ];
    }

    /**
     * @param $tasks
     * @param $project
     * @param $lastProject
     *
     * @return mixed
     */
    public function changeProject($tasks, $project, $lastProject)
    {
        foreach ($tasks as $task) {
            $lastProject->removeTask($task);
            $project->addTask($task);
            $task->setProject($project);
        }

        $this->entityManager->flush();

        return $tasks;
    }
}
