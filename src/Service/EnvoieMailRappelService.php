<?php

namespace App\Service;

use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use DateTime;

class EnvoieMailRappelService
{
    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * @var SendingMailService
     */
    private $sendingMailService;

    public function __construct(
        EntityManagerInterface $entityManager,
        SendingMailService $sendingMailService
    )
    {
        $this->entityManager = $entityManager;
        $this->sendingMailService = $sendingMailService;
    }

    public const SUBJECT_RAPPEL = 'Rappel saisie des temps';
    public const SUBJECT_RELANCE = 'Relance de rappel saisie des temps';
    public const RELANCE = 'relance';
    public const RAPPEL = 'rappel';
    public const TYPES = [
        self::RAPPEL => self::RAPPEL,
        self::RELANCE => self::RELANCE
    ];
    public const DURATION_BEFORE_SEND_MAIL = 7.0;
    private const IS_ON_SITE = true;
    private const EXCLUDE_ADMIN_EMAIL = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    private const EXCLUDE_USER_EMAIL = [
        '<EMAIL>'
    ];

    private function getAllUsers(): array
    {
        return $this->entityManager->getRepository(User::class)
            ->findByActifAndCompanyAndOnSite(
                true,
                User::COMPANY_IT_ROOM,
                self::IS_ON_SITE
            )
        ;
    }

    public function getUsersWithDurationLower7ByDate(DateTime $dateDebut, DateTime $dateFin): array
    {
        foreach ($this->getAllUsers() as $user) {
            if (in_array($user->getMail(), self::EXCLUDE_USER_EMAIL)) {
                continue;
            }

            $role = $user->getRoles();

            if (in_array(User::ROLE_ADMIN, $role)) {
                continue;
            }

            $tasks = $this->getTask($dateDebut, $dateFin, $user);

            $totalDuration = array_reduce($tasks, function ($carry, $task) {
                return $carry + $task['duration'];
            }, 0.0);

            if (
                $totalDuration < self::DURATION_BEFORE_SEND_MAIL ||
                empty($tasks)
            ) {
                $users[] = [
                    'mail' => $user->getMail(),
                    'firstname' => $user->getFirstname()
                ];
            }
        }

        return $users ?? [];
    }

    public function getTask(DateTime $dateDebut, DateTime $dateFin, User $user): array
    {
        $rTask = $this->entityManager->getRepository(Task::class);

        return $rTask->findByMultiplFields(
            [
                'dateD' => $dateDebut,
                'dateF' => $dateFin,
                'user' => [$user],
            ]
        );
    }

    public function sendMail(string $subject, array $users, string $type, string $date): void
    {
        $to = [];

        if ($type === self::RELANCE) {
            $adminMails = $this->entityManager->getRepository(User::class)->findMailByRoleAndCompany(User::ROLE_ADMIN, User::COMPANY_IT_ROOM);
            $to = array_column($adminMails, 'mail');

            foreach (self::EXCLUDE_ADMIN_EMAIL as $exclude) {
                unset($to[array_search($exclude, $to)]);
            }
        }

        foreach ($users as $user) {
            $this->sendingMailService->generateMailBody('emails/rappel-saisie-temps.html.twig', compact('user', 'date'));
            $this->sendingMailService->sendingMail($subject, array_merge($to, [$user['mail']]));
        }
    }
}