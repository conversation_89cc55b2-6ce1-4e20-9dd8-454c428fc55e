<?php

namespace App\Service;

use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use Symfony\Contracts\Translation\TranslatorInterface;

class SuiviTMAService
{
    /**
     * @var ProjectRepository
     */
    private $projectRepository;
    /**
     * @var TaskRepository
     */
    private $taskRepository;
    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(ProjectRepository $projectRepository, TaskRepository $taskRepository, TranslatorInterface $translator)
    {
        $this->projectRepository = $projectRepository;
        $this->taskRepository = $taskRepository;
        $this->translator = $translator;
    }


    public function getSuiviTMA(): array
    {
        $projects = $this->projectRepository->getTMAProjects();
        $dateFrom = new \DateTime('first day of last month');
        $dateFrom->modify('-10 months');
        $tasks = $this->getTasksByMonth(array_column($projects, 'id'), $dateFrom->format('Y-m-d 00:00:00'));

        $chargeVendues = array_column($projects, null, 'name');

        foreach ($tasks as $taskName => &$task) {
            $tasks[$taskName]['id'] = $chargeVendues[$taskName]['id'];
            $tasks[$taskName]['chargeVendue'] = $chargeVendues[$taskName]['chargeVendue'];
            $chargeVendue = (float)($tasks[$taskName]['chargeVendue'] ?? 0);


            foreach ($task['quarters'] as &$year){
                foreach ($year as &$quarter){
                    $quarter['ratio'] = $chargeVendue > 0 ? round($quarter['value'] / $chargeVendue * 100, 2) : 0;
                    $quarter['ratio-color'] = $quarter['ratio'] >= 100 ? 'bg-danger' : ($tasks[$taskName]['chargeVendue'] === null ? '' : 'bg-success');
                }
            }
        }

        $quarters = [];
        foreach (new \DatePeriod($dateFrom, new \DateInterval('P1M'), new \DateTime() ) as $date) {
            $intervals[] = [
                'date'=> $date->format('Y-m'),
                'label'=> $this->translator->trans(sprintf("month.%s",$date->format('M'))) . " " . $date->format('Y'),
            ];
            $quarters[$date->format('Y')][ceil((int)$date->format('m') / 3)] =  ($quarters[$date->format('Y')][ceil((int)$date->format('m') / 3)] ?? 0) + 1;
        }

        return [
            'intervals' => $intervals,
            'quarters' => $quarters,
            'details' => $tasks
        ];

    }

    public function getTasksByMonth(array $projectsIDs, string $dateFrom): array
    {
        $tasks = $this->taskRepository->getTMAProjectsTasks($projectsIDs, $dateFrom);
        $result = [];
        foreach ($tasks as $task) {
            $result[$task['name']]['months'][$task['month']] = $task['duration'];
            $quarter = ceil((int)substr($task['month'], 5, 2) / 3);
            $result[$task['name']]['quarters'][substr($task['month'], 0,4)][$quarter]['value'] = ($result[$task['name']]['quarters'][substr($task['month'], 0,4)][$quarter]['value'] ?? 0) + $task['duration'];
        }

        ksort($result);

        return $result;

    }
}