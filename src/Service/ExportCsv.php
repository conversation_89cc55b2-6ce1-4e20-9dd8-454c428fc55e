<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Contracts\Translation\TranslatorInterface;

class ExportCsv
{
    protected $fichierCsv;
    protected $fileName;
    /** @var TranslatorInterface $translator */
    protected $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
        $this->fileName = $this->getFileName();
        $this->fichierCsv = fopen("/tmp/".$this->fileName, 'w');
    }

    protected function ouvertureDuCsv()
    {
        fprintf($this->fichierCsv, $bom = ( chr(0xEF) . chr(0xBB) . chr(0xBF) )); 
        return $this->fichierCsv;
    }
    
    protected function setHeader($data)
    {
        fputcsv($this->fichierCsv,$data,';');
    }

    protected function setData($data)
    {
        fputcsv($this->fichierCsv,$data,';');
    }
    
    protected function fermetureDuCsv()
    {
        fclose($this->fichierCsv);
        $response = new BinaryFileResponse("/tmp/".$this->fileName);
        $response->headers->set('Content-Type', 'text/csv');
        $response->setContentDisposition(
        ResponseHeaderBag::DISPOSITION_ATTACHMENT,
        $this->fileName . '.csv'
        );
        return $response;
    } 

    protected function getFileName()
    {
        return 'Export';
    }
}