<?php

namespace App\Service;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Swift_Mailer;
use Swift_Message;
use Symfony\Component\Security\Core\Encoder\PasswordEncoderInterface;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Twig_Environment;

class UserManagerService
{
    /**
     * @var PasswordEncoderInterface
     */
    private $passwordEncoder;
    /**
     * @var Swift_Mailer
     */
    private $mailer;
    /**
     * @var EntityManager
     */
    private $entityManager;
    /**
     * @var Twig_Environment
     */
    private $twig;
    private $noReplyAddress;

    public function __construct(
        UserPasswordEncoderInterface $passwordEncoder,
        Swift_Mailer $mailer,
        EntityManagerInterface $entityManager,
        Twig_Environment $twig,
        $noReplyAddress)
    {
        $this->passwordEncoder = $passwordEncoder;
        $this->mailer = $mailer;
        $this->entityManager = $entityManager;
        $this->twig = $twig;
        $this->noReplyAddress = $noReplyAddress;
    }

    public function resetPassword(User $user)
    {

        $newPassword = bin2hex(random_bytes(10));
        $newEncodedPassword = $this->passwordEncoder->encodePassword(
            $user, $newPassword
        );
        $user->setPassword($newEncodedPassword);
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $message = (new Swift_Message('Réinitialisation du mot de passe'))
            ->setFrom($this->noReplyAddress)
            ->setTo($user->getMail())
            ->setBody(
                $this->twig->render(
                // templates/emails/registration.html.twig
                    'emails/reset-password.html.twig',
                    [
                        'new_password' => $newPassword,
                        'login'=> $user->getUsername(),
                        ]
                ),
                'text/html'
            );

        $this->mailer->send($message);


    }

    public function findAllRole(string $role): array
    {
        $allUsers = $this->entityManager->getRepository(User::class)->findAllRole($role);
        return $allUsers;
    }
}