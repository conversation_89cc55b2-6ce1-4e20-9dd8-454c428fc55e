<?php

namespace App\Service;

use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;

class EnvoiMailReportingService
{
    /**
     * @var \Twig\Environment
     */
    private $twig;

    public function __construct(
        EntityManagerInterface $entityManager,
        \Twig\Environment $environment,
        \Swift_Mailer $mailer,
        string $noReplyAddress
    ) {
        $this->entityManager = $entityManager;
        $this->twig = $environment;
        $this->noReplyAddress = $noReplyAddress;
        $this->mailer = $mailer;
    }

    public function envoiMailReporting()
    {
        $dateOfToday = new \DateTime();
        $taskReporting = $this->entityManager->getRepository(Task::class)->getDailyTaskReporting($dateOfToday);
        $userTask = [];

        foreach ($taskReporting as $task) {
            $userTask[$task["user_name"]][] = $task;
         }

        $totalHoursByUser = array_map(function ($tasks) {
            return array_sum(array_column($tasks, 'duration'));
        } ,$userTask);

        $projectMangerEmails = $this->entityManager
            ->getRepository(User::class)
            ->findMailByRole(User::ROLE_PROJECT_MANAGER);

        $message = (new \Swift_Message('Reporting du '. $dateOfToday->format('d-m-Y')))
            ->setFrom($this->noReplyAddress)
            ->setTo(array_column($projectMangerEmails, 'mail'))
            ->setBody(
                $this->twig->render(
                    'emails/mail-reporting.html.twig',
                    [
                        'date' => $dateOfToday,
                        'userTasks' => $userTask,
                        'totalHoursByUser' => $totalHoursByUser
                    ]
                ),
                'text/html'
            );

        $this->mailer->send($message);
    }
}