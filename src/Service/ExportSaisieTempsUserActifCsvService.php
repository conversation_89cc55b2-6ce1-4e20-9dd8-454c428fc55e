<?php

namespace App\Service;

use App\Repository\UserRepository;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\SerializerInterface;

class ExportSaisieTempsUserActifCsvService
{
    private $userRepository;
    private $serializer;
    private $appKernel;
    private $daysOfWeekMakerService;
    private $filename;

    public function __construct(UserRepository $userRepository, SerializerInterface $serializer,  KernelInterface $appKernel, DaysOfWeekMakerService $daysOfWeekMakerService)
    {
        $this->userRepository = $userRepository;
        $this->serializer = $serializer;
        $this->appKernel = $appKernel;
        $this->daysOfWeekMakerService = $daysOfWeekMakerService;
        $this->filename = 'saisie_temps_' . (new \DateTimeImmutable())->format('Ymd');
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function execute(\DateTime $startDate, $nbDay = 7): void
    {
        $this->prepareDirectory($destinationDirectory = $this->appKernel->getProjectDir() . '/var/export/');
        $csvData = $this->userRepository->getUserTaskTimeOfWeek($dateOfWeek = $this->daysOfWeekMakerService->generateDaysOfWeekArray($startDate, $nbDay));
        array_walk($csvData, function (&$data) use ($dateOfWeek) {
            $data = $this->manageCsv($data, $dateOfWeek);
        });
        $this->makeCsvFile($destinationDirectory . '/' . $this->filename, $csvData);
    }

    private function manageCsv($data, $durationArray): array
    {
        $translationDay = DaysOfWeekMakerService::FRENCH_DAYS_TRANSLATOR;
        foreach ($durationArray as $dayName => $day) {
            $data[$translationDay[$dayName] . $day->format(' d/m')] = $data[$dayName];
            unset($data[$dayName]);
        }
        uksort($data, function ($key) {
            return $key === 'total' ? 1 : 0;
        });
        return $data;
    }

    private function prepareDirectory(string $directoryAbsolutePath): void
    {
        $filesystem = new Filesystem();
        if (!$filesystem->exists($directoryAbsolutePath)) {
            $filesystem->mkdir($directoryAbsolutePath);
        }
    }

    private function makeCsvFile(string $absolutePath, array $data): void
    {
        $csvContent = $this->serializer->serialize($data, 'csv', [CsvEncoder::DELIMITER_KEY => ',']);
        file_put_contents($absolutePath . '.csv', $csvContent);
    }
}