<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Twig_Environment;

class ExportCongeCsvService extends ExportCsv
{
    public function generateCsv($conges)
    {
        $fichier = $this->ouvertureDuCsv();

        $congesParUser = $this->getCongesParUser($conges);
        $congesFinaux = $this->getCongesParMois($congesParUser);
        $header=["Nom - Prenom","Janvier","Fevrier","Mars","Avril","<PERSON>","Juin","<PERSON>ille<PERSON>","Aout","Septembre","Octobre","Novembre","Decembre"];
        $this->setHeader($header);
        
        $datas=$this->prepareDatas($congesFinaux);
        foreach ( $datas as $data){
            $this->setData($data);
        }
            
        $response = $this->fermetureDuCsv();
        return ($response);
    }

    private function getTabVide()
    {   
        return $tab=[ 
            "01" => [],
            "02" => [],
            "03" => [],
            "04" => [],
            "05" => [],
            "06" => [],
            "07" => [],
            "08" => [],
            "09" => [],
            "10" => [],
            "11" => [],
            "12" => []   ];    
    }

    private function getCongesParUser($conges)
    {
        $congesParUser=[];
        foreach ($conges as $conge){
            $congesParUser[$conge['idUser']][] = $conge;
        }

        return $congesParUser;
    }

    private function getCongesParMois($congesParUser)
    {   
        $congesFinaux=[];
        foreach ($congesParUser as $congeUser){
            $congeParMois=$this->getTabVide();
            $nomEtPrenom = $congeUser[0]['name'].' - '.$congeUser[0]['firstName'];
            foreach ( $congeUser as $conge){
                $mois = $conge['dateDebut']->format('m');
                $congeParMois[$mois][]=$conge;
            }
            $congesFinaux[$nomEtPrenom]=$congeParMois;
        }

        return $congesFinaux;
    }

    private function prepareDatas($congesFinaux)
    {
        $datas = [];
        foreach ( $congesFinaux as $user=>$mois){
            $ligneUser = [];
            $ligneUser[] = $user;
            foreach ( $mois as $unMois){
                if (empty($unMois)){
                    $ligneUser[] = "";
                    continue;
                }
             
                $congesUser = '';
                foreach ($unMois as $unConge){
                    $congesUser.= 'du '.$unConge['dateDebut']->format('d').' au '.$unConge['dateFin']->format('d').'  ';
                }
                $ligneUser[] = $congesUser; 
            }
            $datas[] = $ligneUser;
        }
        return $datas;
    }

    protected function getFileName()
    {
        return 'Conge';
    }
}