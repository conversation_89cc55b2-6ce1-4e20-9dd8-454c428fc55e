<?php

namespace App\Service;

use phpDocumentor\Reflection\Types\ArrayKey;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Twig_Environment;

class ExportSuiviTempsCsvService extends ExportCsv
{
    /**
     * La methode generateCsv sert à créer un fichier csv avec les données recherchées précedemment 
     * dans l'onglet recherche de l'extranet 
     * 
     * @param array $tasks                      La variable contient les données à exporter dans le fichier csv
     * @return BinaryFileResponse $response     La variable renvoie un BinaryFileResponse      
     */

    public function generateCsv($tasks,$cumulativeSearch=false)
    {
        $this->ouvertureDuCsv();

        $datas = $this->prepareDatas($tasks,$cumulativeSearch);


        $headers = array_keys(reset($datas));
        $this->setHeader($headers);
        

        foreach($datas as $data){
            $this->setData($data);
        }
            
        $response = $this->fermetureDuCsv();
        return $response;
    }

    private function prepareDatas($tasks,$cumulativeSearch)
    {
        if($cumulativeSearch){
            return array_map(function($key, $e) use ($cumulativeSearch) { return [
                $this->translator->trans("search.table.$cumulativeSearch") => $key,
                'Période'=> $e['resume']['date_from'] == $e['resume']['date_to']
                    ? $e['resume']['date_from']->format('Y-m-d')
                    : sprintf("%s au %s", $e['resume']['date_from']->format('Y-m-d') , $e['resume']['date_to']->format('Y-m-d')),
                'Durée'=> $e['resume']['duration'],
            ];} , array_keys($tasks), $tasks);
        }

        foreach($tasks as $key=>$value){
            $date = $tasks[$key]['date'];
            $dateString = $date->format('Y-m-d');
            $tasks[$key]['date'] = $dateString;
        }
        return $tasks;
    }

    protected function getFileName()
    {
        return 'Recherche';
    }
}
