<?php

namespace App\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Exception\ReportingNotFoundException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Twig\Environment;

class ReportingService
{
    public const URL_API_GOUV = 'https://calendrier.api.gouv.fr/jours-feries/';
    public const ZONE_FRANCE = 'metropole';
    private const USER_EMAILS_NOT_TO_SEND_REPORTING_TO = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    private $entityManager;

    private $twig;

    private $noReplyAddress;

    private $mailer;

    private $client;

    public function __construct(
        HttpClientInterface $client,
        EntityManagerInterface $entityManager,
        Environment $environment,
        $noReplyAddress,
        \Swift_Mailer $mailer
    ) {
        $this->entityManager = $entityManager;
        $this->twig = $environment;
        $this->noReplyAddress = $noReplyAddress;
        $this->mailer = $mailer;
        $this->client = $client;
    }


    public function getReporting(\DateTimeImmutable $dateTime)
    {
        $userTasks = $this->entityManager->getRepository(User::class)->getTaskReportingByUser($dateTime);
        $userTotals = $this->entityManager->getRepository(User::class)->getTasksTotalByUser($dateTime);
        foreach ($userTotals as $idUser => $total) {
            $userTasks[$idUser]['total'] = $total['total'];
        }
        return $userTasks;
    }

    public function getReportingBetweenDates(?\DateTimeInterface $startDate, ?\DateTimeInterface $endDate): array
    {
        if (!$startDate && !$endDate) {
            $reportingDate = new \DateTimeImmutable();
            return $this->getReporting($reportingDate);
        }

        if ($startDate && !$endDate) {
            return $this->getReporting(\DateTimeImmutable::createFromMutable($startDate));
        }

        if ($endDate && !$startDate) {
            return $this->getReporting(\DateTimeImmutable::createFromMutable($endDate));
        }

        $tasks = $this->entityManager->getRepository(Task::class)->findTasksBetweenDates($startDate, $endDate);
        $tasksByUser = [];
        foreach ($tasks as $task) {
            $userId = $task['user']['id'];
            $clientId = $task['client']['id'];
            $projectId = $task['project']['id'];

            // Initialisation pour l'utilisateur
            if (!isset($tasksByUser[$userId])) {
                $tasksByUser[$userId] = [
                    'id' => $userId,
                    'name' => $task['user']['name'],
                    'firstName' => $task['user']['firstName'],
                    'clients' => [],
                    'total' => 0,
                ];
            }

            // Initialisation pour le client de cet utilisateur
            if (!isset($tasksByUser[$userId]['clients'][$clientId])) {
                $tasksByUser[$userId]['clients'][$clientId] = [
                    'id' => $clientId,
                    'name' => $task['client']['name'],
                    'projects' => [],
                    'total' => 0,
                ];
            }

            // Initialisation pour le projet de ce client
            if (!isset($tasksByUser[$userId]['clients'][$clientId]['projects'][$projectId])) {
                $tasksByUser[$userId]['clients'][$clientId]['projects'][$projectId] = [
                    'id' => $projectId,
                    'name' => $task['project']['name'],
                    'tasks' => [],
                    'total' => 0,
                ];
            }

            // Ajout de la tâche au projet
            $tasksByUser[$userId]['clients'][$clientId]['projects'][$projectId]['tasks'][] = [
                'id' => $task['id'],
                'subject' => $task['subject'],
                'date' => $task['date'],
                'duration' => $task['duration'],
                'refMantis' => $task['refMantis'],
                'typeInter' => [
                    'id' => $task['typeInter']['id'],
                    'name' => $task['typeInter']['name'],
                ],
            ];

            // Mise à jour des totaux
            $tasksByUser[$userId]['clients'][$clientId]['projects'][$projectId]['total'] += $task['duration'];
            $tasksByUser[$userId]['clients'][$clientId]['total'] += $task['duration'];
            $tasksByUser[$userId]['total'] += $task['duration'];
        }

        return array_values($tasksByUser);
    }

    public function relanceMail(\DateTimeImmutable $dateRelance): int
    {

        $userTotals = $this->getIncompleteReporting($dateRelance);
        $message = (new \Swift_Message('Saisie des temps incompléte'))
            ->setFrom($this->noReplyAddress)
            ->setTo(array_column($userTotals, 'mail'))
            ->setBody(
                $this->twig->render(
                // templates/emails/registration.html.twig
                    'emails/relance-saisie-temps.html.twig',
                    [
                        'userTotals' => $userTotals,
                        'dateRelance' => $dateRelance,
                    ]
                ),
                'text/html'
            );

        $this->mailer->send($message);

        return count($userTotals);
    }

    public function getIncompleteReporting(\DateTimeImmutable $dateRelance)
    {
        return $this->entityManager->getRepository(User::class)->getIncompleteReporting($dateRelance);
    }

    function numberOfWorkingDays(string $from, string $to): int
    {
        $workingDays = [1, 2, 3, 4, 5];

        $from = new \DateTime($from);
        $to = new \DateTime($to);
        $to->modify('+1 day');
        $interval = new \DateInterval('P1D');
        $periods = new \DatePeriod($from, $interval, $to);

        $days = 0;
        foreach ($periods as $period) {
            if (!in_array($period->format('N'), $workingDays)) continue;
            $days++;
        }

        return $days;
    }

    public function getPublicHolidays($year)
    {
        $response = $this->client->request('GET', self::URL_API_GOUV . '/' . self::ZONE_FRANCE . '/' . $year . '.json');

        return array_keys(json_decode($response->getContent(), true));
    }

    public function createTasksArrayForAlexisExport(\DateTimeImmutable $reportingDate, array $tasks): array
    {
        $tasksByUser = [];
        foreach ($tasks as $value) {
            $tasksByUser[$value['username']][] = $value;
        }

        $numberWorkingDays = $this->numberOfWorkingDays(date('Y-m-01', $reportingDate->getTimestamp()), date('Y-m-t', $reportingDate->getTimestamp()));

        $totalHours = $numberWorkingDays * 7;
        $publicHolidays = $this->getPublicHolidays($reportingDate->format('Y'));

        $formatPublicHolidays = array_map(function ($e) {
            return substr($e, 0, 7);
        }, $publicHolidays);

        $publicHolidaysNumber = 0;
        foreach ($formatPublicHolidays as $formatPublicHoliday) {
            if ($reportingDate->format('Y-m') == $formatPublicHoliday) {
                $publicHolidaysNumber++;
            }
        }

        $hoursPublicHoliday = $publicHolidaysNumber * 7;

        foreach ($tasksByUser as $username => $task) {
            $tasksByUser[$username]['total'] = 0;
            $key = 0;
            foreach ($task as $key => $value) {
                if (str_contains($value['duration'], '.')) {
                    $percentage = floatval($value['duration'] * 100 / $totalHours);
                    $tasksByUser[$username][$key]['percentage'] = floatval(number_format($percentage, 2));

                }
                $tasksByUser[$username]['total'] += floatval($value['duration']);
            }
            if ($hoursPublicHoliday > 0) {
                $percentage = floatval($hoursPublicHoliday * 100 / $totalHours);
                $tasksByUser[$username][$key + 1]['username'] = $username;
                $tasksByUser[$username][$key + 1]['matricule'] = $value['matricule'];
                $tasksByUser[$username][$key + 1]['client'] = 'IT ROOM';
                $tasksByUser[$username][$key + 1]['project'] = 'Jours fériés';
                $tasksByUser[$username][$key + 1]['type'] = 'Forfait';
                $tasksByUser[$username][$key + 1]['duration'] = $hoursPublicHoliday;
                $tasksByUser[$username][$key + 1]['percentage'] = floatval(number_format($percentage, 2));
            }
            if ($hoursPublicHoliday > 0) {
                $tasksByUser[$username]['total'] += $hoursPublicHoliday;
            }

            if ($totalHours != $tasksByUser[$username]['total']) {
                $key = $key + 1;
                if ($hoursPublicHoliday > 0) {
                    $key = $key + 2;
                }
                $difference = $totalHours - $tasksByUser[$username]['total'];
                $percentage = floatval($difference * 100 / $totalHours);
                $tasksByUser[$username][$key + 1]['username'] = $username;
                $tasksByUser[$username][$key + 1]['matricule'] = $value['matricule'];
                $tasksByUser[$username][$key + 1]['client'] = 'IT ROOM';
                $tasksByUser[$username][$key + 1]['project'] = 'Non affecté';
                $tasksByUser[$username][$key + 1]['type'] = 'Forfait';
                $tasksByUser[$username][$key + 1]['duration'] = $difference;
                $tasksByUser[$username][$key + 1]['percentage'] = floatval(number_format($percentage, 2));
            }
        }

        return $tasksByUser;
    }

    public function createCsvAlexis( $tasks, string $date)
    {

        $fileName = 'export_alexis' . $date . '_' . uniqid();

        $fp = fopen('/tmp/' . $fileName, 'w+');

        $header = ['Username', 'Matricule', 'Client', 'Projet', 'Référence projet', 'Type', 'Temps passé', 'Pourcentage'];

        fputcsv($fp, $header, ';');
        foreach ($tasks as $task) {
            unset($task['total']);
            foreach ($task as $value){
                fputcsv($fp, $value, ';');
            }
        }
        fclose($fp);

        $response = new BinaryFileResponse('/tmp/'.$fileName);
        $response->headers->set('Content-Type', 'text/csv');
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $fileName . '.csv'
        );
        return $response;
    }

    /**
     * @throws ReportingNotFoundException
     */
    public function sendReportingEmail(
        array $tasksReporting,
        string $reportingText,
        \DateTimeImmutable $date,
        User $user
    ): \Swift_Message {
        ['to' => $to, 'cc' => $cc] = $this->getReportingEmailRecipients($user, $date);

        $message = (new \Swift_Message(\sprintf('Reporting %s %s', $user, $date->format('d-m-Y'))))
            ->setFrom($this->noReplyAddress)
            ->setTo($to)
            ->setCc($cc)
            ->setBody(
                $this->twig->render(
                    'emails/mail-daily-reporting.html.twig',
                    [
                        'reportingText' => $reportingText,
                        'tasksReporting' => $tasksReporting,
                        'userFullName' => $user->getFirstName().' '.$user->getName(),
                    ]
                ),
                'text/html'
            );

        $this->mailer->send($message);

        return $message;
    }

    /**
     * @throws ReportingNotFoundException
     */
    private function getReportingEmailRecipients(User $user, \DateTimeInterface $date): array
    {
        $tasks = $this->entityManager->getRepository(Task::class)->findBy(['user' => $user, 'date' => $date]);

        if (empty($tasks)) {
            throw new ReportingNotFoundException();
        }

        $projects = \array_unique(\array_map(static function (Task $task) {
            return $task->getProject();
        }, $tasks));

        $users = \array_map(static function (Project $project) {
            return $project->getReportingRecipientEmails();
        }, $projects);

        return [
            'to' => \array_diff(\array_unique(\array_merge(...$users)), self::USER_EMAILS_NOT_TO_SEND_REPORTING_TO),
            'cc' => ['<EMAIL>', $user->getMail()],
        ];
    }

    /**
     * @throws ReportingNotFoundException
     */
    public function getReportingEmailRecipientsForView(User $user, \DateTimeInterface $date): array
    {
        $recipients = $this->getReportingEmailRecipients($user, $date);

        return \array_unique(\array_merge(...\array_values($recipients)));
    }

    public function getUsersWithoutTasksOrConges(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        $nonRegieUsers = $this->entityManager->getRepository(User::class)->findBy(['actif' => 'oui', 'onSite' => true]);

        return $this->entityManager->getRepository(Task::class)->getUsersWithoutTasks($nonRegieUsers, $startDate, $endDate);
    }
}