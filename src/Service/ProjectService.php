<?php

/**
 * Created by PhpStorm.
 * User: glefer
 * Date: 12/07/19
 * Time: 13:45
 */

namespace App\Service;


use App\Entity\Project;
use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use App\Repository\UserRepository;
use DateTime;
use Twig\Environment;

class ProjectService
{
    /**
     * @var ProjectRepository
     */
    private $projectRepository;
    /**
     * @var TaskRepository
     */
    private $taskRepository;
    /**
     * @var Environment
     */
    private $twig;
    private $noReplyAddress;
    /**
     * @var \Swift_Mailer
     */
    private $mailer;
    /**
     * @var UserRepository
     */
    private $userRepository;

    public function __construct(
        ProjectRepository $projectRepository,
        TaskRepository $taskRepository,
        UserRepository $userRepository,
        Environment $environment,
        $noReplyAddress,
        \Swift_Mailer $mailer
    ) {
        $this->projectRepository = $projectRepository;
        $this->taskRepository = $taskRepository;
        $this->twig = $environment;
        $this->noReplyAddress = $noReplyAddress;
        $this->mailer = $mailer;
        $this->userRepository = $userRepository;
    }


    public function sendReportByEmail(?array $to = null)
    {
        $dateReport = (new \DateTime())->format('d/m/Y');
        $projectManagers = $this->userRepository->getProjectManagers();
        $projectAucun = $this->projectRepository->findOneBy(['reference' => 'AUCUN']) ?? new Project();
        $message = (new \Swift_Message(sprintf('[INTERNE]Rapport projets au %s', $dateReport)))
            ->setFrom($this->noReplyAddress)
            ->setTo($to ?? array_column($projectManagers, 'mail'))
            ->setBody(
                $this->twig->render(
                    'emails/project-report.html.twig',
                    [
                        'projects_active' => $this->getReport(Project::STATE_ACTIVE),
                        'projects_standby' => $this->getReport(Project::STATE_STANDBY),
                        'date_report' => $dateReport,
                        'detail_projet_aucun' => $this->getStatistics($projectAucun->getId())
                    ]
                ),
                'text/html'
            );

        $this->mailer->send($message);
    }

    public function getReport(?int $state = null)
    {
        $projects = $this->projectRepository->getReport($state);
        return $projects;
    }

    public function getStatistics(?int $projectId = null)
    {
        if (empty($projectId)) {
            return null;
        }

        $taskSumByCustomer = $this->projectRepository->getTaskSumByCustomer($projectId);
        $taskSumByUser = $this->projectRepository->getTaskSumByUser($projectId);
        $taskDurationSumByTag = $this->taskRepository->getTaskDurationSumByTag($projectId);
        $taskBounds = $this->projectRepository->getTaskBounds($projectId);
        $chargeConsommee = array_sum(array_column($taskSumByUser, 'total'));

        return
            [
                'clients' => $taskSumByCustomer,
                'charge_consommee' => $chargeConsommee,
                'charge_consommee_by_user' => $taskSumByUser,
                'charge_tags' => $taskDurationSumByTag,
                'task_bounds' => $taskBounds,
            ];
    }

    /**
     * Retourne un tableau avec en premiere valeur la date de la tache la plus ancienne et en deuxieme valeur la date de la tache la
     * plus récente
     *
     * @param $tasks
     *
     * @return array
     *
     * @throws \Exception
     */
    public function getDatesDandF(array $tasks)
    {
        $dateD = new DateTime();
        $dateF = $tasks[0]['date'];

        foreach ($tasks as $task) {
            if ($dateD > $task['date']) {
                $dateD = $task['date'];
            } else if ($task['date'] > $dateF) {
                $dateF = $task['date'];
            }
        }

        return [$dateD, $dateF];
    }

    public function calculatePourcentageConsomme(float $chargeConsommee, float $chargeVendue): float
    {
        if ($chargeVendue == 0) {
            return 0;
        }
        return ($chargeConsommee / $chargeVendue) * 100;
    }

    public function calculatePourcentageConsommeByType(array $interventions): array
    {
        foreach ($interventions as &$intervention) {
            $chargeVendue = $intervention['chargeVendue'];
            $chargeConsommee = $intervention['duration'];
            
            if ($chargeVendue !== null && $chargeVendue != 0) {
                $intervention['pourcentageConsomme'] = $this->calculatePourcentageConsomme(floatval($chargeConsommee), floatval($chargeVendue));
            } else {
                $intervention['pourcentageConsomme'] = null; 
            }
        }
        return $interventions;
    }

    public function getInterventionsByType(int $projectId): array
    {
        return $this->taskRepository->getInterventionsByType($projectId);
    }
}
