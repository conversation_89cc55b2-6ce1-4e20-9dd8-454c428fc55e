<?php

namespace App\Service;

class DaysOfWeekMakerService
{
    const FRENCH_DAYS_TRANSLATOR = [
        'Monday' => 'lundi',
        'Tuesday' => 'mardi',
        'Wednesday' => 'mercredi',
        'Thursday' => 'jeudi',
        'Friday' => 'vendredi',
        'Saturday' => 'samedi',
        'Sunday' => 'dimanche',
    ];

    public function generateDaysOfWeekArray(\DateTime $startDate, int $nbDay, array $ignoredDayOfWeek = [6, 7]): array
    {
        $daysOfWeek = [];
        $actualDay = 1;
        if (!in_array($startDate->format('N'), $ignoredDayOfWeek)) {
            $daysOfWeek[$startDate->format('l')] = clone $startDate;
        }
        while ($actualDay < $nbDay) {
            $startDate->modify('+1 day');
            if (!in_array($startDate->format('N'), $ignoredDayOfWeek)) {
                $daysOfWeek[$startDate->format('l')] = clone $startDate;
            }
            $actualDay++;
        }
        return $daysOfWeek;
    }
}