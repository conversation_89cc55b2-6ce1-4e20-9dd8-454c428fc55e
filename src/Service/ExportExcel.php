<?php

namespace App\Service;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Symfony\Contracts\Translation\TranslatorInterface;

class ExportExcel
{
    /** @var Spreadsheet $spreadsheet */
    protected $spreadsheet;

    /** @var Worksheet $sheet */
    protected $sheet;
    protected $fileName;
    /** @var TranslatorInterface $translator */
    private $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }


    public function create()
    {
        $this->spreadsheet = new Spreadsheet();
        $this->sheet = $this->spreadsheet->getActiveSheet();
    }


    public function save(): string
    {
        $writer = new Xlsx($this->spreadsheet);
        $filename = 'Export_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filepath = sys_get_temp_dir() . '/' . $filename;
        $writer->save($filepath);
        return $filepath;
    }

    public function setHeaders(array $headers): void
    {
        $column = 'A';
        $lastFilledColumn = 'A';
        foreach ($headers as $header) {
            $lastFilledColumn = $column;
            $this->sheet->setCellValue($column . '1', $header);
            $column++;
        }

        $this->sheet->getStyle('A')
            ->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
        $this->sheet->getStyle('A1:' . $lastFilledColumn . '1')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('145a8e');

        $this->sheet->getStyle('A1:' . $lastFilledColumn . '1')->getFont()
            ->setBold(true)
            ->getColor()->setRGB('FFFFFF');


        foreach ($this->sheet->getColumnIterator('B', 'H') as $key) {
            $this->sheet->getColumnDimension($key->getColumnIndex())->setAutoSize(true);
        }
    }

    public function addRows(array $data, int $startRow = 2, bool $collapsed = false): int
    {
        $row = $startRow;
        foreach ($data as $rowData) {
            $column = 'A';
            $lastFilledColumn = 'A';
            foreach ($rowData as $cellData) {
                $lastFilledColumn = $column;
                $this->sheet->setCellValue($column . $row, $cellData);
                $column++;
            }

            if ($collapsed) {
                $this->sheet->getRowDimension($row)->setOutlineLevel(1);
                $this->sheet->getRowDimension($row)->setVisible(false);
                $this->sheet->getStyle("A$row:" . $lastFilledColumn . $row)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('e5e5e5');
                if ($row === $startRow) {
                    $this->sheet->getStyle("A$row:" . $lastFilledColumn . $row)->getFont()
                        ->setBold(true);
                }


            }

            $row++;
        }

        $this->sheet->getRowDimension($row)->setCollapsed(true);
        return $row;
    }

    public function export(array $data, ?string $cumulativeSearch = null): string
    {
        $this->create();

        if (!empty($cumulativeSearch)) {
            $this->exportCumulativeSearch($data, $cumulativeSearch);
        } else {
            $this->exportSearch($data);
        }


        return $this->save();
    }

    public function exportCumulativeSearch($data, $cumulativeSearch)
    {
        $this->setHeaders([$this->translator->trans("search.table.$cumulativeSearch"), 'Période', 'Durée']);

        $currentRow = 2;
        foreach ($data as $id => $group) {

            $currentRow = $this->addRows([
                [
                    $id,
                    $group['resume']['date_from'] == $group['resume']['date_to']
                        ? $group['resume']['date_from']->format('Y-m-d')
                        : sprintf("%s au %s", $group['resume']['date_from']->format('Y-m-d'), $group['resume']['date_to']->format('Y-m-d')),
                    $group['resume']['duration'],
                ]
            ], $currentRow);

            $currentRow = $this->addRows(
                [
                    'headers' => ['Sujet', 'Utilisateur', 'Date', 'Référence ticket', 'Durée', 'Type d\'intervention', 'Client', 'Projet'],
                ] + array_map(
                    function ($e) {
                        return [
                            $e['subject'],
                            $e['user_firstname'] . ' ' . $e['user_name'],
                            $e['date']->format('Y-m-d'),
                            $e['refMantis'],
                            $e['duration'],
                            $e['typeinter_name'],
                            $e['client_name'],
                            $e['project_name']
                        ];
                    },
                    $group['details']
                ),
                $currentRow,
                true
            );
        }
    }

    private function exportSearch(array $data)
    {
        $this->setHeaders(['Sujet', 'Utilisateur', 'Date', 'Référence ticket', 'Durée', 'Type d\'intervention', 'Client', 'Projet']);
        foreach ($data as $datum) {
            $this->addRows([
                [
                    $datum['subject'],
                    $datum['user_firstname'] . ' ' . $datum['user_name'],
                    $datum['date']->format('Y-m-d'),
                    $datum['refMantis'],
                    $datum['duration'],
                    $datum['typeinter_name'],
                    $datum['client_name'],
                    $datum['project_name']
                ]
            ]);
        }
    }

}