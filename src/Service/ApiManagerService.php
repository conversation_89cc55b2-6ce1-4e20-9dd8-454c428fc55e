<?php


namespace App\Service;


use Symfony\Component\HttpFoundation\JsonResponse;

class ApiManagerService
{
    /**
     * Check if all the required parameters for the "average" API are sent.
     *
     * @param array $parameters The request parameters
     * @return bool
     */
    public function checkAverageApiParameters(array $parameters): bool
    {
        if (empty($parameters)) {
            return false;
        }

        if (!isset($parameters['tickets'])) {
            return false;
        }

        if (isset($parameters['end_date']) && !isset($parameters['start_date'])) {
            return false;
        }

        return true;
    }

    /**
     * Build the API response.
     *
     * @param array $data The data of the response
     * @param int $code The return code of the response
     * @return JsonResponse
     */
    public function buildResponse(array $data, int $code): JsonResponse
    {
        return JsonResponse::create($data, $code);
    }
}
