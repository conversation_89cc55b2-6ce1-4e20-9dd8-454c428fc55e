<?php

namespace App\Client;

use GuzzleHttp\Client;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class MantisApiClient
{

    //const API_TOKEN = '3JjECRgxuFkvc-OcwIdYKlBP2fR6iYer';
    const API_TOKEN = 'FzNl8-JSYtzhCG5NZ7s9PjglAnXZJFrQ'; #Re7

    /**
     * @var Client
     */
    private $guzzleClient;

    /**
     * @var object|string
     */
    private $currentUser;

    public function __construct(string $apiUrl, TokenStorageInterface $tokenStorage)
    {
        $this->currentUser = $tokenStorage->getToken()->getUser();
        $this->guzzleClient = new Client([
            'base_uri' => $apiUrl.'api/rest/'
        ]);
    }

    /**
     * @return array
     */
    public function getAllProjects()
    {
        return $this->request('get', 'projects');
    }

    /**
     * @param int $projectId
     *
     * @return array
     */
    public function getIssueByProject(int $projectId)
    {
        return $this->request('get', 'issues?project_id='.$projectId);
    }

    /**
     * @param array $issue
     *
     * @return array
     */
    public function createMinimalIssue(array $issue)
    {
        $data = [
            'summary' => $issue['summary'],
            'description' => $issue['description'],
            'category' => ['name' => 'General'],
            'project' => ['id' => $issue['project']],
            'custom_fields' => [
                [
                    'field' => [
                        'id' => 1,
                        'name' => 'Charge Estimée en Heures'
                    ],
                    'value' => '0'
                ],
                [
                    'field' => [
                        'id' => 2,
                        'name' => 'Temps passé en Heures'
                    ],
                    'value' => '0'
                ]
            ]
        ];

        return $this->request('post', 'issues', json_encode($data));
    }

    /**
     * @param int    $issueId
     * @param string $status
     *
     * @return array
     */
    public function updateMinimalIssue(int $issueId, string $status)
    {
        $data = [
            'handler' => [
                'name' => ''
            ],
            'status' => [
                'name' => $status
            ]
        ];

        return $this->request('patch', 'issues/'.$issueId, json_encode($data));
    }

    /**
     * @param string      $method
     * @param string      $path
     * @param string|null $params
     *
     * @return array
     */
    private function request(string $method, string $path, string $params = null)
    {
        $methodName = strtolower($method);
        $response = $this->guzzleClient->$methodName(
            $path,
            [
                'headers' => ['Authorization' => $this->currentUser->getMantisApiKey() ?? self::API_TOKEN],
                'json' => json_decode($params, true),
                'timeout' => 10
            ]
        );

        return json_decode($response->getBody(), true);
    }
}