<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

class JourFerieValidator extends ConstraintValidator 
{
    private $kernel;

    public function __construct(KernelInterface $kernel)
    {
        $this->kernel = $kernel;
    }
    
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof JourFerie) {
            throw new UnexpectedTypeException($constraint, JourFerie::class);
        }
        
        if (null === $value || '' === $value) {
            return;
        }
        
        if (!$value instanceof \DateTimeInterface) {
            throw new UnexpectedValueException($value, 'string');
        }        
        $fichierCSV = $this->kernel->getProjectDir()."/jours-feries-seuls.csv";
        
        if (($handle = fopen($fichierCSV, "r")) !== FALSE) {
            $dateSaisie = $value->format('Y-m-d');
            while (($data = fgetcsv($handle, $length=36,$delimiter="," )) !== FALSE){   
                if ($data[0] !== null &&  $data[0] === $dateSaisie) {
                    $this->context
                        ->buildViolation($constraint->message)
                        ->addViolation();
                        fclose($handle);
                        return;
                }
            }
        }
        fclose($handle);
    }  
}
