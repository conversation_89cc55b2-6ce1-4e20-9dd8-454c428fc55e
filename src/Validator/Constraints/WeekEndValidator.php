<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\HttpKernel\KernelInterface;


class WeekEndValidator extends ConstraintValidator 
{
    private $kernel;

    public function __construct(KernelInterface $kernel)
    {
        $this->kernel = $kernel;
    }

    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof WeekEnd) {
            throw new UnexpectedTypeException($constraint, WeekEnd::class);
        }
        
        if (null === $value || '' === $value) {
            return;
        }
        
        if (!$value instanceof \DateTime) {
            throw new UnexpectedValueException($value, 'string');
        }        

        if($value->format("w") ==6 || $value->format("w")==0){
            $this->context
            ->buildViolation($constraint->message)
            ->addViolation();
            return;
        }     
    }
}