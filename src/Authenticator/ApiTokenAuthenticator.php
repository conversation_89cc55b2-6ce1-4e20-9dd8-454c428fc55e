<?php

namespace App\Authenticator;

use App\Entity\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Guard\AbstractGuardAuthenticator;

class ApiTokenAuthenticator extends AbstractGuardAuthenticator
{
    const API_TOKEN_HEADER = 'api-key';

    private $apiToken;

    public function __construct(string $apiToken)
    {
        $this->apiToken = $apiToken;
    }

    public function supports(Request $request): bool
    {
        return $request->headers->has(self::API_TOKEN_HEADER);
    }

    public function getCredentials(Request $request)
    {
        return $request->headers->get(self::API_TOKEN_HEADER);
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, $providerKey)
    {
        return null;
    }

    public function getUser($credentials, UserProviderInterface $userProvider): ?User
    {
        if ($credentials === $this->apiToken) {
            $apiUser = new User();
            $apiUser->setRoles(['ROLE_API']);

            return $apiUser;
        }

        return null;
    }

    public function checkCredentials($credentials, UserInterface $token): bool
    {
        return $credentials === $this->apiToken;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): JsonResponse
    {
        throw new AuthenticationException('Invalid API token.', Response::HTTP_UNAUTHORIZED);
    }

    public function start(Request $request, AuthenticationException $authException = null)
    {
        throw new AuthenticationException('API token required.', Response::HTTP_UNAUTHORIZED);
    }

    public function supportsRememberMe(): bool
    {
        return false;
    }
}