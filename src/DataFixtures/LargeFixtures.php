<?php

namespace App\DataFixtures;

use App\Entity\Client;
use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TypeInter;
use App\Entity\User;
use DateTime;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\Persistence\ObjectManager;
use Faker\Factory;
use Faker\Generator;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;

class LargeFixtures extends Fixture
{

    const FAKER_NB_USERS = 50;
    const FAKER_NB_CUSTOMERS = 50;
    const FAKER_NB_PROJECTS = 100;
    const FAKER_NB_TYPE_INTER = 10;
    const FAKER_NB_TASKS = 5000;

    const REFERENCE_USER = 'user-';
    const REFERENCE_CUSTOMER = 'customer-';
    const REFERENCE_PROJECTS = 'projects-';
    const REFERENCE_TYPE_INTER = 'typeinter-';

    /**
     * @var UserPasswordEncoderInterface
     */
    private $encoder;

    /**
     * @var ObjectManager $manager
     */
    private $manager;

    /**
     * AppFixtures constructor.
     * @param UserPasswordEncoderInterface $userPasswordEncoder
     */
    public function __construct(UserPasswordEncoderInterface $userPasswordEncoder)
    {
        $this->encoder = $userPasswordEncoder;
    }

    public function load(ObjectManager $manager)
    {
        $this->manager = $manager;
        $faker = Factory::create('fr_FR');

        $this->createUsers($faker);
        $this->createCustomers($faker);
        $this->createProjects($faker);
        $this->createTypeInter($faker);
        $this->createTasks($faker);
        $manager->flush();

        return true;


    }

    private function createUsers(Generator $faker)
    {
        $roles = ['ROLE_USER', 'ROLE_ADMIN'];
        for ($i = 0; $i < self::FAKER_NB_USERS; $i++) {
            $user = new User();
            $user->setActif('oui');
            $user->setUsername($faker->userName);
            $user->setName($faker->lastName);
            $user->setFirstName($faker->firstName);
            $user->setMail($faker->email);
            $user->setPassword($this->encoder->encodePassword($user, $faker->password));
            $user->setRoles([$roles[array_rand($roles)]]);
            $user->setDuration(rand(0, 7));
            $user->setOnSite($faker->boolean);
            $this->manager->persist($user);
            $this->setReference(self::REFERENCE_USER . $i, $user);
        }

        // Cypress test account
        $user = new User();
        $user->setActif('oui');
        $user->setUsername($faker->userName);
        $user->setName($faker->name);
        $user->setFirstName($faker->firstName);
        $user->setMail('<EMAIL>');
        $user->setPassword($this->encoder->encodePassword($user, $faker->password));
        $user->setRoles([$roles[array_rand($roles)]]);
        $user->setDuration(rand(0, 7));
        $this->manager->persist($user);

    }

    private function createCustomers(Generator $faker)
    {
        for ($i = 0; $i < self::FAKER_NB_CUSTOMERS; $i++) {
            $client = new Client();
            $client->setActif('oui');
            $client->setName($faker->sentence(2));
            $this->manager->persist($client);
            $this->setReference(self::REFERENCE_CUSTOMER . $i, $client);
        }
    }

    private function createProjects(Generator $faker)
    {
        for ($i = 0; $i < self::FAKER_NB_PROJECTS; $i++) {
            $project = new Project();
            $project->setActif(Project::STATE_ACTIVE);
            $project->setName($faker->name);
            $this->manager->persist($project);
            $this->setReference(self::REFERENCE_PROJECTS . $i, $project);
        }
    }

    private function createTypeInter(Generator $faker)
    {
        for ($i = 0; $i < self::FAKER_NB_TYPE_INTER; $i++) {
            $typeInter = new TypeInter();
            $typeInter->setActif('oui');
            $typeInter->setName($faker->name);
            $this->manager->persist($typeInter);
            $this->setReference(self::REFERENCE_TYPE_INTER . $i, $typeInter);
        }
    }

    private function createTasks(Generator $faker)
    {
        $minDate = new DateTime('-1 month');
        $maxDate = new DateTime('+1 month');

        for ($i = 0; $i < self::FAKER_NB_TASKS; $i++) {
            $task = new Task();
            $task->setSubject($faker->text(255));
            $task->setContent($faker->text(255));
            $task->setrefMantis($faker->numberBetween(1000, 1050));
            $task->setDuration(rand(1, 4));
            $task->setDate($faker->dateTimeBetween($minDate, $maxDate));
            $task->setCreatedAt($task->getDate());
            $task->setUser($this->getReference(self::REFERENCE_USER . (rand(0, self::FAKER_NB_USERS - 1))));
            $task->setClient($this->getReference(self::REFERENCE_CUSTOMER . (rand(0, self::FAKER_NB_CUSTOMERS - 1))));
            $task->setTypeInter($this->getReference(self::REFERENCE_TYPE_INTER . (rand(0, self::FAKER_NB_TYPE_INTER - 1))));
            $task->setProject($this->getReference(self::REFERENCE_PROJECTS . (rand(0, self::FAKER_NB_PROJECTS - 1))));
            $this->manager->persist($task);
        }
    }
}
