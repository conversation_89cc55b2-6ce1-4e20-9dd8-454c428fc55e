<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Client;
use App\Entity\Company;
use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TypeInter;
use App\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;
use Faker\Generator;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;

class ReportingFixtures extends Fixture
{
    private $userPasswordEncoder;

    public function __construct(UserPasswordEncoderInterface $userPasswordEncoder)
    {
        $this->userPasswordEncoder = $userPasswordEncoder;
    }

    public function load(ObjectManager $manager)
    {
        $faker = Factory::create('fr_FR');

        $company = new Company();
        $company->setName('IT-Room');
        $company->setProjectEnabled(true);
        $company->setUserEnabled(true);
        $manager->persist($company);
        $this->setReference('company', $company);

        $userManager = $this->createUser($faker, '<EMAIL>');
        $manager->persist($userManager);
        $this->setReference('user-manager', $userManager);

        $user = $this->createUser($faker, '<EMAIL>');
        $manager->persist($user);
        $this->setReference('user-user', $user);

        for ($i = 0; $i < 2; ++$i) {
            $observer = $this->createUser($faker, \sprintf('<EMAIL>', $i));
            $manager->persist($observer);
            $this->setReference(\sprintf('user-observer-%s', $i), $observer);
        }

        $project = new Project();
        $project->setActif(Project::STATE_ACTIVE);
        $project->setName($faker->name);
        $project->setManager($this->getReference('user-manager'));
        $project->setCompany($this->getReference('company'));
        $project->addObserver($this->getReference('user-observer-0'));
        $project->addObserver($this->getReference('user-observer-1'));
        $manager->persist($project);
        $this->setReference('project', $project);

        $customer = new Client();
        $customer->setActif('oui');
        $customer->setName($faker->sentence(2));
        $manager->persist($customer);
        $this->setReference('customer', $customer);

        $typeInter = new TypeInter();
        $typeInter->setActif('oui');
        $typeInter->setName($faker->name);
        $manager->persist($typeInter);
        $this->setReference('type_inter', $typeInter);

        for ($i = 0; $i < 3; ++$i) {
            $task = new Task();

            $task->setSubject($faker->text(255));
            $task->setContent($faker->text(255));
            $task->setDuration(rand(1, 4));
            $task->setCreatedAt(new \DateTimeImmutable());
            $task->setDate($task->getCreatedAt()->setTime(0, 0));
            $task->setUser($this->getReference('user-user'));
            $task->setClient($this->getReference('customer'));
            $task->setProject($this->getReference('project'));
            $task->setTypeInter($this->getReference('type_inter'));

            $manager->persist($task);
        }

        $manager->flush();
    }

    private function createUser(Generator $faker, string $username): User
    {
        $user = new User();
        $user->setActif('oui');
        $user->setUsername($username);
        $user->setName($faker->lastName);
        $user->setFirstName($faker->firstName);
        $user->setMail($username);
        $user->setPassword($this->userPasswordEncoder->encodePassword($user, 'P@ssw0rd'));
        $user->setRoles(['ROLE_USER']);
        $user->setDuration(rand(0, 7));
        $user->setOnSite($faker->boolean);
        $user->setCompany($this->getReference('company'));

        return $user;
    }
}
