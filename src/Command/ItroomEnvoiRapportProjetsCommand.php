<?php

namespace App\Command;

use App\Service\ProjectService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ItroomEnvoiRapportProjetsCommand extends Command
{
    protected static $defaultName = 'itroom:envoi-rapport-projets';
    /**
     * @var ProjectService
     */
    private $projectService;

    public function __construct(ProjectService $projectService, ?string $name = null)
    {
        parent::__construct($name);
        $this->projectService = $projectService;
    }


    protected function configure()
    {
        $this
            ->setDescription('Send a projects report')
            ->addOption('to', null, InputOption::VALUE_OPTIONAL, 'Send mail to', null)
            ->addOption('debug', null, InputOption::VALUE_NONE, 'Display without send mail.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {


        if ($input->getOption('debug')) {
            $reporting = $this->projectService->getReport();
            $table = new Table($output);
            $table->setHeaderTitle(sprintf('Etat des projets au %s', (new \DateTime())->format('d/m/Y')));
            $table
                ->setHeaders(['ID', 'Client','Projet', 'Reference commande', 'Charge vendue','Charge consommée', 'Pourcentage consommé', 'Delta pourcentage consommé','Pourcentage avancement'])
                ->setRows($reporting);
            $table->render();
            return true;

        }

        if (!empty($to = $input->getOption('to'))){
            $to = explode(',',$to);
        }

        $this->projectService->sendReportByEmail($to);
    }
}
