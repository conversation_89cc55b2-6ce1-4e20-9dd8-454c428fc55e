<?php

namespace App\Command;

use Symfony\Component\Console\Command\Command;
use App\Service\CongeManagerService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class ItroomEnvoiCongesEnAttenteCommand extends Command
{
    protected static $defaultName = 'itroom:envoi-mail-conges';
    /**
     * @var CongeManagerService
     */
    private $congeManagerService;

    public function __construct(CongeManagerService $congeManagerService, ?string $name = null)
    {
        parent::__construct($name);
        $this->congeManagerService = $congeManagerService;
    }

    protected function configure()
    {
        $this
            ->setDescription('Envoie un mail aux personnes habilités pour la gestion des congés');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $nbMailEnvoye = $this->congeManagerService->envoieMailEnAttente();

        $io->success(sprintf('%d congé(s) envoyé(s)', $nbMailEnvoye));
    }
}
