<?php

namespace App\Command;

use DateTime;
use DateTimeImmutable;
use Google\Client;
use Google\Service\Sheets;
use IntlDateFormatter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateAssignmentsCommand extends Command
{
    protected static $defaultName = 'app:update-assignments';

    protected function configure()
    {
        $this->setDescription('Met à jour les affectations depuis Google Sheets.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $monthSheetName = ucfirst(IntlDateFormatter::create(
            'fr_FR',
            IntlDateFormatter::LONG,
            IntlDateFormatter::NONE,
            'Europe/Paris',
            IntlDateFormatter::GREGORIAN,
            'LLLL yyyy'
        )->format(new DateTimeImmutable()));

        $client = new Client();
        $client->setAuthConfig(__DIR__.'/../../config/service-account.json');
        $client->addScope(Sheets::SPREADSHEETS_READONLY);

        $service = new Sheets($client);

        $spreadsheetId = '1TEVv7H7VlKigfLFgi2atOnCfPf3E91GCZAf-n4MfIGs';

        // -- 1) Récupération des collaborateurs (B4:B) :
        $rangeNames = $monthSheetName . '!B4:B';
        $responseNames = $service->spreadsheets_values->get($spreadsheetId, $rangeNames);
        $allNames = $responseNames->getValues();

        // -- 2) Récupération de la ligne des dates (F3:3) pour identifier les colonnes/Index
        $rangeRow3 = $monthSheetName . '!F3:3';
        $responseRow3 = $service->spreadsheets_values->get($spreadsheetId, $rangeRow3);
        $row3 = $responseRow3->getValues(); // ligne unique avec les dates

        // 4) Calcul du vendredi prochain (inclus)
        $today = new DateTimeImmutable('today'); // ou new DateTimeImmutable('2025-04-14') pour test
        // Jour de la semaine (1=lun...5=ven...7=dim)
        $dayOfWeek = (int) $today->format('N');
        // Décalage jusqu'à vendredi
        $offset = 5 - $dayOfWeek;
        if ($offset <= 0) {
            // Si on est déjà vendredi (offset=0) ou samedi/dimanche (offset<0),
            // on prend le vendredi de la semaine suivante
            $offset += 7;
        }
        $nextFriday = $today->modify("+$offset days");

        // 5) Collecte des dates (et index de colonnes) entre aujourd'hui et vendredi inclus
        $uniqueDates = [];
        $dateIndexMap = [];

        foreach ($row3[0] as $index => $cell) {

            $date = DateTime::createFromFormat('d/m/Y', $cell);
            if ($date && $date >= $today) {
                if ($date->format('Y-m-d') > $nextFriday->format('Y-m-d')) {
                    break;
                }
                // On ajoute chaque date dans la liste, si pas déjà présente
                $key = $date->format('l Y-m-d');
                if (!in_array($key, $uniqueDates, true)) {
                    $uniqueDates[] = $key;
                    $dateIndexMap[] = $index;
                }
            }
        }

        // Construit la liste des colonnes voulues (matin + aprem)
        $cellIndexes = [];
        foreach ($dateIndexMap as $i) {
            $cellIndexes[] = $i;
            $cellIndexes[] = $i + 1;
        }
        sort($cellIndexes);

        // -- 4) Récupérer maintenant valeurs + notes sur la plage F4:ZZ
        //    via un appel sheets->get() avec le champ "note"
        $spreadsheet = $service->spreadsheets->get($spreadsheetId, [
            'ranges' => [$monthSheetName . '!F4:ZZ'],
            'fields' => 'sheets.data.rowData.values(formattedValue,note)'
        ]);
        $sheet = $spreadsheet->getSheets()[0];    // On suppose que la première sheet est la bonne
        $sheetData = $sheet->getData()[0];        // Première plage demandée
        $rowDataArray = $sheetData->getRowData(); // Tableau des lignes

        // -- 5) Préparer la structure de résultat
        $results = [
            'assignments' => []
        ];

        if (!empty($allNames)) {
            foreach ($allNames as $rowIndex => $rowData) {
                $fullName = $rowData[0] ?? '';
                // Extraction du NOM en majuscules
                if (preg_match('/^([\p{Lu}]+(?:[-\s][\p{Lu}]+)*)\b/u', $fullName, $m)) {
                    $lastName = $m[1];
                } else {
                    $lastName = $fullName;
                }

                if (!isset($results['assignments'][$lastName])) {
                    $results['assignments'][$lastName] = [];
                }

                // Sur la ligne "rowIndex" de la plage F4:ZZ,
                // on va chercher la liste des cellules (chacune a un formattedValue et éventuellement un note)
                $cellValues = $rowDataArray[$rowIndex]->getValues();
                // $cellValues est un tableau de Google\Service\Sheets\CellData

                // Parcourt les colonnes voulues (matin + aprem)
                foreach ($cellIndexes as $colIdx) {
                    $dateFormatted = $row3[0][$colIdx] ?? '';
                    $date = DateTime::createFromFormat('d/m/Y', $dateFormatted);

                    $formatter = new IntlDateFormatter(
                        'fr_FR',                // Locale
                        IntlDateFormatter::FULL,// Style de date (FULL, LONG, MEDIUM, SHORT)
                        IntlDateFormatter::NONE,// Pas besoin de l'heure
                        'Europe/Paris',         // Fuseau horaire
                        IntlDateFormatter::GREGORIAN,
                        'EEEE dd/MM'       // 'EEEE' => nom complet du jour (lundi...), puis la forme AAAA-MM-JJ
                    );

// Formate la date
                    $dateFormatted = $formatter->format($date);
                    $dateFormatted = ucfirst($dateFormatted);

                    if (!isset($results['assignments'][$lastName][$dateFormatted])) {
                        $results['assignments'][$lastName][$dateFormatted] = [];
                    }

                    // Récupère la CellData (peut être null si la cellule est hors plages réelles)
                    $cellData = $cellValues[$colIdx] ?? null;

                    $value = '';
                    $note = '';
                    if ($cellData) {
                        $value = $cellData->getFormattedValue() ?: '';
                        $note  = $cellData->getNote() ?: '';
                    }

                    // Exemple : concaténer valeur + note si vous voulez tout dans un champ
                    // $assignmentValue = $value;
                    // if ($note !== '') {
                    //     $assignmentValue .= ' ('.$note.')';
                    // }

                    // Ou bien stocker un mini-objet : { "value": "...", "comment": "..." }
                    $assignmentValue = [
                        'value'   => $value,
                        'comment' => $note
                    ];

                    $results['assignments'][$lastName][$dateFormatted][] = $assignmentValue;
                }
            }
        }

        // -- 6) Exporter en JSON, enregistrer puis afficher
        $jsonData = json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        file_put_contents(__DIR__.'/../../assignments.json', $jsonData);

        $output->writeln('Affectations mises à jour.');
        return 0;
    }
}