<?php 

namespace App\Command;

use App\Service\WeeklyReportService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class SendWeeklyReportCommand extends Command
{
    protected static $defaultName = 'itroom:send-weekly-report';

    private $rapportHebdomadaireService;

    public function __construct(WeeklyReportService $rapportHebdomadaireService)
    {
        parent::__construct();
        $this->rapportHebdomadaireService = $rapportHebdomadaireService;
    }

    protected function configure()
    {
        $this
            ->setDescription('Envoi un rapport hebdomadaire par email chaque lundi à 8h.')
            ->addOption('to', null, InputOption::VALUE_OPTIONAL | InputOption::VALUE_IS_ARRAY, 'Liste d\'adresses destinataires du mail', [])
            ->addOption('debug', null, InputOption::VALUE_NONE, 'Si activé, affiche le contenu du mail sans l\'envoyer');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Envoi du rapport hebdomadaire');

        if ($input->getOption('debug')) {
            // Récupérer le rapport hebdomadaire
            $weeklyReportData = $this->rapportHebdomadaireService->getWeeklyReport();
            $report = $weeklyReportData['report'];

            // Afficher le tableau des données
            $table1 = new Table($output);
            $table1->setHeaderTitle(sprintf('Rapport hebdomadaire au %s', (new \DateTime())->format('d/m/Y')));
            $table1
                ->setHeaders(['Nom', 'Prénom', 'Client', 'Projet', 'Temps passé'])
                ->setRows($report);
            $table1->render();

            return 0;
        }

        $this->rapportHebdomadaireService->sendWeeklyReportByEmail($input->getOption('to'));

        $io->success('Le rapport hebdomadaire a été envoyé avec succès.');

        return 0;
    }
}
