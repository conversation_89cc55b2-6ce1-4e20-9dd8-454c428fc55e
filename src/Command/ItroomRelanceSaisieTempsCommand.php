<?php

namespace App\Command;

use App\Service\ReportingService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class ItroomRelanceSaisieTempsCommand extends Command
{
    protected static $defaultName = 'itroom:relance-saisie-temps';
    /**
     * @var ReportingService
     */
    private $reportingService;

    public function __construct(ReportingService $reportingService, ?string $name = null)
    {

        parent::__construct($name);
        $this->reportingService = $reportingService;
    }


    protected function configure()
    {
        $this
            ->setDescription('Relance les utilisauteurs n\'ayant pas saisie leurs temps')
            ->addOption('debug', null, InputOption::VALUE_NONE, 'Display without send mail.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $dateRelance = new \DateTimeImmutable('-1 weekday');
        if ($input->getOption('debug')) {
            $reporting = $this->reportingService->getIncompleteReporting($dateRelance);

            $table = new Table($output);
            $table->setHeaderTitle(sprintf('Reporting du %s', $dateRelance->format('d/m/Y')));
            $table
                ->setHeaders(['ID', 'Mail', 'Name', ' FirstName', 'Total'])
                ->setRows($reporting);
            $table->render();
            return true;
        }

        $nbRelances = $this->reportingService->relanceMail($dateRelance);

        $io->success(sprintf('Les relances ont été envoyées par mail [%d personnes]', $nbRelances));
    }
}
