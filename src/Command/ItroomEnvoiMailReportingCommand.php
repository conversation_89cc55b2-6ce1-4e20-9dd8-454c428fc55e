<?php

namespace App\Command;

use App\Service\EnvoiMailReportingService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ItroomEnvoiMailReportingCommand extends Command
{
   public const SUCCESS = 0;

    protected static $defaultName = 'itroom:envoi-mail-reporting';
    /**
     * @var EnvoiMailReportingService
     */
    private $envoiMailReporting;

    public function __construct(EnvoiMailReportingService $envoiMailReporting)
    {
        parent::__construct();
        $this->envoiMailReporting = $envoiMailReporting;
    }

    protected function configure()
    {
        $this->setDescription('Commande qui envoi un mail journalier du Reporting.');

    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
       $this->envoiMailReporting->envoiMailReporting();
       return self::SUCCESS;
    }
}