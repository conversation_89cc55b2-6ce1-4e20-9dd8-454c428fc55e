<?php

namespace App\Command;

use App\Service\EnvoieMailRappelService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use DateTime;
use Symfony\Component\HttpKernel\KernelInterface;

class ItroomEnvoiMailRappelSaisieTempsCommand extends Command
{
    private $kernel;
    private $envoieMailRappelService;
    protected static $defaultName = 'itroom:envoi-mail-rappel-saisie-temps';
    private const SATURDAY = 6;
    private const SUNDAY = 0;
    private const WEEK_END = [self::SATURDAY, self::SUNDAY];

    public function __construct(
        EnvoieMailRappelService $envoieMailRappelService,
        KernelInterface $kernel
    )
    {
        parent::__construct();
        $this->envoieMailRappelService = $envoieMailRappelService;
        $this->kernel = $kernel;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Envoie un mail aux personnes qui n\'ont pas saisie leurs temps');
    }

    protected function execute(InputInterface $input, OutputInterface $output): void
    {
        $io = new SymfonyStyle($input, $output);

        $io->success(sprintf('%d rappel(s) envoyé(s)', $this->rappel()));

        $io->success(sprintf('%d relance de rappel(s) envoyé(s)', $this->relanceRappel()));
    }

    /** J-1 */
    private function rappel(): int
    {
        $dateDebut = date('d.m.Y', strtotime("-1 days"));
        $oDateDebut = new DateTime($dateDebut);

        if (false === $this->verifyDate($oDateDebut)) {
            return 0;
        }

        $dateFin = date('d.m.Y', strtotime("-1 days"));
        $oDateFin = new DateTime($dateFin);
        $oDateFin->setTime(23, 59, 59);

        $users = $this->envoieMailRappelService->getUsersWithDurationLower7ByDate($oDateDebut, $oDateFin);

        $this->envoieMailRappelService->sendMail(
            EnvoieMailRappelService::SUBJECT_RAPPEL,
            $users,
            EnvoieMailRappelService::TYPES[EnvoieMailRappelService::RAPPEL],
            $oDateDebut->format('d-m-Y')
        );

        return count($users);
    }

    /** J-3 */
    private function relanceRappel(): int
    {
        $dateDebut = date('d.m.Y', strtotime("-3 days"));
        $oDateDebut = new DateTime($dateDebut);

        if (false === $this->verifyDate($oDateDebut)) {
            return 0;
        }

        $dateFin = date('d.m.Y', strtotime("-3 days"));
        $oDateFin = new DateTime($dateFin);
        $oDateFin->setTime(23, 59, 59);

        $users = $this->envoieMailRappelService->getUsersWithDurationLower7ByDate($oDateDebut, $oDateFin);

        $this->envoieMailRappelService->sendMail(
            EnvoieMailRappelService::SUBJECT_RELANCE,
            $users,
            EnvoieMailRappelService::TYPES[EnvoieMailRappelService::RELANCE],
            $oDateDebut->format('d-m-Y')
        );

        return count($users);
    }

    private function verifyDate(DateTime $oDateTime): bool
    {
        $now = new DateTime('now');
        $jourActuel = (int) $now->format('w');

        if (in_array($jourActuel, self::WEEK_END)) {
            return false;
        }

        $fichierCSV = $this->kernel->getProjectDir() . '/jours-feries-seuls.csv';

        // Vérification si la date est un jour férié seul
        if (($handle = fopen($fichierCSV, "r")) !== false) {
            while (($data = fgetcsv($handle)) !== false) {
                if (
                    !empty($data[0]) &&
                    $data[0] === $oDateTime->format('Y-m-d') ||
                    $data[0] === $now->format('Y-m-d')
                ) {
                    fclose($handle);
                    return false;
                }
            }

            fclose($handle);
        }

        $jourSemaine = (int) $oDateTime->format('w');

        if (in_array($jourSemaine, self::WEEK_END)) {
            return false;
        }

        return true;
    }
}