<?php

namespace App\Command;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;

class ItroomMakeSuperAdminCommand extends Command
{
    protected static $defaultName = 'itroom:make:super-admin';
    private $userRepository;
    private $em;

    public function __construct(UserRepository $userRepository, EntityManagerInterface $em)
    {
        parent::__construct();
        $this->userRepository = $userRepository;
        $this->em = $em;
    }

    protected function configure()
    {
        $this
            ->setDescription('add ROLE_SUPER_ADMIN to user')
            ->addArgument('mail', InputArgument::OPTIONAL, 'mail of user');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        if (null === $mail = $input->getArgument('mail')) {
            $helper = $this->getHelper('question');
            $question = new Question('Please enter the mail of user you want to promote : ');
            $mail = $helper->ask($input, $output, $question);
        }

        if (null === $user = $this->userRepository->findOneByMail($mail)) {
            $io->error('L\'utilisateur ' . $mail . ' n\'existe pas');
            return 0;
        }
        $user->addRole(User::ROLE_SUPER_ADMIN);

        $this->em->persist($user);
        $this->em->flush();

        $io->success('L\'utilisateur ' . $mail . ' a le rôle ROLE_SUPER_ADMIN');
    }
}
