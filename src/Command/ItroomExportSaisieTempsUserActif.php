<?php

namespace App\Command;

use App\Repository\UserRepository;
use App\Service\ExportSaisieTempsUserActifCsvService;
use App\Service\SendingMailService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ItroomExportSaisieTempsUserActif extends Command
{
    protected static $defaultName = 'itroom:export:saisie-temps-user-actif';
    private $userRepository;
    private $saisieTempsCsvService;
    private $sendingMailService;

    public function __construct(UserRepository $userRepository, ExportSaisieTempsUserActifCsvService $saisieTempsCsvService, SendingMailService $sendingMailService)
    {
        parent::__construct();
        $this->userRepository = $userRepository;
        $this->saisieTempsCsvService = $saisieTempsCsvService;
        $this->sendingMailService = $sendingMailService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Send a csv export with time of all active user')
            ->addOption('to', null, InputOption::VALUE_OPTIONAL | InputOption::VALUE_IS_ARRAY, 'Send mail to', null)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): void
    {
        $this->saisieTempsCsvService->execute(new \DateTime('Friday last week'));
        if (empty($to = $input->getOption('to'))) {
            $to = array_column($this->userRepository->getProjectManagers(), 'mail');
        }
        $this->sendingMailService->generateMailBody('emails/export-saisie-temps.html.twig', ['fileName' => $this->saisieTempsCsvService->getFilename()]);
        $this->sendingMailService->sendingMail('[INTERNE]Saisie des temps de la semaine', $to);
    }
}
