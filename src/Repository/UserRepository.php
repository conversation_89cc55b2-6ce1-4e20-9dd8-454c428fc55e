<?php

namespace App\Repository;

use App\Entity\Task;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Doctrine\ORM\Query\Expr\Join;

/**
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, User::class);
    }

    public function findByActifAndCompanyAndOnSite(int $filter, string $companyName, bool $onSite = null): array
    {
        $qb = $this->createQueryBuilder('u')
            ->where('u.actif IN (:filters)')
            ->setParameter('filters', User::FILTERS_SELECTOR[$filter]);

        if (User::COMPANY_ALL !== $companyName) {
            $qb
                ->leftJoin('u.company', 'c')
                ->andWhere('c.name = :company')
                ->setParameter('company', $companyName)
            ;
        }

        if (is_bool($onSite)) {
            $qb
                ->andWhere('u.onSite = :onSite')
                ->setParameter('onSite', $onSite)
            ;
        }

        return $qb
            ->orderBy('u.name')
            ->getQuery()
            ->execute()
        ;
    }

    public function findByActive(bool $isActive): array
    {
        $qb = $this->createQueryBuilder('u')
            ->select('u.id, u.actif, u.mail, u.username, u.firstName as prenom, u.name as nom, u.roles')
            ->andWhere('u.actif IN (:enabledChoices)')
        ;
        if ($isActive) {
            $qb
                ->setParameter('enabledChoices', ['oui', '1'])
            ;
        } else {
            $qb
                ->setParameter('enabledChoices', ['non', '0'])
            ;
        }

        return $qb
            ->orderBy('u.name')
            ->getQuery()
            ->execute()
            ;
    }

    public function findAllRole(string $role): array
    {
        $qb = $this->createQueryBuilder('u')
            ->select('u.mail')
            ->where('u.roles LIKE :role')
            ->setParameter(":role", '%' . $role . '%')
            ->getQuery();
        return array_column($qb->getScalarResult(), 'mail');
    }

    public function getUserForChoiceList()
    {
        return $this->createQueryBuilder('u')
            ->select('u.id, u.firstName, u.name')
            ->where('u.actif = :actif')
            ->orWhere('u.actif = 1')
            ->setParameter('actif', 'oui')
            ->orderBy('u.firstName, u.name')
            ->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    private function onSite(QueryBuilder &$qb, bool $and = false)
    {
        $and ? $qb->andWhere('u.onSite = 1') : $qb->where('u.onSite = 1');
    }

    public function getTaskReportingByUser(\DateTimeImmutable $date)
    {
        $qb = $this->createQueryBuilder('u', 'u.id')
            ->select('PARTIAL u.{id, firstName, name}')
            ->addSelect('tasks, project, typeInter, client')
            ->leftJoin('u.tasks', 'tasks', Query\Expr\Join::WITH, 'tasks.date IS NULL OR (tasks.date BETWEEN :dateBegin AND :dateEnd)')
            ->leftJoin('tasks.typeInter', 'typeInter')
            ->leftJoin('tasks.project', 'project')
            ->leftJoin('tasks.client', 'client')
            ->where('u.actif IN (:actif)')
            ->setParameters([
                'dateBegin' => $date->format('Y-m-d 00:00:00'),
                'dateEnd' => $date->format('Y-m-d 23:59:00'),
                'actif' => ['oui', 1]
            ])
            ->orderBy('u.firstName');

        return $qb->getQuery()
            ->setHint(Query::HINT_INCLUDE_META_COLUMNS, true)// Utilisé pour récupérer les identifiants sans jointures
            ->getArrayResult();
    }


    public function getTasksTotalByUser(\DateTimeImmutable $date)
    {
        $qb = $this->createQueryBuilder('u', 'u.id')
            ->select('u.id, SUM(tasks.duration) as total')
            ->leftJoin(Task::class, 'tasks', Query\Expr\Join::WITH, 'tasks.user = u.id AND (tasks.date IS NULL OR (tasks.date BETWEEN :dateBegin AND :dateEnd))')
            ->groupBy('u.id')
            ->where('u.actif IN (:actif)')
            ->setParameters([
                'dateBegin' => $date->format('Y-m-d 00:00:00'),
                'dateEnd' => $date->format('Y-m-d 23:59:00'),
                'actif' => ['oui', 1]
            ]);

        return $qb->getQuery()->getArrayResult();
    }

    public function getIncompleteReporting(\DateTimeImmutable $date)
    {
        $qb = $this->createQueryBuilder('u', 'u.id')
            ->select('u.id, u.mail, u.name, u.firstName, SUM(tasks.duration) as total')
            ->leftJoin(Task::class, 'tasks', Query\Expr\Join::WITH, 'tasks.user = u.id AND (tasks.date IS NULL OR (tasks.date BETWEEN :dateBegin AND :dateEnd))')
            ->where('u.actif = :actif')
            ->having('total < 7 OR total IS NULL')
            ->groupBy('u.id')
            ->setParameters([
                'dateBegin' => $date->format('Y-m-d 00:00:00'),
                'dateEnd' => $date->format('Y-m-d 23:59:00'),
                'actif' => 'oui'
            ])
        ;

        $this->onSite($qb, true);
        return $qb->getQuery()->getArrayResult();
    }

    public function getProjectManagers()
    {
        return $this->createQueryBuilder('u')
            ->where('u.roles like :roleProjectManager')
            ->setParameter('roleProjectManager','%'.User::ROLE_PROJECT_MANAGER.'%')
            ->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }


    public function getMailRoleGestionConge()
    {
        $roleAdmin='%ROLE_ADMIN%';
        $roleSuperAdmin='%ROLE_SUPER_ADMIN%';
        $roleGestionConge='%ROLE_GESTION_CONGES%';
        return $this->createQueryBuilder('u')
        ->select('u.mail')
        ->where('u.roles like :roleAdmin OR u.roles like :roleGestionConge OR u.roles like :roleSuperAdmin')
        ->setParameter('roleAdmin',$roleAdmin)
        ->setParameter('roleSuperAdmin',$roleSuperAdmin)
        ->setParameter('roleGestionConge',$roleGestionConge)
        ->orderBy('u.id')
        ->getQuery()->getArrayResult();
    }

    public function findMailByRole(string $role)
    {
        return $this->createQueryBuilder('u')
        ->select('u.mail')
        ->where('u.roles LIKE :roles')
        ->setParameter('roles', '%"'.$role.'"%')
        ->getQuery()->getArrayResult();
    }

    public function findMailByRoleAndCompany(string $role, string $company)
    {
        return $this->createQueryBuilder('u')
            ->select('u.mail')
            ->where('u.roles LIKE :roles')
            ->leftJoin('u.company', 'c')
            ->andWhere('c.name = :company')
            ->setParameter('roles', '%"'.$role.'"%')
            ->setParameter('company', $company)
            ->getQuery()->execute();
    }

    public function getUserTaskTimeOfWeek(array $daysOfWeek)
    {
        $qb = $this->createQueryBuilder('u')->select('u.mail, u.username, u.firstName as prenom, u.name as nom');
        foreach ($daysOfWeek as $dayName => $day)  {
            $qb
                ->addSelect('SUM(CASE WHEN t.date = :' . $dayName . ' THEN t.duration ELSE 0 END) as ' . $dayName)
                ->setParameter($dayName, $day)
            ;
        }

        return $qb
            ->addSelect('COALESCE(SUM(t.duration), 0) as total')
            ->leftJoin(
                'u.tasks',
                't',
                Join::WITH,
                new Query\Expr\Andx([
                    't.date >= :firstDay or t.date is null',
                    't.date <= :lastDay or t.date is null'
                ])
            )
            ->where('u.actif IN (:enabled)')
            ->setParameter('enabled', ['oui', 1])
            ->setParameter('firstDay', reset($daysOfWeek))
            ->setParameter('lastDay', end($daysOfWeek))
            ->groupBy('u.id')
            ->getQuery()->getArrayResult()
        ;
    }

    public function getAllUsersForApi(): array
    {
        return $this->createQueryBuilder('u')
            ->select("u.id, u.mail, CONCAT(u.name, ' ', u.firstName) as username, u.googleId, u.tag, u.actif, u.onSite, c.name as company")
            ->leftJoin('u.company', 'c')
            ->getQuery()->getArrayResult();
    }
}

