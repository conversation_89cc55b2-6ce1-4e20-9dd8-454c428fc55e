<?php

namespace App\Repository;

use App\Entity\Conge;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Symfony\Component\Validator\Constraints\DateTime;

/**
 * @method Conge|null find($id, $lockMode = null, $lockVersion = null)
 * @method Conge|null findOneBy(array $criteria, array $orderBy = null)
 * @method Conge[]    findAll()
 * @method Conge[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CongeRepository extends ServiceEntityRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, Conge::class);
    }

    public function getTousLesConges()
    {
        $etat='refuse';
        
        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name,user.firstName')
        ->leftJoin('c.user','user')
        ->where('c.etat != :etat')
        ->setParameter('etat',$etat)
        ->orderBy('c.dateCreation')
        ->getQuery()->getArrayResult();
    }

    public function getCongeParAnnee(int $annee)
    {
        $dateD=strtotime($annee.'/01/01');
        $dateDebut=date('Y-m-d',$dateD);
        $dateF=strtotime($annee.'/12/31');
        $dateFin=date('Y-m-d',$dateF);
        $etat='Refusé';

        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name,user.firstName,user.id as idUser')
        ->leftJoin('c.user','user')
        ->where('c.dateDebut between :dateD and :dateF')
        ->andWhere('c.etat != :etat')
        ->setParameter('etat',$etat)
        ->setParameter('dateD',$dateDebut)
        ->setParameter('dateF',$dateFin)
        ->orderBy('user.name,user.firstName,c.dateDebut')
        ->getQuery()->getArrayResult();
    }

    public function getCongeParAnneeEtMois(int $annee, int $mois)
    {
        $dateD=strtotime($annee.'/'.$mois.'/01');
        $dateDebut=date('Y-m-d',$dateD);
        $dateF=strtotime($annee.'/'.$mois.'/31');
        $dateFin=date('Y-m-d',$dateF);
        $etat='Refusé';
        
        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name,user.firstName,user.id as idUser')
        ->leftJoin('c.user','user')
        ->where('c.dateDebut between :dateD and :dateF')
        ->andWhere('c.etat != :etat')
        ->setParameter('etat',$etat)
        ->setParameter('dateD',$dateDebut)
        ->setParameter('dateF',$dateFin)
        ->orderBy('user.name,c.dateCreation')
        ->getQuery()->getArrayResult();
    }

    public function getCongesParUser(User $user)
    {
        $id=$user->getId();
        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name, c.reason as reason')
        ->leftJoin('c.user','user')
        ->where('user.id = :id')
        ->setParameter('id',$id)
        ->orderBy('c.dateCreation', 'DESC')
        ->getQuery()->getArrayResult();
    }

    public function getCongeEnAttente()
    {
        $etat="en attente";
        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name,user.firstName,user.id as idUser, c.reason as reason')
        ->where('c.etat = :etat ')
        ->setParameter('etat',$etat)
        ->leftJoin('c.user','user')
        ->orderBy('c.dateCreation')
        ->getQuery()->getArrayResult();
    }

    public function findCongeDuJour($date)
    {
        $etat="valide";
        return $this->createQueryBuilder('c')
        ->select('c.dateFin,c.dureeDebut,c.dureeFin,user.name,user.firstName')
        ->where('c.dateDebut = :date or c.dateFin = :date or :date between c.dateDebut and c.dateFin ')
        ->andWhere("c.etat = :valide")
        ->setParameter('date',$date)
        ->setParameter('valide',$etat)
        ->leftJoin('c.user','user')
        ->orderBy('user.firstName')
        ->getQuery()->getArrayResult();
    }

    public function getCongesAVenir()
    {
        $etat = "Validé";
        $dateDuJour = date('Y-m-d H:i:s');

        return $this->createQueryBuilder('c')
        ->select('c.id,c.dateDebut,c.dateFin,c.dateCreation,c.etat,c.dureeDebut,c.dureeFin,user.name,user.firstName')
        ->leftJoin('c.user','user')
        ->Where("c.etat = :valide")
        ->andWhere('c.dateFin > :date')
        ->setParameter('date',$dateDuJour)
        ->setParameter('valide',$etat)
        ->orderBy('c.dateDebut')
        ->getQuery()->getArrayResult();
    }

    public function getCongesInDateRange(\DateTime $startDate, \DateTime $endDate)
    {
        $etat = 'Refusé';
        
        return $this->createQueryBuilder('c')
            ->select('c.dateDebut, c.dateFin, c.dureeDebut, c.dureeFin, c.etat, c.reason, user.name, user.firstName, user.weekly_work_time')
            ->leftJoin('c.user', 'user')
            ->where('c.dateDebut <= :endDate AND c.dateFin >= :startDate')
            ->andWhere('c.etat != :etat')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('etat', $etat)
            ->getQuery()
            ->getArrayResult();
    }
}
