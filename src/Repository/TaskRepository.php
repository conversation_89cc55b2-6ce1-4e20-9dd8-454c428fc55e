<?php

namespace App\Repository;

use App\Entity\Conge;
use App\Entity\Task;
use App\Form\RechercheType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\Query;
use Symfony\Bridge\Doctrine\RegistryInterface;
use App\Service\WeeklyReportService;

/**
 * @method Task|null find($id, $lockMode = null, $lockVersion = null)
 * @method Task|null findOneBy(array $criteria, array $orderBy = null)
 * @method Task[]    findAll()
 * @method Task[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TaskRepository extends ServiceEntityRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, Task::class);
    }

    /**
     * @return Task[] Returns an array of Task objects
     */

    //, $client, $typeInter, $ptojet
    public function findByMultiplFields($filters, ?string $orderBy = null)
    {
        $qb = $this->createQueryBuilder('t');
        $qb->select('t.id as id, t.subject as subject, t.date as date, 
                            t.duration as duration, t.refMantis as refMantis, user.id as user_id, user.firstName as user_firstname, user.name as user_name,
                            CONCAT(user.firstName, \' \' ,user.name) as user_fullname, 
                            typeInter.name as typeinter_name, 
                            client.id as client_id, client.name as client_name, 
                            project.id as project_id, project.name as project_name')
            ->innerJoin('t.user', 'user')
            ->innerJoin('t.typeInter', 'typeInter')
            ->innerJoin('t.client', 'client')
            ->innerJoin('t.project', 'project')
            ->innerJoin('project.company', 'company');
        foreach ($filters as $field => $value) {
            switch ($field) {
                case 'dateD':
                    $qb->andWhere('t.date >= :' . $field)
                        ->setParameter($field, $value);
                    break;
                case 'dateF':
                    $qb->andWhere('t.date <= :' . $field)
                        ->setParameter($field, $value);
                    break;
                case 'user':
                    if (count($value) > 0) {
                        $qb->andWhere('t.user in(:' . $field . ')')
                            ->setParameter($field, $value);
                    }
                    break;
                case 'client':
                    if (count($value) > 0) {
                        $qb->andWhere('t.client in(:' . $field . ')')
                            ->setParameter($field, $value);
                    }
                    break;
                case 'project':
                    if (count($value) > 0) {
                        $qb->andWhere('t.project in(:' . $field . ')')
                            ->setParameter($field, $value);
                    }
                    break;
                case 'refMantis':

                    $refMantis = explode(',', $value);

                    if (count($refMantis) > 1) {
                        $qb->andWhere('t.refMantis IN (:' . $field . ')')
                            ->setParameter($field, $refMantis);
                    } else {
                        $qb->andWhere('t.refMantis = :' . $field)
                            ->setParameter($field, $value);
                    }
                    break;
                case 'company':
                    if (count($value) > 0) {
                        $qb->andWhere('project.company in(:' . $field . ')')
                            ->setParameter($field, $value);
                    }
                    break;
                default:
                    $qb->andWhere('t.' . $field . ' = :' . $field)
                        ->setParameter($field, $value);
            }
        }


        switch ($orderBy) {
            case RechercheType::GROUPED_BY_TICKET:
                $qb->orderBy('t.refMantis', 'ASC');
                break;
            case RechercheType::GROUPED_BY_USER:
                $qb->orderBy('user.firstName', 'ASC')
                    ->addOrderBy('user.name', 'ASC')
                ;
                break;
        }
        $qb->addOrderBy('t.date', 'ASC');

        return $qb->getQuery()
            ->getArrayResult();
    }

    public function getLastTasks(?int $limit = 100)
    {
        return $this->createQueryBuilder('p')
            ->setMaxResults($limit)
            ->where('p.date <= CURRENT_DATE()')
            ->orderBy('p.date', 'DESC')
            ->getQuery()->getResult();
    }

    public function getEventsCalendar($userId, $month, $year)
    {
        return $this->createQueryBuilder('t')
            ->select('t.id, t.subject,t.date, t.duration, t.refMantis')
            ->addSelect('client.name as client_name')
            ->addSelect('typeInter.name as type_inter')
            ->innerJoin('t.client', 'client')
            ->innerJoin('t.typeInter', 'typeInter')
            ->where('t.user = :userId')
            ->andWhere('MONTH(t.date) = :month AND YEAR(t.date) = :year')
            ->setParameter('userId', $userId)
            ->setParameter('month', $month)
            ->setParameter('year', $year)
            ->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    /**
     * Retrieve the tasks for which the "refMantis" is included in the $tickets parameter
     * and for which the "date" is betweend the $startDate and $endDate parameters.
     *
     * @param array $tickets List of tickets that we want to retrieve
     * @param string|null $startDate The date after which the tasks must have been entered
     * @param string|null $endDate The date before which the tasks must have been entered
     * @return mixed
     */
    public function getTicketsByRefMantisAndDate(array $tickets, string $startDate = null, string $endDate = null)
    {
        $qb = $this->createQueryBuilder('t');
        $qb->select('t.refMantis, t.duration')
            ->where('t.refMantis IN (:tickets)')
            ->setParameter('tickets', $tickets);

        if (null !== $startDate && null !== $endDate) {
            $qb->andWhere('t.date BETWEEN :start AND :end')
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate);
        } elseif (null !== $startDate) {
            $qb->andWhere('t.date BETWEEN :start AND NOW()')
                ->setParameter('start', $startDate);
        }

        return $qb->getQuery()->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    public function getTasksByConge(Conge $conge)
    {
        return $this->createQueryBuilder('t')
            ->innerJoin('t.user', 'user')
            ->where("t.date between :dateD and :dateF or t.date = :dateD")
            ->andWhere("user.id = :id")
            ->andWhere("t.subject = ' CONGE '")
            ->setParameter('dateD', $conge->getDateDebut())
            ->setParameter('dateF', $conge->getDateFin())
            ->setParameter('id', $conge->getUser()->getId())
            ->getQuery()->getResult();
    }

    public function getTaskByProject($project, $customer, $idUser)
    {
        return $this->createQueryBuilder('t')
            ->select('t.id as id, t.subject as subject, t.date as date, 
                            t.duration as duration, t.refMantis as refMantis, 
                            user.firstName as user_firstname, user.name as user_name,
                            typeInter.name as typeinter_name, 
                            client.id as client_id, client.name as client_name, 
                            project.id as project_id, project.name as project_name')
            ->innerJoin('t.user', 'user')
            ->innerJoin('t.typeInter', 'typeInter')
            ->innerJoin('t.client', 'client')
            ->innerJoin('t.project', 'project')
            ->where('t.project = :idProject')
            ->andWhere('t.client = :idClient')
            ->andWhere('t.user = :idUser')
            ->setParameter('idProject', $project->getId())
            ->setParameter('idClient', $customer->getId())
            ->setParameter('idUser', $idUser)
            ->getQuery()->getResult();
    }

    public function getDailyTaskReporting(\DateTime $date)
    {
        $queryBuilder = $this->createQueryBuilder('t');
        $queryBuilder->select(' user.firstName as user_firstname, user.name as user_name,
                                      client.name as client_name,project.name as project_name,
                                      t.subject as subject, t.date as date,
                                      t.refMantis as refMantis,typeInter.name as typeinter_name,
                                      t.duration as duration')
            ->innerJoin('t.user', 'user')
            ->innerJoin('t.typeInter', 'typeInter')
            ->innerJoin('t.client', 'client')
            ->innerJoin('t.project', 'project')
            ->where('user.onSite = true')
            ->andwhere('t.date BETWEEN :dateBegin AND :dateEnd')
            ->setParameter('dateBegin', $date->format('Y-m-d 00:00:00'))
            ->setParameter('dateEnd', $date->format('Y-m-d 23:59:59'))
            ->orderBy('user.name', 'ASC');
        $query = $queryBuilder->getQuery();
        return $query->getResult();
    }

    public function getTasksByMonth(int $month, int $year)
    {
        return $this->createQueryBuilder('t')
            ->select('u.username', 'u.registrationNumber as matricule', 
                            'c.name as client', 'p.name as project', 
                            'p.reference as reference', 'ti.name as type', 
                            'SUM(t.duration) as duration')
            ->join('t.project', 'p')
            ->join('t.typeInter', 'ti')
            ->join('t.client', 'c')
            ->join('t.user', 'u')
            ->where('MONTH(t.date) = ' . $month)
            ->groupBy('u.username', 'u.registrationNumber', 'c.name', 'p.name', 'p.reference', 'ti.name')
            ->andWhere('YEAR(t.date) = ' . $year)
            ->andWhere('u.actif = :userActif')
            ->setParameter('userActif', 'oui')
            ->getQuery()->getArrayResult();
    }

    public function getTaskDurationSumByTag(int $projectId): array
    {
        $query = $this->createQueryBuilder('task')
            ->andWhere('task.project = :idProject')
            ->leftJoin('task.user', 'user')
            ->select('user.tag AS tag, SUM(task.duration)/7.5 as total')
            ->setParameter('idProject', $projectId)
            ->groupBy('tag')
            ->orderBy('total', 'DESC')
            ->getQuery()->getResult();

        return array_column($query, 'total', 'tag');
    }

    public function getTMAProjectsTasks(array $projectsID, string $format)
    {
        return $this->createQueryBuilder('t')
            ->select("p.name, DATE_FORMAT(t.date, '%Y-%m') AS month, SUM(t.duration)/7.5 AS duration")
            ->join('t.project', 'p')
            ->where('p.id IN (:projects)')
            ->andWhere('t.date >= :date')
            ->groupBy('p.name, month')
            ->orderBy('month', 'ASC')
            ->setParameter('projects', $projectsID)
            ->setParameter('date', $format)
            ->getQuery()->getArrayResult();
    }

    public function getInterventionsByType(int $projectId): array
    {
        return $this->createQueryBuilder('t')
            ->select('ti.name as type, SUM(t.duration)/7.5 as duration, 
                      CASE 
                          WHEN ti.name = \'Projet - Développement (front et back)\' THEN p.chargeTech
                          WHEN ti.name = \'Projet - Pilotage & coordination\' THEN p.pilotage
                          WHEN ti.name = \'Projet - Recette & Garantie\' THEN p.recette
                          WHEN ti.name = \'Projet - Conception technique et fonctionnelle détaillée\' THEN p.conception
                          WHEN ti.name = \'Projet - Migration & Mise en production\' THEN p.mep
                          ELSE 0
                      END as chargeVendue')
            ->innerJoin('t.typeInter', 'ti')
            ->innerJoin('t.project', 'p')
            ->where('t.project = :projectId')
            ->setParameter('projectId', $projectId)
            ->groupBy('ti.id')
            ->getQuery()
            ->getResult();
    }

    public function getTimeEntriesPerUser(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('t')
            ->select('user.firstName AS firstName, user.name AS lastName, user.weekly_work_time, company.id as company_id, c.name AS client_name, p.name AS project_name, SUM(t.duration) AS total')
            ->innerJoin('t.user', 'user')
            ->innerJoin('t.project', 'p')
            ->innerJoin('t.client', 'c')
            ->innerJoin('user.company', 'company')
            ->where('company.id = 1')
            ->andWhere('t.date BETWEEN :startDate AND :endDate AND user.onSite = 1')
            ->groupBy('user.firstName, user.name, company.id, client_name, project_name, user.weekly_work_time')
            ->orderBy('user.name', 'ASC')
            ->addOrderBy('project_name', 'ASC')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getResult();
    }

    public function findTasksBetweenDates(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        $qb = $this->createQueryBuilder('t')
            ->select('t', 'user', 'client', 'project', 'typeInter')
            ->innerJoin('t.user', 'user')
            ->innerJoin('t.client', 'client')
            ->innerJoin('t.project', 'project')
            ->innerJoin('t.typeInter', 'typeInter')
            ->where('t.date >= :startDate')
            ->andWhere('t.date <= :endDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'));
    
        return $qb->getQuery()->getArrayResult();
    }

    public function getAllActiveUsers(array $excludeEmails = [], bool $onSiteOnly = false): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('u.id, u.name, u.firstName, u.weekly_work_time')
           ->from('App\Entity\User', 'u')
           ->where('u.actif = :actif')
           ->andWhere('u.company = :companyId')
           ->setParameter('companyId', 1)
           ->setParameter('actif', 'oui');
        
        if ($onSiteOnly) {
            $qb->andWhere('u.onSite = :onSite')
               ->setParameter('onSite', true);
        }
        
        if (!empty($excludeEmails)) {
            $qb->andWhere('u.mail NOT IN (:excludeEmails)')
               ->setParameter('excludeEmails', $excludeEmails);
        }
        
        return $qb->getQuery()->getResult();
    }

    public function getFormationTasks(\DateTime $startDate, \DateTime $endDate): array
    {
        $qb = $this->createQueryBuilder('t');
        $qb->select('t.duration, IDENTITY(t.user) as userId')
           ->innerJoin('t.client', 'c')
           ->innerJoin('t.project', 'p')
           ->where('t.date BETWEEN :startDate AND :endDate')
           ->andWhere('c.name LIKE :formation OR p.name LIKE :formation')
           ->setParameter('startDate', $startDate->format('Y-m-d 00:00:00'))
           ->setParameter('endDate', $endDate->format('Y-m-d 23:59:59'))
           ->setParameter('formation', '%Formation%');
        
        return $qb->getQuery()->getResult();
    }

    public function getUsersWithoutTasksOrConges(array $nonRegieUsers, \DateTime $startDate, \DateTime $endDate, array $excludeEmails = []): array
    {
        $qb = $this->createQueryBuilder('t')
            ->select('IDENTITY(t.user) as userId')
            ->where('t.date BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->groupBy('t.user');

        $taskUserIds = array_column($qb->getQuery()->getArrayResult(), 'userId');

        $congeUserIds = $this->getEntityManager()->getRepository(Conge::class)
            ->createQueryBuilder('c')
            ->select('IDENTITY(c.user) as userId')
            ->where('c.dateDebut <= :endDate AND c.dateFin >= :startDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->groupBy('c.user')
            ->getQuery()
            ->getArrayResult();

        $congeUserIds = array_column($congeUserIds, 'userId');

        $declaredUserIds = array_unique(array_merge($taskUserIds, $congeUserIds));

        return array_filter($nonRegieUsers, function ($user) use ($declaredUserIds, $excludeEmails) {
            return !in_array($user['id'], $declaredUserIds) &&
                   (!isset($user['mail']) || !in_array($user['mail'], $excludeEmails));
        });
    }

    public function getUsersWithoutTasks(array $nonRegieUsers, \DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        $qb = $this->createQueryBuilder('t')
            ->select('IDENTITY(t.user) as userId')
            ->where('t.date BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->groupBy('t.user');

        $taskUserIds = array_column($qb->getQuery()->getArrayResult(), 'userId');

        $congeUserIds = $this->getEntityManager()->getRepository(Conge::class)
            ->createQueryBuilder('c')
            ->select('IDENTITY(c.user) as userId')
            ->where('c.dateDebut <= :endDate AND c.dateFin >= :startDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->groupBy('c.user')
            ->getQuery()
            ->getArrayResult();

        $congeUserIds = array_column($congeUserIds, 'userId');

        $declaredUserIds = array_unique(array_merge($taskUserIds, $congeUserIds));

        // Vérifier si nous avons des objets User ou des tableaux associatifs
        if (!empty($nonRegieUsers) && is_object($nonRegieUsers[0])) {
            // Si ce sont des objets User
            return array_filter($nonRegieUsers, function ($user) use ($declaredUserIds) {
                return !in_array($user->getId(), $declaredUserIds);
            });
        } else {
            // Si ce sont des tableaux associatifs
            return array_filter($nonRegieUsers, function ($user) use ($declaredUserIds) {
                return !in_array($user['id'], $declaredUserIds);
            });
        }
    }
}
