<?php

namespace App\Repository;

use App\Entity\Client;
use App\Entity\Company;
use App\Entity\Project;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Symfony\Bridge\Doctrine\RegistryInterface;

/**
 * @method Project|null find($id, $lockMode = null, $lockVersion = null)
 * @method Project|null findOneBy(array $criteria, array $orderBy = null)
 * @method Project[]    findAll()
 * @method Project[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProjectRepository extends ServiceEntityRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, Project::class);
    }

    public function getAllActive()
    {
        return $this->createQueryBuilder('p')
            ->where('p.actif = :etat')
            ->setParameter('etat',Project::STATE_ACTIVE)
            ->orderBy('p.name','ASC')
            ->getQuery()->getResult();
    }
    public function getAdminList(?array $filters = [])
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.id, p.actif, p.name, p.reference, p.chargeVendue, p.avancement')
            ->addSelect('manager.name as manager_name')
            ->addSelect('manager.firstName as manager_first_name')
            ->addSelect('client.name as client_name')
            ->addSelect('SUM(tasks.duration)/7.5 as chargeConsommee')
            ->addSelect('(SUM(tasks.duration)/7.5) / p.chargeVendue  * 100 as pourcentageConsomme')
            ->leftJoin('p.client', 'client')
            ->leftJoin('p.tasks', 'tasks')
            ->leftJoin('p.manager', 'manager')
            ->groupBy('p.id');

        if (!empty($filters['actif'])) {
            $qb->andWhere('p.actif ' . ($filters['actif'] ? '!=' : '=') . ' :val5')
                ->setParameter('val5', 'non');
        }

        return $qb->getQuery()->getArrayResult();
    }

    public function getReport(?int $state = null)
    {
        $datePreviousWeek = (new \DateTimeImmutable())->sub(new \DateInterval('P1W'));
        $dateYesterday = (new \DateTimeImmutable())->sub(new \DateInterval('P1D'));
        $qb = $this->createQueryBuilder('p')
            ->select('p.id,client.name as client_name,  p.name, p.reference, p.type, CONCAT(manager.name, \' \', manager.firstName) as manager_name')
            ->addSelect(' p.chargeVendue')
            ->addSelect('p.dateRecette, p.dateProduction')
            ->addSelect('SUM(tasks.duration)/7.5 as chargeConsommee')
            ->addSelect('(SUM(tasks.duration)/7.5) - ( SUM(CASE WHEN (tasks.date < :datePreviousWeek) THEN tasks.duration ELSE 0 END)/7 ) as deltaChargeConsommee')
            ->addSelect('(SUM(tasks.duration)/7.5) / p.chargeVendue  * 100 as pourcentageConsomme')
            ->addSelect('((SUM(tasks.duration)/7.5) / p.chargeVendue  * 100)
                               - ((SUM(CASE WHEN (tasks.date < :datePreviousWeek) THEN tasks.duration ELSE 0 END)/7) / p.chargeVendue  * 100) as deltaPourcentageConsomme')
            ->addSelect('p.avancement')
            ->addSelect('MIN(tasks.date) as first_task_date, MAX(tasks.date) as last_task_date')
            ->leftJoin('p.client', 'client')
            ->leftJoin('p.tasks', 'tasks')
            ->leftJoin('p.manager', 'manager')
            ->where('tasks.date <= :dateYesterday')
            ->setParameter('dateYesterday', $dateYesterday->format('Y-m-d 23:59:59'))
            ->setParameter('datePreviousWeek', $datePreviousWeek->format('Y-m-d 00:00:00'))
            ->orderBy('deltaChargeConsommee', 'DESC')
            ->groupBy('p.id');

        if (!empty($state)){
            $qb->andWhere('p.actif = :state')
                ->setParameter('state',$state);
        }

        return $qb->getQuery()->getArrayResult();
    }

    public function getPourcentageAvancement(Project $project)
    {

        $pourcentage = $this->createQueryBuilder('p')
            ->select('(SUM(tasks.duration)/7.5) / p.chargeVendue  * 100')
            ->leftJoin('p.tasks', 'tasks')
            ->where('p.id = :project')
            ->setParameter('project', $project)
            ->getQuery()->getSingleScalarResult();

        return !empty($pourcentage) ? number_format($pourcentage, 2, '.','') : $pourcentage;
    }

    public function getChargeConsommee(Project $project)
    {

        $charge = $this->createQueryBuilder('p')
            ->select('SUM(tasks.duration)/7.5')
            ->leftJoin('p.tasks', 'tasks')
            ->where('p.id = :project')
            ->setParameter('project', $project)
            ->getQuery()->getSingleScalarResult();

        return !empty($charge) ? number_format($charge, 2) : $charge;
    }

    public function getTaskSumByCustomer(?int $projectId)
    {
        $datePreviousWeek = (new \DateTimeImmutable())->sub(new \DateInterval('P1W'));
        $dateYesterday = (new \DateTimeImmutable())->sub(new \DateInterval('P1D'));
        return $this->createQueryBuilder('p')
            ->select('client.id, client.name, SUM(tasks.duration)/7.5 as total')
            ->addSelect('(SUM(tasks.duration)/7.5) - (SUM(CASE WHEN tasks.date < :datePreviousWeek THEN tasks.duration ELSE 0 END )/7) as deltaTotal')
            ->addSelect('MIN(tasks.date) as first_task_date, MAX(tasks.date) as last_task_date')
            ->join('p.tasks', 'tasks')
            ->leftJoin('tasks.client', 'client')
            ->where('p.id = :idProject')
            ->setParameter('idProject', $projectId)
            ->groupBy('client.id, client.name')
            ->orderBy('deltaTotal', 'DESC')
            ->andWhere('tasks.date <= :dateYesterday')
            ->setParameter('dateYesterday', $dateYesterday->format('Y-m-d 23:59:59'))
            ->setParameter('datePreviousWeek', $datePreviousWeek->format('Y-m-d 00:00:00'))
            ->getQuery()->getArrayResult();
    }

    public function getTaskSumByUser(?int $projectId)
    {
        return $this->createQueryBuilder('p')
            ->select('user.id, CONCAT(user.name, \' \', user.firstName) as name, user.tag as tag, SUM(tasks.duration)/7.5 as total')
            ->join('p.tasks', 'tasks')
            ->leftJoin('tasks.user', 'user')
            ->where('p.id = :idProject')
            ->setParameter('idProject', $projectId)
            ->groupBy('user.id, user.name')
            ->orderBy('total', 'DESC')
            ->getQuery()->getArrayResult();
    }

    public function getTaskBounds(?int $projectId)
    {
        return $this->createQueryBuilder('p')
            ->select('MIN(tasks.date) as first_task_date, MAX(tasks.date) as last_task_date')
            ->join('p.tasks', 'tasks')
            ->leftJoin('tasks.user', 'user')
            ->where('p.id = :idProject')
            ->setParameter('idProject', $projectId)
            ->getQuery()->getOneOrNullResult(Query::HYDRATE_SCALAR);
    }

    public function getProjectsByClient(?Client $client)
    {
        $projects =  $this->createQueryBuilder('p','p.name')
            ->leftJoin('p.client', 'client')
            ->join('p.userProject','userProject')
            ->andWhere('p.actif != :val5')
            ->andWhere('p.client IS NULL OR p.client = :client')
            ->setParameter('client', $client)
            ->setParameter('val5', 'non')
            ->orderBy('p.name', 'ASC')->getQuery()->getResult();

        if (count($projects) > 1 && isset($projects[Project::NONE])){
            unset($projects[Project::NONE]);
        }
        return $projects;
    }
    public function findUserProjectsByClient(User $user, ?Client $client)
    {
        $projects =  $this->createQueryBuilder('p','p.name')
            ->leftJoin('p.client', 'c')
            ->join('p.userProject','up')
            ->andWhere('p.actif != :actif')
            ->andWhere('c IS NULL OR c = :client')
            ->andWhere('up = :user')
            ->setParameter('client', $client)
            ->setParameter('actif', 'non')
            ->setParameter('user', $user)
            ->orderBy('p.name', 'ASC')->getQuery()->getResult();

        if (count($projects) > 1){
            unset($projects[Project::NONE]);
        }
        return $projects;
    }

    public function getProjectsByStateAndType(int $state , $type)
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.id, p.actif, p.name, p.reference, p.chargeVendue, p.avancement')
            ->addSelect('manager.name as manager_name')
            ->addSelect('manager.firstName as manager_first_name')
            ->addSelect('client.name as client_name')
            ->addSelect('SUM(tasks.duration)/7.5 as chargeConsommee')
            ->addSelect('(SUM(tasks.duration)/7.5) / p.chargeVendue  * 100 as pourcentageConsomme')
            ->addSelect('company.name as company_name')
            ->leftJoin('p.client', 'client')
            ->leftJoin('p.tasks', 'tasks')
            ->leftJoin('p.manager', 'manager')
            ->leftJoin('p.company', 'company')
            ->groupBy('p.id');

        if ($state !== null && $state !== 3) {
            $qb->andWhere('p.actif = :state' )
                ->setParameter(':state',$state);
        }

        if ($type !== null) {
            if ($type == Company::IT_ROOM) {
                $qb->andWhere("company.name = 'IT-Room'");
            } else if ($type == Company::IT_REF) {
                $qb->andWhere("company.name = 'IT-Ref'");
            }
        }

        return $qb->getQuery()->getArrayResult();
    }

    public function getAuthorizedUser($id)
    {
        return $this->createQueryBuilder('p')
            ->where('p.id = :id')
            ->setParameter('id', $id)
            ->getQuery()->getOneOrNullResult();
    }

    public function getProjectsAllowUsers(): array
    {
        return $this->createQueryBuilder('p')
            ->select("p.name as projet, GROUP_CONCAT(CONCAT(up.firstName, ' ', up.name) SEPARATOR '; ') as utilisateurs")
            ->leftJoin('p.userProject', 'up')
            ->where('p.actif = :actif')
            ->setParameter('actif', Project::STATE_ACTIVE)
            ->groupBy('p.id')
            ->getQuery()->getArrayResult();
    }

    public function getTMAProjects(): array
    {
        return $this->createQueryBuilder('p')
            ->select('p.id, p.name, p.chargeVendue')
            ->where('p.name LIKE :name')
            ->setParameter('name', '% - TMA')
            ->getQuery()->getArrayResult();
    }
}
