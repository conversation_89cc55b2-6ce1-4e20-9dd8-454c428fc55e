<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Company;
use App\Entity\Project;
use App\Entity\TypeInter;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\CompanyRepository;
use App\Repository\ProjectRepository;
use App\Repository\TypeInterRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Security;

class UserType extends AbstractType
{
    /**
     * @var Security
     */
    private $security;

    /**
     * @var ProjectRepository
     */
    private $projectRepository;

    public function __construct(Security $security, ProjectRepository $projectRepository)
    {
        $this->security = $security;
        $this->projectRepository = $projectRepository;
    }


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('mail')
            ->add('name')
            ->add('firstName')
            ->add('username')
            ->add('duration', TextType::class, [
                'label' => 'Durée par défaut',
                'required' => false,
                'empty_data' => 0,
            ])
            ->add('weeklyWorkTime', TextType::class, [
                'label' => 'Temps de travail hebdomadaire',
                'required' => false,
                'empty_data' => 0,
            ])
            ->add('client', EntityType::class, [
                'query_builder' => function (ClientRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.name', 'ASC');
                },
                'class' => Client::class,
                'choice_label' => 'name',
                'label' => 'Client par défaut',
                'required' => false,
            ])
            ->add('typeInter', EntityType::class, [
                'query_builder' => function (TypeInterRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.name', 'ASC');
                },
                'class' => TypeInter::class,
                'choice_label' => 'name',
                'label' => 'Type d\'intervention par défaut',
                'required' => false,
            ])
            ->add('project', EntityType::class, [
                'query_builder' => function (ProjectRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.name', 'ASC');
                },
                'class' => Project::class,
                'choice_label' => 'name',
                'label' => 'Projet par défaut',
                'required' => false,

            ])
            ->add('registrationNumber', TextType::class, [
                'label' => 'Matricule',
                'required' => false
            ])
            ->add('onSite', ChoiceType::class, [
                'label' => 'En régie ?',
                'multiple' => false,
                'expanded' => false,
                'choices' => [
                    'Oui' => false,
                    'Non' => true,
                ],
                'placeholder'=>'-- Sélectionner --'
            ])
            ->add('mantisApiKey', TextType::class, [
                'label' => 'Jeton d\'API Mantis',
                'required' => false
            ])
            ->add('company', EntityType::class, [
                'label' => 'Entité',
                'placeholder' => '',
                'required' => true,
                'class' => Company::class,
                'choice_label' => 'name',
                'query_builder' => function (CompanyRepository $companyRepository) {
                    return $companyRepository->createQueryBuilder('c')
                        ->where('c.userEnabled = :true')
                        ->setParameter('true', true);
                }
            ])
            ->add('tag', ChoiceType::class, [
                'label' => 'Tag',
                'placeholder' => '',
                'required' => false,
                'choices' => User::IT_ROOM_TAGS
            ])
        ;

        if ($this->security->isGranted(['ROLE_ADMIN', 'ROLE_PROJECT_MANAGER'], $this->security->getUser())){
            $defaultProject = $this->projectRepository->findBy(['defaultProject' => true, 'actif' => [Project::STATE_ACTIVE, Project::STATE_STANDBY]]);
            $userProject = $options['data']->getUserProjects();
            $builder->add('userProjects', EntityType::class, [
                'label' => 'Projet(s) autorisé(s)',
                'class' => Project::class,
                'attr' => ['class' => 'select2-selection__rendered'],
                'multiple' => true,
                'by_reference' => false,
                'choice_label' => function (Project $project) {
                    $label = '';
                    $label .= (!empty($project->getClient()) ? sprintf('%s - ', $project->getClient()) : '');
                    $label .= $project->getName();
                    $label .= (!empty($project->getReference()) ? sprintf(' (%s)', $project->getReference()) : '');

                    return $label;
                },
                'query_builder' => function (EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('u')
                        ->where('u.actif in (:actif)')
                        ->setParameter('actif', [Project::STATE_ACTIVE, Project::STATE_STANDBY]);
                },
                'preferred_choices' => $defaultProject,
                'data' => $userProject->isEmpty() ? $defaultProject : $userProject,
                'placeholder' => '-- Sélectionnez un projet --',
            ]);
        }

        if ($options['is_admin']) {
            $roles = User::ROLES;
            if (!$this->security->isGranted(User::ROLE_SUPER_ADMIN)) {
                unset($roles[User::ROLE_SUPER_ADMIN]);
            }
            $builder->add('actif', ChoiceType::class, [
                'choices' => [
                    'Oui' => 'oui',
                    'Non' => 'non',
                ]
            ])
                ->add('roles', ChoiceType::class, [
                    'choices' => $roles,
                    'required' => true,
                    'multiple' => true,
                ]);
        }
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => User::class,
            'is_admin' => false,
        ]);
    }
}
