<?php

namespace App\Form;

use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use App\Repository\UserRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use App\Entity\User;

class CalendarCongeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('users', EntityType::class,[
            'class' => User::class,
            'multiple' => true,
            'query_builder' => function(UserRepository $userRepository){
                return $userRepository->createQueryBuilder('u')
                ->andWhere('u.actif != :val5')
                ->orWhere('u.actif != 0')
                ->setParameter('val5', 'non')
                ->orderBy('u.name');
            }
        ])
            ->add('submit', SubmitType::class, ['label' => 'Soumission']);
    }
}