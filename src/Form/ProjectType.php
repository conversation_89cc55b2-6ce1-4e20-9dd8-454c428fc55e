<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Company;
use App\Entity\Project;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\CompanyRepository;
use App\Repository\ProjectRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Security;

class ProjectType extends AbstractType
{
    private $projectRepository;

    private $security;

    public function __construct(ProjectRepository $projectRepository, Security $security)
    {
        $this->projectRepository = $projectRepository;
        $this->security = $security;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom'
            ])
            ->add('description')
            ->add('reference')
            ->add('client', EntityType::class, [
                'query_builder' => function (ClientRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.name', 'ASC');
                },
                'class' => Client::class,
                'choice_label' => 'name',
            ])
        ;
        if (Project::STATE_INACTIVE !== $options['data']->getActif() || $this->security->isGranted(['ROLE_SUPER_ADMIN'])) {
            $builder->add('actif', ChoiceType::class, [
                'label' => 'Statut',
                'choices' => [
                    'Actif' => Project::STATE_ACTIVE,
                    'Inactif' => Project::STATE_INACTIVE,
                    'Standby' => Project::STATE_STANDBY,
                ],
            ]);
        }
        $builder
            ->add('avancement')
            ->add('manager', EntityType::class, [
                'label' => 'Chef de projet',
                'required' => TRUE,
                'class' => User::class,
                'query_builder' => function (EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('u')
                        ->where('u.roles LIKE :roles')
                        ->setParameter('roles', '%' . User::ROLE_PROJECT_MANAGER . '%');
                }
            ]);
        if ($this->security->isGranted(['ROLE_ADMIN', 'ROLE_PROJECT_MANAGER'])) {
            $builder->add('userProject', EntityType::class, [
                'label' => 'Utilisateur autorisé',
                'class' => User::class,
                'attr' => ['class' => 'select2-selection__rendered'],
                'required' => false,
                'multiple' => true,
                'query_builder' => function (EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('u')
                        ->where('u.actif = :actif')
                        ->setParameter('actif', 'oui');
                }
            ]);
        }
        $builder
            ->add('chargeVendue', NumberType::class, ['label' => 'Charge vendue (en jours)', 'required' => FALSE])
            ->add('type', ChoiceType::class, [
                'choices' => [
                    'Forfait' => Project::TYPE_FORFAIT,
                    'Moyen' => Project::TYPE_ENGAGEMENT,
                    'Autre' => Project::TYPE_OTHER,
                ],
            ])
            ->add('chargeTech', NumberType::class, ['label' => 'Projet - Développement (front et back) (en jours)', 'required' => FALSE])
            ->add('observers', EntityType::class, [
                'class' => User::class,
                'label' => 'Observateurs',
                'attr' => ['class' => 'select2-selection__rendered'],
                'disabled' => !$this->security->isGranted(['ROLE_SUPER_ADMIN']),
                'required' => false,
                'multiple' => true,
                'query_builder' => function (EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('u')
                        ->where('u.actif = :actif')
                        ->setParameter('actif', 'oui');
                }
            ])
            ->add('pilotage', NumberType::class, ['label' => 'Pilotage & coordination (en jours)', 'required' => FALSE])
            ->add('dateRecette', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'required' => false,
            ])
            ->add('recette', NumberType::class, ['label' => 'Recette & Garantie (en jours)', 'required' => FALSE])
            ->add('dateProduction', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'required' => false,
            ])
            ->add('conception', NumberType::class, ['label' => 'Conception technique et fonctionnelle détaillée (en jours)', 'required' => FALSE])
            ->add('defaultProject', ChoiceType::class, [
                'label' => 'Projet par défaut',
                'choices' => ['non' => false, 'oui' => true],
                'empty_data' => false
            ])
            ->add('mep', NumberType::class, ['label' => 'Migration & Mise en production (en jours)', 'required' => FALSE])
            ->add('company', EntityType::class, [
                'class' => Company::class,
                'label' => 'Entité',
                'required' => true,
                'choice_label' => 'name',
                'query_builder' => function (CompanyRepository $companyRepository) {
                    return $companyRepository->createQueryBuilder('c')
                        ->where('c.projectEnabled = :true')
                        ->setParameter('true', true);
                },
            ]);



        $builder->addEventListener(FormEvents::PRE_SET_DATA, [$this, 'onPreSetData']);
    }

    public function onPreSetData(FormEvent $event)
    {

        $form = $event->getForm();
        $project = $event->getData();

        if (!empty($project) && !empty($project->getId())) {
            $pourcentageAvancement = $this->projectRepository->getPourcentageAvancement($project);

            $form->add('ChargeConsommee', TextType::class, [
                'label' => 'Charge consommée',
                'mapped' => FALSE,
                'data' => $this->projectRepository->getChargeConsommee($project),
                'disabled' => TRUE,
            ])->add('PourcentageConsomme', TextType::class, [
                'label' => 'Pourcentage consommé',
                'mapped' => FALSE,
                'data' => $pourcentageAvancement,
                'disabled' => TRUE,
                'attr' => ['class' => $this->getClassPourcentage($pourcentageAvancement)],
            ]);
        }
    }


    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Project::class,
        ]);
    }

    private function getClassPourcentage(?float $pourcentageAvancement)
    {
        if (empty($pourcentageAvancement)) {
            return '';
        }
        if ($pourcentageAvancement <= Project::SEUIL_POURCENTAGE_CONSOMME) {
            return 'text-white bg-success';
        }
        if ($pourcentageAvancement <= 100) {
            return 'text-white bg-warning';
        }
        return 'text-white bg-danger';
    }
}
