<?php

declare(strict_types=1);

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;

class ReportingType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('date', DateType::class, [
                'widget' => 'single_text',
            ])
            ->add('change_date', SubmitType::class, [
                'label' => 'Changer la date'
            ])
            ->add('reporting_text', TextareaType::class, [
                'label' => 'Précisez les objectifs de la prochaine journée, le reste à faire ou les points non soldés',
                'attr' => [
                    'cols' => '50',
                    'rows' => '10',
                    'class' => 'mb-low',
                ],
                'data' => '
Demain il me reste à :
 -
 -

Points non soldés :
                '
            ])
            ->add('reporting_validation', SubmitType::class, ['label' => 'Valider le reporting']);
    }
}
