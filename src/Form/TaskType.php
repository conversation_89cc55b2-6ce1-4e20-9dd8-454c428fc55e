<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TypeInter;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\ProjectRepository;
use App\Repository\TypeInterRepository;
use App\Repository\UserRepository;
use App\Validator\Constraints\WeekEnd;
use Doctrine\ORM\Query\Expr\Orx;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use App\Validator\Constraints\JourFerie;

class TaskType extends AbstractType
{

    public const ID_HOLIDAYS = 204;

    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;
    /**
     * @var AuthorizationCheckerInterface
     */
    private $authChecker;
    /**
     * @var ProjectRepository
     */
    private $projectRepository;
    /**
     * @var Security
     */
    private $security;

    public function __construct(TokenStorageInterface $tokenStorage, AuthorizationCheckerInterface $authChecker, ProjectRepository $projectRepository, Security $security)
    {
        $this->tokenStorage = $tokenStorage;
        $this->authChecker = $authChecker;
        $this->projectRepository = $projectRepository;
        $this->security = $security;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('date', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'empty_data' => (new \DateTime())->format('Y-m-d'),
                'constraints' => [new JourFerie(), new WeekEnd()]
            ])
            ->add('subject', TextareaType::class, [
                'label' => 'Résumé de l\'intervention',
                'attr' => ['maxlength' => 255],
                'constraints' => [
                    new Length(['max' => 255]),
                ],
                'empty_data'=>'',
            ])
            ->add('refMantis', TextType::class, [
                'label' => 'Référence ticket',
                'required'=> false,
                ])
            ->add('duration', TextType::class, [
                'label' => 'Durée en numérique 1.5 = 1h30 / Journée à 7',
                'empty_data' => 0,
            ])
            ->add('client', EntityType::class, [
                'query_builder' => function (ClientRepository $clientRepository) {
                    return $clientRepository->createQueryBuilder('c')
                        ->join('c.projects','p')
                        ->join('p.userProject', 'up')
                        ->andWhere('up = :user')
                        ->setParameter('user', $this->security->getUser())
                        ->andWhere('c.actif = :val5')
                        ->setParameter('val5', 'oui')
                        ->andWhere('c.id != :idHolidays')
                        ->setParameter('idHolidays', self::ID_HOLIDAYS)
                        ->orderBy('c.name', 'ASC');
                },
                'class' => Client::class,
                'choice_label' => 'name',
                'attr' => [
                    'data-route-project-description' => $options['project_description_url'],
                ],
            ])
        ;
        $user = $this->security->getUser();
        $builder
            ->add('typeInter', EntityType::class, [
                'query_builder' => function (TypeInterRepository $typeInterRepository) use ($user) {
                    return $typeInterRepository->createQueryBuilder('ti')
                        ->leftJoin('ti.allowCompany', 'ac')
                        ->where('ti.actif = :actif')
                        ->andWhere(new Orx([
                            'ac.id = :companyId',
                            'ac.id IS null',
                        ]))
                        ->setParameter('actif', 'oui')
                        ->setParameter('companyId', $user->getCompany()->getId())
                        ->orderBy('ti.name', 'ASC')
                        ;
                },
                'class' => TypeInter::class,
                'choice_label' => 'name',
            ]);

        if ($this->authChecker->isGranted('ROLE_ADMIN')) {
            $builder->add('user', EntityType::class, [
                'query_builder' => function (UserRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.mail', 'ASC');
                },
                'class' => User::class,
                'choice_label' => 'mail',
            ]);
        }

        $builder->addEventListener(FormEvents::PRE_SET_DATA, [$this, 'onPreSetData']);
        $builder->get('client')->addEventListener(FormEvents::POST_SUBMIT, [$this, 'onPostSubmit']);
    }

    public function onPreSetData(FormEvent $event)
    {
        $form = $event->getForm();
        /** @var Task $task */
        $task = $event->getData();
        /** @var User $currentUser */
        $currentUser = $this->tokenStorage->getToken()->getUser();

        // La répétition n'est accessible qu'en création
        if (!$task || $task->getId() === null) {
            $task->setDate(new \DateTime())
                 ->setDuration($currentUser->getDuration())
                 ->setClient($currentUser->getClient())
                 ->setTypeInter($currentUser->getTypeInter())
                 ->setProject($currentUser->getProject())
                 ->setUser($currentUser);
            $form
                ->add('repeat', NumberType::class, [
                    'label' => 'Répétition en jour',
                    'mapped' => false,
                    'data' => '0',
                    'required' => false,
                    'constraints' => [
                        new GreaterThanOrEqual(0),
                    ]
                ])
                ->add('durationJ', CheckboxType::class, [
                    'data' => false,
                    'mapped' => false,
                    'label' => 'Journée complète',
                    'required' => false,
                ]);
        }
        $this->filtrerProjets($form, $task->getClient());
    }

    public function onPostSubmit(FormEvent $event)
    {
        $client = $event->getForm()->getData();
        $this->filtrerProjets($event->getForm()->getParent(), $client);
    }

    public function filtrerProjets(FormInterface $form, ?Client $client = null)
    {
        $preferedProjects = [];
        if (!empty($client)) {
            $preferedProjects = $this->projectRepository->findBy(['client' => $client]);
        }

        $projects = $this->projectRepository->findUserProjectsByClient($this->security->getUser(), $client);
        $form->add('project', EntityType::class, [
            'choices' => $projects,
            'class' => Project::class,
            'choice_label' => function (Project $project) {
                $label = '';
                $label .= (!empty($project->getClient()) ? sprintf('%s - ', $project->getClient()) : '');
                $label .= $project->getName();
                $label .= (!empty($project->getReference()) ? sprintf(' (%s)', $project->getReference()) : '');

                return $label;
            },
            'placeholder' => '-- Sélectionnez un projet --',
            'preferred_choices' => $preferedProjects,
            'required'=> false,
        ]);
    }

    public function finishView(FormView $view, FormInterface $form, array $options)
    {
        $childrens = $view->children;
        $projectElement = ['project' => $childrens['project']];
        unset($childrens['project']);
        $index = array_search('typeInter', array_keys($childrens));
        $view->children = array_slice($childrens, 0, $index + 1, true) + $projectElement + array_slice($childrens, $index + 1, null, true);
        parent::finishView($view, $form, $options);
    }


    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Task::class,
            'project_description_url' => null,
        ]);
    }
}
