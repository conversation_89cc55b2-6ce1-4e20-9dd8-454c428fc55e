<?php
/**
 * Created by PhpStorm.
 * User: glefer
 * Date: 25/03/19
 * Time: 13:20
 */

namespace App\Form;


use App\Repository\UserRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class CalendarFilterType extends AbstractType
{
    /**
     * @var UserRepository
     */
    private $userRepository;
    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    public function __construct(UserRepository $userRepository, TokenStorageInterface $tokenStorage)
    {
        $this->userRepository = $userRepository;
        $this->tokenStorage = $tokenStorage;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('user_id', ChoiceType::class, [
            'choices' => $this->getUsersAsChoices(),
            'label'=> 'Utilisateur',
            'data'=> $options['id_user']?? $this->tokenStorage->getToken()->getUser()->getId(),
        ])
            ->add('submit', SubmitType::class, ['label' => 'Soumission']);
    }

    public function getBlockPrefix()
    {
        return parent::getBlockPrefix();
    }

    private function getUsersAsChoices()
    {
        $users = $this->userRepository->getUserForChoiceList();
        $usersChoices = array();
        foreach ($users as $user) {
            $userFullName = trim($user['firstName'] . ' ' . $user['name']);
            $usersChoices[$userFullName] = $user['id'];
        }

        return $usersChoices;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(['id_user' => null]);
    }

}