<?php

namespace App\Form;

use App\Entity\Conge;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
//use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use App\Validator\Constraints\WeekEnd;
use App\Validator\Constraints\JourFerie;
use App\Validator\Constraints\Date;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class CongeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('dateDebut', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'empty_data' => (new \DateTime())->format('Y-m-d'),
                'constraints' => [new JourFerie, new WeekEnd],
            ])
            ->add('dureeDebut', ChoiceType::class, [
                'choices' => [
                    'Journée complète' => 'journée complète',
                    'Matin' => 'matin',
                    'Apres-midi' => 'apres-midi',
                ]])
            ->add('dateFin', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'empty_data' => (new \DateTime())->format('Y-m-d'),
                'constraints' => [new JourFerie, new WeekEnd],
            ])
            ->add('dureeFin', ChoiceType::class, [
                'choices' => [
                    'Journée complète' => 'journée complète',
                    'Matin' => 'matin',
                    'Apres-midi' => 'apres-midi',
                ]])
            ->add('reason', ChoiceType::class, [
                    'choices' => Conge::REASON,
                    'label' => 'Motif'
                ]
            );
    }
}