<?php

namespace App\Form;

use App\Entity\Company;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CompanyType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name')
            ->add('projectEnabled', ChoiceType::class, [
                'label' => 'Accessible depuis un projet',
                'choices' =>['oui' => true, 'non' => false],
                'placeholder' => ''
            ])
            ->add('userEnabled', ChoiceType::class, [
                'label' => 'Accessible depuis un utilisateur',
                'choices' =>['oui' => true, 'non' => false],
                'placeholder' => ''
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Company::class,
        ]);
    }
}