<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Company;
use App\Entity\Project;
use App\Entity\TypeInter;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\CompanyRepository;
use App\Repository\ProjectRepository;
use App\Repository\TypeInterRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class RechercheType extends AbstractType
{
    const EXCLUDED_VALUE_QUERY = ['from_project'];

    const GROUPED_BY_TICKET = 'groupedByTicket';
    const GROUPED_BY_USER = 'groupedByUser';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $submitedData = $this->fromProject($options) ? [] : $options['submitedData'];
        $queryBuilderFilterModifier = $this->getQueryBuilderFilterModifier();

        $builder
            ->add('refMantis', TextType::class, [
                'required' => false,
                'label' => 'Référence ticket'
            ])
            ->add('user', EntityType::class, [
                'query_builder' => function (UserRepository $er) use ($queryBuilderFilterModifier, $submitedData) {
                    unset($submitedData['user']);
                    return $queryBuilderFilterModifier($er->createQueryBuilder('u')
                        ->leftJoin('u.tasks', 'tasks')
                        ->leftJoin('tasks.project', 'project')
                        ->select('u')
                        ->distinct()
                        ->andWhere('u.actif != :val5')
                        ->setParameter('val5', 'non')
                        ->orderBy('u.mail', 'ASC'), $submitedData);
                },
                'choice_label' => function ($user) {
                    return $user->getFirstName() . ' ' . $user->getName();
                },
                'class' => User::class,
                'required' => false,
                'multiple' => true,
                'placeholder' => ' - - Fais ton choix - -',
                'label' => 'Utilisateur',
                'data' => $options['var']['user'] ?? null
            ])
            ->add('client', EntityType::class, [
                'query_builder' => function (ClientRepository $er) use ($queryBuilderFilterModifier, $submitedData) {
                    unset($submitedData['client']);
                    return $queryBuilderFilterModifier($er->createQueryBuilder('u')
                        ->select('u')
                        ->leftJoin('u.tasks', 'tasks')
                        ->leftJoin('tasks.project', 'project')
                        ->distinct()
                        ->orderBy('u.name', 'ASC'), $submitedData);
                },
                'class' => Client::class,
                'required' => false,
                'multiple' => true,
                'choice_label' => 'name',
                'placeholder' => ' - - Fais ton choix - -',
                'data' => $options['var']['customer'] ?? null
            ])
            ->add('typeInter', EntityType::class, [
                'class' => TypeInter::class,
                'query_builder' => function (TypeInterRepository $er) use ($queryBuilderFilterModifier, $submitedData) {
                    unset($submitedData['typeInter']);
                    return $queryBuilderFilterModifier($er->createQueryBuilder('u')
                        ->leftJoin('u.tasks', 'tasks')
                        ->leftJoin('tasks.project', 'project')
                        ->orderBy('u.name', 'ASC'), $submitedData);
                },
                'required' => false,
                'choice_label' => 'name',
                'placeholder' => ' - - Fais ton choix - -',
                'label' => 'Type Intervention'
            ])
            ->add('project', EntityType::class, [
                'class' => Project::class,
                'query_builder' => function (ProjectRepository $er) use ($queryBuilderFilterModifier, $submitedData) {
                    unset($submitedData['project']);
                    return $queryBuilderFilterModifier($er->createQueryBuilder('project')
                        ->leftJoin('project.tasks', 'tasks')
                        ->orderBy('project.name', 'ASC'), $submitedData);
                },
                'required' => false,
                'multiple' => true,
                'choice_label' => 'name',
                'placeholder' => ' - - Fais ton choix - -',
                'data' => $options['var']['project'] ?? null,
                'label' => 'Projet'
            ])
            ->add('dateD', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'required' => false,
                'empty_data' => null,
                'data' => $options['var']['dateD'] ?? new \DateTime("- 30 days"),
                'label' => 'Date de début'
            ])
            ->add('dateF', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'required' => false,
                'empty_data' => null,
                'data' => $options['var']['dateF'] ?? new \DateTime("now"),
                'label' => 'Date de fin'
            ])
            ->add('company', EntityType::class, [
                'query_builder' => function (CompanyRepository $er) use ($queryBuilderFilterModifier, $submitedData) {
                    unset($submitedData['company']);
                    return $queryBuilderFilterModifier($er->createQueryBuilder('u')
                        ->leftJoin('u.projects', 'up')
                        ->leftJoin('up.tasks', 'tasks')
                        ->orderBy('up.name', 'ASC'), $submitedData);
                },
                'class' => Company::class,
                'required' => false,
                'multiple' => true,
                'choice_label' => 'name',
                'placeholder' => ' - - Fais ton choix - -',
                'data' => $options['var']['company'] ?? null,
                'label' => 'Groupe'
            ])
            ->add('from_project', HiddenType::class, [
                'data' => $this->fromProject($options),
                'mapped' => false
            ])
            ->add('print', SubmitType::class, ['label' => 'Imprimer en pdf'])
            ->add('export', SubmitType::class, ['label' => 'Exporter au format CSV'])
            ->add('exportExcel', SubmitType::class, ['label' => 'Exporter au format Excel'])
            ->add('search', SubmitType::class, ['label' => 'Rechercher'])
            ->add('groupedBy', ChoiceType::class, [
                'choices' => [

                    'Collaborateur' => self::GROUPED_BY_USER,
                    'Référence ticket' => self::GROUPED_BY_TICKET,
                ],
                'required' => false,
                'placeholder'=> '---',

                'label' => 'Regrouper par'
            ]);

    }

    /**
     * @param array $options
     * @return bool
     */
    private function fromProject(array $options)
    {
        return !empty($options['var']) || (isset($options['submitedData']['from_project']) && $options['submitedData']['from_project'] == true);
    }


    private function getQueryBuilderFilterModifier()
    {
        return function (QueryBuilder $qb, $submitedData = []) {
            if (!empty($dateD = $submitedData['dateD'] ?? (new \DateTime("- 30 days"))->format('Y-m-d'))) {
                $qb
                    ->andWhere("tasks.date >= :dateD")
                    ->setParameter('dateD', $dateD);
            }

            if (!empty($dateF = $submitedData['dateF'] ?? (new \DateTime("now"))->format('Y-m-d'))) {
                $qb->andWhere("tasks.date <= :dateF")
                    ->setParameter('dateF', $dateF . ' 23:59:59');
            }
            if (!empty($submitedData['user'] ?? NULL)) {
                $qb->andWhere(is_array($submitedData['user']) ? "tasks.user IN (:user)" : "tasks.user = :user")
                    ->setParameter('user', $submitedData['user']);
            }
            if (!empty($submitedData['client'] ?? NULL)) {
                $qb->andWhere(is_array($submitedData['client']) ? "tasks.client IN (:client)" : "tasks.client = :client")
                    ->setParameter('client', $submitedData['client']);
            }
            if (!empty($submitedData['project'] ?? NULL)) {
                $qb->andWhere(is_array($submitedData['project']) ? "tasks.project IN (:project)" : "tasks.project = :project")
                    ->setParameter('project', $submitedData['project']);
            }
            if (!empty($submitedData['company'] ?? NULL)) {
                $qb->andWhere(is_array($submitedData['company']) ? "project.company IN (:company)" : "company = :company")
//                    ->leftJoin('tasks.project', 'project')
                    ->setParameter('company', $submitedData['company']);
            }
            unset($submitedData['dateD']);
            unset($submitedData['dateF']);
            unset($submitedData['user']);
            unset($submitedData['company']);
            unset($submitedData['client']);
            unset($submitedData['project']);
            unset($submitedData['groupedBy']);
            unset($submitedData['_token']);
            foreach ($submitedData as $field => $value) {
                if (!empty($value ?? NULL) && !in_array($field, self::EXCLUDED_VALUE_QUERY)) {
                    if ($field === 'company') {
                        $qb->andWhere("project.$field = :$field")
                            ->setParameter($field, $value);
                    }else{
                        $qb->andWhere("tasks.$field = :$field")
                            ->setParameter($field, $value);
                    }
                }
            }
            return $qb;
        };
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => NULL,
            'submitedData' => [],
            'var' => []
        ]);
    }
}
