<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TypeInter;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\ProjectRepository;
use App\Repository\TypeInterRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Validator\Constraints\Length;

class TimerTaskType extends AbstractType
{
    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;
    /**
     * @var AuthorizationCheckerInterface
     */
    private $authChecker;
    /**
     * @var ProjectRepository
     */
    private $projectRepository;
    private $security;

    public function __construct(TokenStorageInterface $tokenStorage, AuthorizationCheckerInterface $authChecker, ProjectRepository $projectRepository, Security $security)
    {
        $this->tokenStorage = $tokenStorage;
        $this->authChecker = $authChecker;
        $this->projectRepository = $projectRepository;
        $this->security = $security;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('subject', TextareaType::class, [
                'label' => 'Résumé de l\'intervention',
                'attr' => ['maxlength' => 255],
                'constraints' => [
                    new Length(['max' => 255]),
                ],

            ])
            ->add('refMantis', IntegerType::class, ['label' => 'Référence ticket'])
            ->add('duration', TextType::class, [
                'label' => 'Commencer à (défault = 0)',
                'required' => false,
                'empty_data' => 0,
            ])
            ->add('client', EntityType::class, [
                'query_builder' => function (ClientRepository $clientRepository) {
                    return $clientRepository->createQueryBuilder('c')
                        ->join('c.projects','p')
                        ->join('p.userProject', 'up')
                        ->andWhere('up = :user')
                        ->setParameter('user', $this->security->getUser())
                        ->andWhere('c.actif = :val5')
                        ->setParameter('val5', 'oui')
                        ->orderBy('c.name', 'ASC');
                },
                'class' => Client::class,
                'choice_label' => 'name',
            ])
            ->add('typeInter', EntityType::class, [
                'query_builder' => function (TypeInterRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->andWhere('u.actif = :val5')
                        ->setParameter('val5', 'oui')
                        ->orderBy('u.name', 'ASC');
                },
                'class' => TypeInter::class,

                'choice_label' => 'name',
            ])
            ->add('Save', SubmitType::class);


        $builder->addEventListener(FormEvents::PRE_SET_DATA, [$this, 'formPreSetData']);
        $builder->get('client')->addEventListener(FormEvents::POST_SUBMIT, [$this, 'onPostSubmit']);
    }

    public function formPreSetData(FormEvent $event)
    {
        $form = $event->getForm();
        /** @var Task $task */
        $task = $event->getData();
        /** @var User $currentUser */
        $currentUser = $this->tokenStorage->getToken()->getUser();

        if (!$task || $task->getId() === null) {
            $task->setDate(new \DateTime())
                ->setDuration($currentUser->getDuration())
                ->setClient($currentUser->getClient())
                //->setProject($currentUser->getProject())
                ->setUser($currentUser);
        }

        $this->filtrerProjets($form, $task->getClient());
    }
    
    public function onPostSubmit(FormEvent $event)
    {
        $client = $event->getForm()->getData();
        $this->filtrerProjets($event->getForm()->getParent(), $client);
    }

    public function filtrerProjets(FormInterface $form, ?Client $client = null)
    {
        $preferedProjects = [];
        if (!empty($client)) {
            $preferedProjects = $this->projectRepository->findBy(['client' => $client]);
        }

        $projects = $this->projectRepository->getAllActive();
        
        if ($client) {
            $projects = $this->projectRepository->findUserProjectsByClient($this->security->getUser(), $client);
        }

        $form->add('project', EntityType::class, [
            'choices' => $projects,
            'class' => Project::class,
            'choice_label' => function (Project $project) {
                $label = '';
                $label .= (!empty($project->getClient()) ? sprintf('%s - ', $project->getClient()) : '');
                $label .= $project->getName();
                $label .= (!empty($project->getReference()) ? sprintf(' (%s)', $project->getReference()) : '');

                return $label;
            },
            'placeholder' => '-- Sélectionnez un projet --',
            'preferred_choices' => $preferedProjects,
            'required'=> false,
        ]);
    }
    public function finishView(FormView $view, FormInterface $form, array $options)
    {
        $childrens = $view->children;
        $projectElement = ['project' => $childrens['project']];
        unset($childrens['project']);
        $index = array_search('typeInter', array_keys($childrens));
        $view->children = array_slice($childrens, 0, $index + 1, true) + $projectElement + array_slice($childrens, $index + 1, null, true);
        parent::finishView($view, $form, $options);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Task::class,
        ]);
    }


}
