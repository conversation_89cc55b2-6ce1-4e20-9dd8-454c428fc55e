<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240813130313 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'Remove leading zeros from ref_mantis';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("UPDATE task SET ref_mantis = TRIM(LEADING '0' FROM ref_mantis) WHERE ref_mantis LIKE '0%';");
    }

    public function down(Schema $schema) : void
    {
    }
}
