<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230725134926 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'add relation many to many between tables type_inter and company';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('CREATE TABLE type_inter_company (type_inter_id INT NOT NULL, company_id INT NOT NULL, INDEX IDX_CE29AC5A25A7B762 (type_inter_id), INDEX IDX_CE29AC5A979B1AD6 (company_id), PRIMARY KEY(type_inter_id, company_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE type_inter_company ADD CONSTRAINT FK_CE29AC5A25A7B762 FOREIGN KEY (type_inter_id) REFERENCES type_inter (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE type_inter_company ADD CONSTRAINT FK_CE29AC5A979B1AD6 FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE type_inter_company');
    }
}
