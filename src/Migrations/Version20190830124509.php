<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190830124509 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE INDEX INDEX_DATE ON task (date)');
        $this->addSql('ALTER TABLE task RENAME INDEX idx_527edb25a76ed395 TO INDEX_USER');
        $this->addSql('ALTER TABLE task RENAME INDEX idx_527edb2519eb6921 TO INDEX_CLIENT');
        $this->addSql('ALTER TABLE task RENAME INDEX idx_527edb2525a7b762 TO INDEX_TYPEINTER');
        $this->addSql('ALTER TABLE task RENAME INDEX idx_527edb25166d1f9c TO INDEX_PROJECT');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX INDEX_DATE ON task');
        $this->addSql('ALTER TABLE task RENAME INDEX index_client TO IDX_527EDB2519EB6921');
        $this->addSql('ALTER TABLE task RENAME INDEX index_typeinter TO IDX_527EDB2525A7B762');
        $this->addSql('ALTER TABLE task RENAME INDEX index_user TO IDX_527EDB25A76ED395');
        $this->addSql('ALTER TABLE task RENAME INDEX index_project TO IDX_527EDB25166D1F9C');
    }
}
