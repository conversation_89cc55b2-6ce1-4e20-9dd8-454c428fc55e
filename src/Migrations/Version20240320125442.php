<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240320125442 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the "projects_observers" table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE projects_observers (project_id INT NOT NULL, user_id INT NOT NULL, INDEX IDX_FC4710AB166D1F9C (project_id), INDEX IDX_FC4710ABA76ED395 (user_id), PRIMARY KEY(project_id, user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE projects_observers ADD CONSTRAINT FK_FC4710AB166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE projects_observers ADD CONSTRAINT FK_FC4710ABA76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE projects_observers');
    }
}
