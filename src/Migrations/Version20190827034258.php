<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190827034258 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('UPDATE project set actif =1 where actif = \'oui\' ');
        $this->addSql('UPDATE project set actif =0 where actif = \'non\' ');
        $this->addSql('ALTER TABLE project CHANGE actif actif SMALLINT NOT NULL');

        $this->addSql('ALTER TABLE project ADD manager_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EE783E3463 FOREIGN KEY (manager_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_2FB3D0EE783E3463 ON project (manager_id)');

        $this->addSql('ALTER TABLE project ADD type SMALLINT DEFAULT NULL');
        $this->addSql('ALTER TABLE project ADD date_recette DATE DEFAULT NULL, ADD date_production DATE DEFAULT NULL');

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE project CHANGE actif actif VARCHAR(255) NOT NULL COLLATE utf8mb4_unicode_ci');

        $this->addSql('ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EE783E3463');
        $this->addSql('DROP INDEX IDX_2FB3D0EE783E3463 ON project');
        $this->addSql('ALTER TABLE project DROP manager_id');

        $this->addSql('ALTER TABLE project DROP type');
        $this->addSql('ALTER TABLE project DROP date_recette, DROP date_production');

    }
}
