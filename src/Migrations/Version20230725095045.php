<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230725095045 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'make relation between tables user and company + add field projectEnable and userEnabled into company table';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE company ADD project_enabled TINYINT(1) NOT NULL, ADD user_enabled TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE user ADD company_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D649979B1AD6 FOREIGN KEY (company_id) REFERENCES company (id)');
        $this->addSql('CREATE INDEX IDX_8D93D649979B1AD6 ON user (company_id)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE company DROP project_enabled, DROP user_enabled');
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D649979B1AD6');
        $this->addSql('DROP INDEX IDX_8D93D649979B1AD6 ON user');
        $this->addSql('ALTER TABLE user DROP company_id');
    }
}
