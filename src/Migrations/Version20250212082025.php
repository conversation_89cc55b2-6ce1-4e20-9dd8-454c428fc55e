<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250212082025 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'Ajout des champs de charge technique, pilotage, recette, conception et mep dans la table project';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE project ADD charge_tech NUMERIC(10, 2) DEFAULT NULL, ADD pilotage NUMERIC(5, 2) DEFAULT NULL, ADD recette NUMERIC(5, 2) DEFAULT NULL, ADD conception NUMERIC(5, 2) DEFAULT NULL, ADD mep NUMERIC(5, 2) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE project DROP charge_tech, DROP pilotage, DROP recette, DROP conception, DROP mep');
    }
}
