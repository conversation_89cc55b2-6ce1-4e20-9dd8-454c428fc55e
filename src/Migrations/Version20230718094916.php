<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230718094916 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'adding tags to users like back, front, design, manager';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE user ADD tag VARCHAR(255)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE user DROP tag');
    }
}
