<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230725115729 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'add field description into table project';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE project ADD description LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
       $this->addSql('ALTER TABLE project DROP description');

    }
}
