<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230725103334 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'add field defaultProject to project table';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE project ADD default_project TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE project DROP default_project');
    }
}
