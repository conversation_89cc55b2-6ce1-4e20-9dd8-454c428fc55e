<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240828063957 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'Change project label to $projectname - TMA';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
        UPDATE project set name = CONCAT(TRIM(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(name, '- TMA (2j/mois)',''),'- TMA (2j / mois)',''), '- TMA (0,5j/mois)',''), ' / TMA',''), 'TMA -', '' ), 'TMA', '')),' - TMA')
        WHERE name != 'TMA' AND name LIKE '%TMA%' AND name NOT LIKE '%- TMA'");
    }

    public function down(Schema $schema) : void
    {
    }
}
