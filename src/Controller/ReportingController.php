<?php

namespace App\Controller;

use App\Exception\ReportingNotFoundException;
use App\Form\ReportingType;
use App\Form\TimeCheckFormType;
use App\Repository\TaskRepository;
use App\Service\ReportingService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReportingController extends AbstractController
{
    /**
     * @Route("/reporting/", name="reporting_index")
     */
    public function index(Request $request, ReportingService $reportingService, LoggerInterface $reportingLogger): Response
    {
        $reportingDate = new \DateTimeImmutable();
        $userTasksReporting = $reportingService->getReporting($reportingDate)[$this->getUser()->getId()] ?? [];

        $form = $this->createForm(ReportingType::class, ['date' => $reportingDate]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid() && $form->get('reporting_validation')->isClicked()) {
            $selectedDate = $form->get('date')->getData();
            $reportingDate = \DateTimeImmutable::createFromMutable($selectedDate);
            $reportingText = $form->get('reporting_text')->getData();

            try {
                $reportingService->sendReportingEmail($userTasksReporting, $reportingText, $reportingDate, $this->getUser());
            } catch (ReportingNotFoundException $e) {
                $reportingLogger->info('No task has been found, the reporting email is not sent.', [
                    'user' => $this->getUser()->getId(),
                    'date' => $reportingDate->format('Y-m-d'),
                ]);
            }

            return $this->redirectToRoute('home');
        }

        try {
            $reportingRecipients = $reportingService->getReportingEmailRecipientsForView($this->getUser(), $reportingDate->setTime(0, 0));
        } catch (ReportingNotFoundException $e) {
            $reportingLogger->info('No recipient can be returned because no task has been found.', [
                'user' => $this->getUser()->getId(),
                'date' => $reportingDate->format('Y-m-d'),
            ]);

            $reportingRecipients = [];
        }

        return $this->render('reporting/index.html.twig', [
            'date' => $reportingDate,
            'taskReporting' => $userTasksReporting,
            'form' => $form->createView(),
            'reporting_recipients' => $reportingRecipients,
        ]);
    }

    /**
     * @Route("/reporting/controle-saisie", name="reporting_time_check")
     * @param ReportingService $reportingService
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    public function timeCheck(Request $request, ReportingService $reportingService, TaskRepository $taskRepository)
    {
        $reportingDate = (new \DateTimeImmutable())->sub(new \DateInterval('P1D'));

        $form = $this->createForm(TimeCheckFormType::class, [
            'startDate' => $reportingDate,
            'endDate' => $reportingDate,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $startDate = $data['startDate'];
            $endDate = $data['endDate'];

            $tasksReporting = $reportingService->getReportingBetweenDates($startDate, $endDate);
            $usersWithoutTasks = $reportingService->getUsersWithoutTasksOrConges($startDate, $endDate);

            return $this->render('reporting/timeCheck.html.twig', [
                'startDate' => $startDate,
                'endDate' => $endDate,
                'taskReporting' => $tasksReporting,
                'usersWithoutTasks' => $usersWithoutTasks,
                'form' => $form->createView(),
            ]);
        }

        $tasksReporting = $reportingService->getReportingBetweenDates($reportingDate, $reportingDate);
        $usersWithoutTasks = $reportingService->getUsersWithoutTasksOrConges($reportingDate, $reportingDate);

        return $this->render('reporting/timeCheck.html.twig', [
            'date' => $reportingDate,
            'taskReporting' => $tasksReporting,
            'usersWithoutTasks' => $usersWithoutTasks,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/reporting/export-csv", name="reporting_export_csv")
     */
    public function exportCsv(Request $request, ReportingService $reportingService): Response
    {
        $startDate = $request->query->get('startDate');
        $endDate = $request->query->get('endDate');

        // Convertir les dates en objets DateTimeImmutable
        $startDate = $startDate ? new \DateTimeImmutable($startDate) : null;
        $endDate = $endDate ? new \DateTimeImmutable($endDate) : null;

        // Récupérer les données pour l'export
        $tasksByUser = $reportingService->getReportingBetweenDates($startDate, $endDate);

        // Transformer les données pour inclure la clé "username"
        $tasks = [];
        foreach ($tasksByUser as $user) {
            foreach ($user['tasks'] as $task) {
                $tasks[] = [
                    'username' => $user['firstName'] . ' ' . $user['name'],
                    'matricule' => $user['id'],
                    'client' => $task['client']['name'],
                    'project' => $task['project']['name'],
                    'type' => $task['typeInter']['name'],
                    'duration' => $task['duration'],
                ];
            }
        }

        // Créer le tableau pour l'export
        $tasksArray = $reportingService->createTasksArrayForAlexisExport($startDate ?? $endDate, $tasks);

        // Générer le fichier CSV
        return $reportingService->createCsvAlexis($tasksArray, ($startDate ?? $endDate)->format('Y-m-d'));
    }
}
