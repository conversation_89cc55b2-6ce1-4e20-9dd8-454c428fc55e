<?php

namespace App\Controller;

use App\Client\MantisApiClient;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends AbstractController
{
    /**
     * @Route("/dashboard")
     */
    public function mantis(MantisApiClient $mantisApiClient, int $projectId = null)
    {
        if (null === $this->getUser()->getMantisApiKey()) {
            $this->addFlash('danger', 'Tu n\'a pas renseigné de jeton d\'API Mantis dans ton profil');
            return $this->redirectToRoute('home');
        }

        $projects = $mantisApiClient->getAllProjects();

        $sortedIssues = [];
        $sortProjects = [];

        $alreadyListed = [];
        foreach ($projects['projects'] as $project) {
            if (false === array_search($project['id'], $alreadyListed)) {
                $sortProjects[$project['id']] = $project;
                $alreadyListed[] = $project['id'];

                foreach ($project['subProjects'] ?? [] as $subProject) {
                    $alreadyListed[] = $subProject['id'];
                }
            }
        }

        if (null !== $projectId) {
            $issues = $mantisApiClient->getIssueByProject($projectId);

            foreach ($issues['issues'] as $issue) {
                $sortedIssues[$issue['status']['name']][] = $issue;
            }

        }

        return $this->render('dashboard/mantis.html.twig', [
            'projects' => $sortProjects,
            'issues' => $sortedIssues,
            'projectId' => $projectId
        ]);
    }

    /**
     * @Route("/update_assignments", name="update_assignments")
     */
    public function update_assignments(KernelInterface $kernel) {
        $application = new Application($kernel);
        $application->setAutoExit(false);

        $input = new ArrayInput([
            'command' => 'app:update-assignments',
        ]);
        $output = new BufferedOutput();

        $application->run($input, $output);

        return new Response($output->fetch());
    }
}