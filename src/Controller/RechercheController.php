<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Form\ListProjectType;
use App\Form\RechercheType;
use App\Repository\TaskRepository;
use App\Service\ExportExcel;
use App\Service\ExportSuiviTempsPdfService;
use App\Service\ExportSuiviTempsCsvService;
use App\Service\ProjectService;
use DateTimeInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use function is_iterable;

class RechercheController extends AbstractController
{
    /**
     * @Route("/recherche", name="recherche")
     * @throws \Exception
     */
    public function index(Request $request,
                          TaskRepository $taskRepository,
                          ExportSuiviTempsPdfService $exportSuiviTempsPdfService,
                          ExportSuiviTempsCsvService $exportSuiviTempsCsvService,
                          ProjectService $projectService,
                          ExportExcel  $exportExcel
    ): Response
    {
        $submitedData = $request->request->all()['recherche'] ?? [];
        $options = null;
        $projectForm = null;
        $dates = null;
        $tasks = [];

        if ($request->query->get('idProject') !== null && $request->query->get('idUser') !== null) {
            $em = $this->getDoctrine()->getManager();

            $project = $em->getRepository(Project::class)->findOneBy(['id' => $request->query->get('idProject')]);
            $idUser = $request->query->get('idUser');

            $tasks = $em->getRepository(Task::class)->getTaskByProject($project,$project->getClient(),$idUser);
            $user = $em->getRepository(User::class)->findOneBy(['id' => $idUser]);
            $dates = $projectService->getDatesDandF($tasks);

            $options = ['dateD' => $dates[0] , 'dateF' => $dates[1], 'project' => [$project],'customer' => [$project->getClient()],'user' => [$user]];

            $projects = $em->getRepository(Project::class)->findBy(['actif'=>[Project::STATE_ACTIVE,Project::STATE_STANDBY]]);

            if ($this->isGranted('ROLE_ADMIN')) {
                $projects = $em->getRepository(Project::class)->findAll();
            }

            $projectForm = $this->createForm(ListProjectType::class,NULL,['projects' => $projects ])->createView();
        }
        $form = $this->createForm(RechercheType::class, NULL, ['submitedData' => $submitedData,'var' => $options]);
        $form->handleRequest($request);
        if ($form->isSubmitted() ) {
            $filters = array_filter($form->getData());
            unset($filters['groupedBy']);
            if (isset($form->getData()['refMantis']) && $form->getData()['refMantis'] == "0") {
                $filters['refMantis'] = $form->getData()['refMantis'];
            }
            $message = '';
            foreach ($filters as $key => $value) {
                if (is_iterable($value)) {
                    if (count($value) === 0 ){
                        continue;
                    }
                    $value =  join(', ', $value->toArray());
                }elseif($value instanceof DateTimeInterface){
                    $value = $value->format('Y-m-d');
                }else{
                    $message =  $value;
                }
                $message .= $key . ': ' .$value. ' / ';
            }
            $message = trim($message," \t\n\r\0\x0B/");

            $cumulativeSearch = $form->get('groupedBy')->getData();
            if (!$request->isXmlHttpRequest()) {
                $tasks = $taskRepository->findByMultiplFields($filters, $cumulativeSearch);
            }

            if ($cumulativeSearch ) {
                $tasks = $this->cumulateEntries($tasks,$cumulativeSearch);
            }

            if ($form->get('print')->isClicked() && !$request->isXmlHttpRequest()){
                return $exportSuiviTempsPdfService->generatePdf($tasks,$filters, $cumulativeSearch);
            }
            if ($form->get('export')->isClicked() && !$request->isXmlHttpRequest()
            ) {
                return $exportSuiviTempsCsvService->generateCsv($tasks,$cumulativeSearch);
            }
            if ($form->get('exportExcel')->isClicked() && !$request->isXmlHttpRequest()
            ) {
                $fileName = $exportExcel->export($tasks, $cumulativeSearch);
                return $this->file($fileName)->deleteFileAfterSend();
            }
            
            else{
                if ($dates === null) {
                    $dates = [$form->get('dateD')->getData(),$form->get('dateF')->getData()];
                }

                return $this->render('recherche/index.html.twig', [
                    'form' => $form->createView(),
                    'msg' => $message,
                    'tasks' =>  $tasks,
                    'projectForm' => $projectForm,
                    'dates' => $dates,
                    'cumulativeSearch' => $cumulativeSearch ,
                ]);
            }
        } else {
            if ($dates === null) {
                $dates = [$form->get('dateD')->getData(),$form->get('dateF')->getData()];
            }

            return $this->render('recherche/index.html.twig', [
                'form' => $form->createView(),
                'tasks' => $tasks,
                'msg' => 'Entrez votre recherche',
                'projectForm' => $projectForm,
                'dates' => $dates,
            ]);
        }
    }

    private function cumulateEntries(array $entries, ?string $cumulativeSearch = null): array {
        if (empty($cumulativeSearch)){
            return $entries;
        }

        $result = [];

        switch ($cumulativeSearch){
            case RechercheType::GROUPED_BY_USER:
                $key = 'user_fullname';
                break;
            case RechercheType::GROUPED_BY_TICKET:
                $key = 'refMantis';
                break;
            default:
                return $entries;
        }

        foreach ($entries as $entry) {

            if (empty($entry['refMantis'])) {
                $entry['refMantis'] = 'Autres';
            }

            $groupKey = ltrim($entry[$key], "0");

            $result[$groupKey]['resume']['duration'] = ((float)$entry['duration']) + ($result[$groupKey]['resume']['duration'] ?? 0.0);
            $result[$groupKey]['resume']['users'][$entry['user_id']] = $entry['user_firstname'] . ' ' . $entry['user_name'];
            $result[$groupKey]['details'][] = $entry;

            if (!isset($result[$groupKey]['resume']['date_from']) || $entry['date'] < $result[$groupKey]['resume']['date_from']) {
                $result[$groupKey]['resume']['date_from'] = $entry['date'];
            }
            if (!isset($result[$groupKey]['resume']['date_to']) || $entry['date'] > $result[$groupKey]['resume']['date_to']) {
                $result[$groupKey]['resume']['date_to'] = $entry['date'];
            }
        }

        return $result;
    }

}
