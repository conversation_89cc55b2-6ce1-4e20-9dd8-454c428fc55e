<?php

namespace App\Controller\Api;

use App\Repository\ClientRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class ClientController extends AbstractController
{
    /**
    * @Route("/api/clients", name="api_clients", methods={"GET"})
    */
    public function getClients(ClientRepository $clientRepository): JsonResponse
    {
        $clients = $clientRepository->getAllClientsForApi();
        $responseData = [];

        foreach ($clients as $client) {
            $activeStatus = ($client['actif'] == 'oui') ? 1 : 0;

            $responseData[] = [
                'id' => $client['id'],
                'name' => $client['name'],
                'active' => $activeStatus
            ];
        }

        return $this->json($responseData);
    }
}
