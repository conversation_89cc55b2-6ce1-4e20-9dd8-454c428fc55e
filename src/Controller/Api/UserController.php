<?php

namespace App\Controller\Api;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class UserController extends AbstractController
{
    /**
    * @Route("/api/users", name="api_users", methods={"GET"})
    */
    public function getUsers(UserRepository $userRepository): JsonResponse
    {
        $users = $userRepository->getAllUsersForApi();
        $responseData = [];

        foreach ($users as $user) {
            $activeStatus = $user['actif'] == 'oui';

            $responseData[] = [
                'id' => $user['id'],
                'mail' => $user['mail'],
                'username' => $user['username'],
                'googleId' => $user['googleId'],
                'tag' => $user['tag'],
                'active' => $activeStatus,
                'onSite' => $user['onSite'],
                'company' => $user['company']
            ];
        }

        return $this->json($responseData);
    }
}
