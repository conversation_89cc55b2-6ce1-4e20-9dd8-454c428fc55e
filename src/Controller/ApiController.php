<?php

namespace App\Controller;

use App\Service\ApiManagerService;
use App\Service\TaskManagerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;

class ApiController extends AbstractController
{
    /**
     * @Route(name="api_login", path="/api/login_check")
     * @return JsonResponse
     */
    public function login(): JsonResponse
    {
        $user = $this->getUser();

        return $this->json([
            'username' => $user->getUsername(),
            'roles' => $user->getRoles()
        ]);
    }

    public function insert(Request $request, TaskManagerService $taskManagerService): JsonResponse
    {

        // Récupérer la requête Ajax sous forme de tableau
        $requestAjax = json_decode($request->getContent(), true);
        if (TRUE !== ($error = $taskManagerService->checkAjaxRequestIsAllowed($requestAjax))){
            return $this->json(['jsonResponse' => $error],Response::HTTP_BAD_REQUEST);
        }
        $taskManagerService->createTaskFromAjaxRequest($requestAjax);

        return $this->json(['jsonResponse' => 'successChronoTask']);
    }

    /**
     * API for retrieve average time per tickets
     *
     * @param Request $request
     * @param ApiManagerService $apiManagerService
     * @param TaskManagerService $taskManagerService
     * @return JsonResponse
     */
    public function average(Request $request, ApiManagerService $apiManagerService, TaskManagerService $taskManagerService): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (null === $data || !$apiManagerService->checkAverageApiParameters($data)) {
            return $apiManagerService->buildResponse(['message' => 'Not all required parameters are sent'], 400);
        }

        $tickets = explode(';', $data['tickets']);
        $startDate = $data['start_date'] ?? null;
        $endDate = $data['end_date'] ?? null;
        $average = $taskManagerService->getAverageTime($tickets, $startDate, $endDate);

        if (null === $average) {
            return $apiManagerService->buildResponse(['message' => 'No ticket found'], 404);
        }

        return $apiManagerService->buildResponse($average, 200);
    }
}
