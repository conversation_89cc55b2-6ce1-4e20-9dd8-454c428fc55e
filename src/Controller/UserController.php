<?php

namespace App\Controller;

use App\Entity\Company;
use App\Entity\User;
use App\Entity\Client;
use App\Form\UserType;
use App\Entity\Project;
use App\Entity\TypeInter;
use App\Repository\CompanyRepository;
use App\Repository\TaskRepository;
use App\Repository\UserRepository;
use App\Repository\ClientRepository;
use App\Repository\ProjectRepository;
use App\Repository\TypeInterRepository;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\SerializerInterface;

class UserController extends AbstractController
{
    /**
     * @Route("/user", name="user_index", methods={"GET"})
     */
    public function index(UserRepository $userRepository, Request $request, CompanyRepository $companyRepository): Response
    {
        $activity = (int)$request->get('activity', User::FILTER_ACTIVE);
        $companies = $companyRepository->findByUserEnabled(true);
        $companiesName = array_map(function (Company $company) {
            return $company->getName();
        }, $companies);

        $companyName = $request->get('company', User::COMPANY_ALL);

        if (!array_key_exists($activity, User::FILTERS_SELECTOR) || !in_array($companyName, array_merge([User::COMPANY_ALL], $companiesName))) {
            $this->addFlash('error', 'erreur dans la requête');
            return $this->redirectToRoute('user_index');
        }

        return $this->render('user/index.html.twig', [
            'users' => $userRepository->findByActifAndCompanyAndOnSite($activity, $companyName),
            'companies' => $companies,
            'filter' => [
                'activity' => $activity,
                'company' => $companyName,
            ],
        ]);
    }

    /**
     * @Route("/user-actif/list/{active}", requirements={"active": "1|0"}, defaults={"active": "1"}, name="user_actif_list", methods={"GET"})
     * @IsGranted("ROLE_SUPER_ADMIN")
     */
    public function exportActiveList(bool $active, UserRepository $userRepository, SerializerInterface $serializer): Response
    {
        $csvData = $userRepository->findByActive($active);

        //manage data roles
        array_walk($csvData, function (&$data) {
            $data['roles'] = is_array($data['roles']) ? implode('; ', $data['roles']) : null;
        });

        $response = new Response($serializer->serialize($csvData, CsvEncoder::FORMAT, [CsvEncoder::DELIMITER_KEY => ',']));
        $response->headers->set('Content-Type', 'text/csv');
        $filename = $active ? 'actif' : 'inactif';
        $response->headers->set('Content-Disposition', 'inline; filename="user_'.$filename.'.csv"');

        return $response;
    }

    /**
     * @Route("/admin/user/new", name="user_new", methods={"GET","POST"})
     */
    public function new(Request $request, UserPasswordEncoderInterface $encoder,AuthorizationCheckerInterface $authChecker): Response
    {
        $user = new User();
        $form = $this->createForm(UserType::class, $user,[
            'is_admin'=> $authChecker->isGranted('ROLE_ADMIN'),
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager = $this->getDoctrine()->getManager();
            $encoded = $encoder->encodePassword($user, $user->getPassword());
            $user->setPassword($encoded);
            $user->setActif('oui');
            $entityManager->persist($user);
            $entityManager->flush();

            return $this->redirectToRoute('user_index');
        }

        return $this->render('user/new.html.twig', [
            'user' => $user,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/user/{id}", name="user_show", methods={"GET"})
     */
    public function show(User $user): Response
    {
        return $this->render('user/show.html.twig', [
            'user' => $user,
        ]);
    }

    /**
     * @Route("/user/{id}/edit", name="user_edit", methods={"GET","POST"})
     */
    public function edit(Request $request, User $user, UserPasswordEncoderInterface $encoder,AuthorizationCheckerInterface $authChecker): Response
    {
        $form = $this->createForm(UserType::class, $user,[
            'is_admin'=> $authChecker->isGranted('ROLE_ADMIN'),
        ]);

        $form->add('passwd', PasswordType::class, [
            'required' => false,
            'label' => 'Mot de passe'
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager = $this->getDoctrine()->getManager();

            if ( $form->get('passwd')->getData() != '' ) {
                $encoded = $encoder->encodePassword($user, $user->getPasswd());
                $user->setPassword($encoded);
            }

            $entityManager->persist($user);
            $entityManager->flush();

            return $this->redirectToRoute('user_index', [
              'id' => $user->getId(),
            ]);
        }

        if ( $user->getMail() == $this->getUser()->getMail() || $this->isGranted('ROLE_ADMIN'))
        {
            return $this->render('user/edit.html.twig', [
                'user' => $user,
                'form' => $form->createView(),
            ]);
        }
        else
        {
            return $this->render('user/show.html.twig', [
                'user' => $user,
                'form' => $form->createView(),
            ]);
        }

    }

    /**
     * @Route("/admin/user/{id}", name="user_delete", methods={"DELETE"})
     */
    public function delete(Request $request, User $user): Response
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            if ($this->isCsrfTokenValid('delete'.$user->getId(), $request->request->get('_token'))) {
                $entityManager = $this->getDoctrine()->getManager();
                $entityManager->remove($user);
                $entityManager->flush();
            }
        }
        return $this->redirectToRoute('user_index');
    }

    /**
     * @Route("/user/export/{name}", name="user_export_task", methods={"get"})
     */
    public function exportActiveUser(string $name, KernelInterface $kernel): Response
    {
        $fileName = $name . '.csv';
        $filePath = $kernel->getProjectDir() . '/var/export/' . $fileName;
        if (!preg_match('/^saisie_temps_\d{8}$/', $name) || !file_exists($filePath)) {
            $this->addFlash('error', 'Le fichier demandé n\'est pas valide');
            return $this->redirectToRoute('home');
        }

        $response = new BinaryFileResponse($filePath);
        $response->headers->set('Content-Type', 'text/csv');
        return $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, $fileName);
    }
}
