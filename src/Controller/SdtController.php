<?php

namespace App\Controller;

use App\Entity\Task;
use App\Entity\User;
use App\Form\TimerTaskType;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Service\TaskManagerService;

class SdtController extends AbstractController
{
    /**
     * @Route("/", name="home")
     */
    public function index(Request $request, TaskManagerService $taskManagerService): Response
    {
        // Instancier une nouvelle tâche
        $task = new Task();
        $form = $this->createForm(TimerTaskType::class, $task);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $task->setCreatedAt(new \DateTime());
            if (empty($task->getUser())) {
                $task->setUser($this->getUser());
            }
            $this->getDoctrine()->getManager()->persist($task);
            $this->getDoctrine()->getManager()->flush();
            $this->addFlash('success', 'Sauvegarde effectuée avec succés');
            return $this->redirectToRoute('task_index');
        }

        // Récupérer l'utilisateur connecté et son nom de famille
        $user = $this->getUser();
        $assignment = null;
        if ($user) {
            $userName = $user->getName();
            // make susername upper
            $userName = strtoupper($userName);
            // Récupérer son assignment dans DIR ../../assignments.json, dans assignments puis son nom de famille
            $assignmentsFile = __DIR__ . '/../../assignments.json';
            if (file_exists($assignmentsFile)) {
                dump(true);
                $assignments = json_decode(file_get_contents($assignmentsFile), true);
                if (isset($assignments["assignments"][$userName])) {
                    $assignment = $assignments["assignments"][$userName];
                }
            }
        }


        return $this->render('sdt/index.html.twig', [
            'form' => $form->createView(),
            'assignment' => $assignment
        ]);
    }
}