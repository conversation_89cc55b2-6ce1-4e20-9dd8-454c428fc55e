<?php

namespace App\Controller;

use App\Repository\UserRepository;
use App\Service\UserManagerService;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends AbstractController
{
    /**
     * @Route("/connexion-google", name="security_login_google")
     */
    public function loginGoogle(ClientRegistry $clientRegistry)
    {
        return $clientRegistry->getClient('gmail_itroom')
            ->redirect(['email']);
    }

    /**
     * @param Request $request
     * @param ClientRegistry $clientRegistry
     *
     * @Route("/connexion-google/check", name="security_login_google_check")
     */
    public function connectLoginGoogleCheck(Request $request, ClientRegistry $clientRegistry)
    {
        $client = $clientRegistry->getClient('gmail_itroom');

        try {
            // the exact class depends on which provider you're using
            /** @var \League\OAuth2\Client\Provider\GoogleUser $user */
            $user = $client->fetchUser();
            // do something with all this new power!
            // e.g. $name = $user->getFirstName();
            $this->addFlash('success', 'Vous êtes identifié !');
            // ...
        } catch (IdentityProviderException $e) {
            // something went wrong!
            // probably you should return the reason to the user
            $this->addFlash('error', $e->getMessage());
        }
    }

    /**
     * @Route("/connexion", name="security_login")
     */
    public function login(AuthenticationUtils $authenticationUtils)
    {
        $error = $authenticationUtils->getLastAuthenticationError();
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render('security/index.html.twig', [
            'last_username' => $lastUsername,
            'error'         => $error,
        ]);
    }
    /**
     * @Route("/logout", name="security_logout")
     */
    public function logout() 
    {
        
    }

    /**
     * @Route("/reset-password", name="security_reset_password")
     */
    public function resetPassword(UserRepository $userRepository, Request $request, UserManagerService $userManagerService){
        $form = $this->createFormBuilder()
            ->add('email', EmailType::class)
            ->add('submit',SubmitType::class, ['attr'=>['class'=>'btn btn-primary'],'label'=> 'Envoyer'])
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()){
            if (!empty($user = $userRepository->findOneBy(['mail'=> $form->get('email')->getData()]))){
                $userManagerService->resetPassword($user);
                $this->addFlash('success','Votre nouveau mot de passe est envoyé par mail.');
                return $this->redirectToRoute('security_login');
            }else{
                $this->addFlash('error','Utilisateur non trouvé.');
            }
        }

        return $this->render('security/reset-password.html.twig',[
            'form'=> $form->createView(),
        ]);
    }
}
