<?php

namespace App\Controller;

use App\Entity\Task;
use App\Form\CalendarFilterType;
use App\Repository\TaskRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class CalendarController extends AbstractController
{
    /**
     * @Route("/calendar", name="calendar")
     */
    public function index(Request $request)
    {
        $idUser = $request->get('id_user');
        $form = $this->createForm(CalendarFilterType::class,null ,['id_user' => $idUser]);

        return $this->render('calendar/index.html.twig', [
            'controller_name' => 'CalendarController',
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/calendar-api", name="calendar_api")
     * @param Request $request
     * @param TaskRepository $taskRepository
     * @return JsonResponse
     */
    public function api(Request $request, TaskRepository $taskRepository)
    {
        $month = $request->get('month') + 1;
        $year = $request->get('year');
        $userId = $request->get('user_id');

        $tasks = $taskRepository->getEventsCalendar($userId, $month, $year);
        $taskCalendar = [];
        /** @var Task $task */
        foreach ($tasks as $task) {
            $taskCalendar[] = [
                'title' =>  sprintf('[%s][%s] - %s ( %s h )', $task['client_name'] ,$task['type_inter'], $task['subject'], $task['duration'] ),
                'start' => $task['date'],
                'duration' => $task['duration'],
                'task_id' => $task['id'],
                'ticket_id' => $task['refMantis'],
//              'client' => $task['client_name'],
            ];
        }
        return $this->json($taskCalendar);
    }
}
