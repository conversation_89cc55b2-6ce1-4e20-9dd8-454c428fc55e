<?php

namespace App\Controller;

use App\Client\MantisApiClient;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/mantis")
 */
class MantisController extends AbstractController
{
    /**
     * @Route("/create-issue", methods={"POST"})
     *
     * @param Request         $request
     * @param MantisApiClient $mantisApiClient
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function createIssue(Request $request, MantisApiClient $mantisApiClient)
    {
        $issue = $request->request->get('issue');

        $mantisApiClient->createMinimalIssue($issue);

        return $this->redirectToRoute('project_view', ['projectId' => $issue['project']]);
    }

    /**
     * @Route("/drop-issue")
     *
     * @param Request         $request
     * @param MantisApiClient $mantisApiClient
     *
     * @return JsonResponse
     */
    public function dropIssue(Request $request, MantisApiClient $mantisApiClient)
    {
        return new JsonResponse(
            $mantisApiClient->updateMinimalIssue(
                $request->query->get('issueId'),
                $request->query->get('status')
            )
        );
    }
}