<?php

namespace App\Controller;

use App\Entity\Company;
use App\Entity\Project;
use App\Entity\Task;
use App\Form\ListProjectType;
use App\Form\ProjectType;
use App\Repository\ProjectRepository;
use App\Service\ProjectService;
use App\Service\TaskManagerService;
use Doctrine\ORM\EntityManagerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\SerializerInterface;

class ProjectController extends AbstractController
{
    /**
     * @Route("/projects", name="project_index", methods={"GET"})
     *
     * @param ProjectRepository $projectRepository
     * @param Request $request
     *
     * @return Response
     */
    public function index(ProjectRepository $projectRepository, Request $request): Response
    {
        $state = (int)$request->get('state',Project::STATE_ACTIVE);
        $type = (int)$request->get('type', Company::IT_ROOM);

        $projects = $projectRepository->getProjectsByStateAndType($state,$type);

        return $this->render('project/index.html.twig', [
            'projects' => $projects,
            'filter' => ['state' => $state, 'type' => $type]
        ]);
    }

    /**
     * @Route("/project-actif", name="project_actif_index", methods={"GET"})
     */
    public function indexActif(ProjectRepository $projectRepository): Response
    {
        return $this->render('project/index.html.twig', [
            'projects' => $projectRepository->getAdminList(['actif'=>true]),
            'filter' => ['state' => 1, 'type' => 1]
        ]);
    }

    /**
     * @Route("/project/new", name="project_new", methods={"GET","POST"})
     * @IsGranted("ROLE_PROJECT_MANAGER")
     */
    public function new(Request $request): Response
    {
        $project = new Project();
        $form = $this->createForm(ProjectType::class, $project);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager = $this->getDoctrine()->getManager();
            $project->setActif(Project::STATE_ACTIVE);
            $entityManager->persist($project);
            $entityManager->flush();
            $this->addFlash('success', 'Projet créé avec succés');
            return $this->redirectToRoute('project_index');
        }

        return $this->render('project/new.html.twig', [
            'project' => $project,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/project/description", name="project_description", methods={"GET"})
     */
    public function description(Request $request, ProjectRepository $projectRepository): Response
    {
        $projectDescription = $projectRepository->find($request->get('id'))->getDescription() ?? 'Pas de description';
        return $this->json(['description' => 'Description : ' . $projectDescription]);
    }

    /**
     * @Route("/project/{id}", name="project_show", methods={"GET"})
     */
    public function show(Project $project, ProjectService $projectService): Response
    {
        $projects = $this->getDoctrine()->getManager()->getRepository(Project::class)->findBy(['actif'=>[Project::STATE_ACTIVE,Project::STATE_STANDBY]]);

        if ($this->isGranted('ROLE_ADMIN')) {
            $projects = $this->getDoctrine()->getManager()->getRepository(Project::class)->findAll();
        }
        
        $projectForm = $this->createForm(ListProjectType::class,NULL,['projects' => $projects ])->createView();
        
        $authorizedUser = $this->getDoctrine()->getManager()->getRepository(Project::class)->getAuthorizedUser($project->getId());
        
        $interventionsByType = $projectService->getInterventionsByType($project->getId());
        $interventionsByType = $projectService->calculatePourcentageConsommeByType($interventionsByType);
        
        return $this->render('project/show.html.twig', [
            'project' => $project,
            'statitics' => $projectService->getStatistics($project->getId()),
            'projectForm' => $projectForm,
            'userAuthorizeds' => $authorizedUser->getUserProject(),
            'interventionsByType' => $interventionsByType,
        ]);
    }

    /**
     * @Route("/admin/project/{id}/edit", name="project_edit", methods={"GET","POST"})
     * @IsGranted("ROLE_PROJECT_MANAGER")
     */
    public function edit(Request $request, Project $project): Response
    {
        $form = $this->createForm(ProjectType::class, $project);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->getDoctrine()->getManager()->flush();

            $this->addFlash('success', 'Projet modifié avec succés');
            return $this->redirectToRoute('project_index', [
                'id' => $project->getId(),
            ]);
        }

        return $this->render('project/edit.html.twig', [
            'project' => $project,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/project/{id}", name="project_delete", methods={"DELETE"})
     * @IsGranted("ROLE_PROJECT_MANAGER")
     */
    public function delete(Request $request, Project $project): Response
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            if ($this->isCsrfTokenValid('delete'.$project->getId(), $request->request->get('_token'))) {
                if ($project->getTasks()->getValues() != [] ) {
                    $this->addFlash('warning', 'Impossible de supprimer le projet car il a des taches assignées');
                    return $this->redirectToRoute('project_show',[
                        'id' => $project->getId(),
                    ]);
                }
                $entityManager = $this->getDoctrine()->getManager();
                $entityManager->remove($project);
                $entityManager->flush();
            }
        }

        return $this->redirectToRoute('project_index');
    }

    /**
     * @Route("project/ref/ordre", name="ref_ordre")
     */
    public function indexByRef(ProjectRepository $projectRepository)
    {
        $projects = $projectRepository->getAdminList();

        return $this->render('project/index.html.twig',[
            'projects' => $projects,
            'filter' => ['state' => 1, 'type' => 1]
        ]);
    }

    /**
     * @Route("/project/list/allow-user", name="project_list_allow_user", methods={"GET"})
     * @IsGranted("ROLE_SUPER_ADMIN")
     */
    public function exportAllowUserList(ProjectRepository $projectRepository, SerializerInterface $serializer): Response
    {
        $csvData = $projectRepository->getProjectsAllowUsers();
        $response = new Response($serializer->serialize($csvData, CsvEncoder::FORMAT, [CsvEncoder::DELIMITER_KEY => ',']));
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'inline; filename="utilisateurs_autorisés.csv"');

        return $response;
    }
}
