<?php

namespace App\Controller;

use App\Service\SuiviTMAService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SuiviTMAController extends AbstractController
{
    /**
     * @Route("/suivi-tma", name="suivi_tma")
     * @IsGranted("ROLE_ADMIN")
     */
    public function index(SuiviTMAService $suiviTMAService): Response
    {
        return $this->render('suivi_tma/index.html.twig', [
            'controller_name' => 'SuiviTMAController',
            'suivi_tma' => $suiviTMAService->getSuiviTMA(),
        ]);
    }
}
